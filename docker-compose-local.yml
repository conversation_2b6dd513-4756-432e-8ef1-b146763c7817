services:
   app-agent:
     build:
       context: .
       dockerfile: Dockerfile

     environment:
       - ENV=development
       - LIVEKIT_URL=${LIVEKIT_URL}
       - LIVEKIT_API_KEY=${LIVEKIT_API_KEY}
       - LIVEKIT_API_SECRET=${LIVEKIT_API_SECRET}
       - ELEVENLABS_API_KEY=${ELEVENLABS_API_KEY}
       - LANGFUSE_SECRET_KEY=${LANGFUSE_SECRET_KEY}
       - LANGFUSE_PUBLIC_KEY=${LANGFUSE_PUBLIC_KEY}
       - LANGFUSE_HOST=${LANGFUSE_HOST}
       - DEEPGRAM_API_KEY=${DEEPGRAM_API_KEY}
       - OPENAI_API_KEY=${OPENAI_API_KEY}
       - CORE_API_URL=${CORE_API_URL}
       - CORE_API_LOGIN=${CORE_API_LOGIN}
       - CORE_API_PASSWORD=${CORE_API_PASSWORD}
       - CONVERSATION_API_URL=${CONVERSATION_API_URL}
       - CONVERSATION_API_LOGIN=${CONVERSATION_API_LOGIN}
       - CONVERSATION_API_PASSWORD=${CONVERSATION_API_PASSWORD}
       - CALL_AGENT_ID=${CALL_AGENT_ID}
       - STT_PLUGIN=${STT_PLUGIN}
       - ASSEMBLYAI_API_KEY=${ASSEMBLYAI_API_KEY}
       - SENTRY_DSN=${SENTRY_DSN}
     networks:
       - call-agent
     env_file:
       - .env

  redis:
    image: redis:7.2.4-alpine
    container_name: redis-agent
    command: redis-server --requirepass call-agent
    ports:
      - '6379:6379'
    networks:
      - call-agent

  livekit-sip:
    container_name: livekit-sip-agent
    image: livekit/sip:latest
    ports:
      - '5060:5060'
      - '10000-10100:10000-10100/udp'
    environment:
      SIP_CONFIG_BODY: |
        api_key: '{LIVEKIT_API_KEY}'
        api_secret: '{LIVEKIT_API_SECRET}'
        ws_url: 'ws://livekit:7880'
        redis:
          address: 'redis:6379'
          password: 'call-agent'
        sip_port: 5060
        rtp_port: 10000-10100
        use_external_ip: true
        logging:
          level: debug
    networks:
      call-agent:


  livekit-agent:
    container_name: livekit-agent
    image: livekit/livekit-server:v1.8.0
    ports:
      - '30000-30100:30000-30100/udp'
      - '7881:7881/tcp'
      - '7880:7880'
    environment:
      LIVEKIT_KEYS: '{LIVEKIT_API_KEY}: {LIVEKIT_API_SECRET}'
      REDIS_HOST: redis:6379
      REDIS_PASSWORD: call-agent
      UDP_PORT: 30000-30100
      LIVEKIT_NODE_IP: "0.0.0.0"
      LIVEKIT_CONFIG: |
        webhook:
          api_key: {LIVEKIT_API_KEY}
          urls:
            - https://03ce-5-34-1-136.ngrok-free.app/v1/livekit/handler
        logging:
           level: debug
    networks:
      call-agent:

  freeswitch:
    container_name: freeswitch
    image: safarov/freeswitch:latest
    platform: linux/amd64
    ports:
      - '5061:5060/tcp'
      - '5061:5060/udp'
      - '5080:5080/tcp'
      - '5080:5080/udp'
      - '10200-10300:10200-10300/udp'
    volumes:
      - ./serve/freeswitch:/etc/freeswitch
      - ./serve/freeswitch/sounds:/usr/share/freeswitch/sounds:rw
    networks:
      call-agent:
    restart: unless-stopped

networks:
  call-agent:
    name: call-agent


