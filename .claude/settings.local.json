{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["WebFetch(domain:github.com)", "WebFetch(domain:raw.githubusercontent.com)", "Bash(ssh:*)", "Bash(ping:*)", "<PERSON><PERSON>(docker:*)", "<PERSON><PERSON>(echo:*)", "Bash(ls:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./deploy_freeswitch_config.sh)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(scp:*)", "<PERSON><PERSON>(sipp:*)", "<PERSON><PERSON>(timeout:*)", "Bash(nc:*)", "<PERSON><PERSON>(true)", "Bash(brew install:*)", "<PERSON><PERSON>(python:*)", "Bash(pdm install:*)", "<PERSON><PERSON>(source:*)", "Bash(pdm sync:*)", "Ba<PERSON>(pdm run:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "mcp__airtable__*", "Bash(export LIVEKIT_URL=ws://32016-51127.bacloud.info:7880)", "Bash(export LIVEKIT_API_KEY=lk_2f7c8d9a1b3e4f6g)", "Bash(export LIVEKIT_API_SECRET=lk_3h9k1m2n4p6q8r0s5t7v9w0x2z4y6a8b)", "Bash(lk create-sip-trunk:*)", "Bash(lk sip:*)", "Bash(pdm add:*)", "Bash(./test_and_monitor.sh)", "Bash(./test_888_flow.sh:*)", "<PERSON><PERSON>(pkill:*)", "Bash(ss:*)", "Bash(lk room:*)", "Bash(lk:*)", "Bash(cp:*)", "WebFetch(domain:docs.sillytavern.app)", "Bash(npx @felores/airtable-mcp:*)", "Bash(npx airtable-mcp-server:*)", "Bash(AIRTABLE_API_KEY=e4c9ad54b8e0f7993bc72e8c365b99a74ccad06f13ccf97e6ed8ba2b20719fa4 npx airtable-mcp-server --help)", "<PERSON><PERSON>(claude mcp:*)", "Bash(AIRTABLE_API_KEY=e4c9ad54b8e0f7993bc72e8c365b99a74ccad06f13ccf97e6ed8ba2b20719fa4 npx airtable-mcp-server)", "mcp__airtable__list_bases", "Bash(grep:*)", "Bash(sudo chmod:*)", "<PERSON><PERSON>(sudo mkdir:*)", "WebFetch(domain:docs.livekit.io)", "WebFetch(domain:docs.freeswitch.org)", "Bash(pip install:*)", "<PERSON><PERSON>(pdm show:*)", "Bash(pdm update:*)", "Bash(pdm lock:*)", "WebFetch(domain:signoz.io)", "<PERSON><PERSON>(cat:*)", "Bash(opentelemetry-instrument:*)"], "deny": []}, "mcpServers": {"airtable": {"command": "npx", "args": ["airtable-mcp-server"], "env": {"AIRTABLE_API_KEY": "e4c9ad54b8e0f7993bc72e8c365b99a74ccad06f13ccf97e6ed8ba2b20719fa4"}}}}