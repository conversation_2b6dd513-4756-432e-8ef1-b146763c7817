# Outbound Calling Configuration

This document describes the final configuration for dynamic outbound calling via LiveKit → FreeSWITCH → Octella.

## Overview

The system enables LiveKit agents to make outbound calls to any international phone number through the following flow:
```
LiveKit Agent → FreeSWITCH (auth: keyman) → Dynamic Number → Octella Trunk → Target Phone
```

## Configuration Files

### Core Configuration Files (Keep These)

#### 1. FreeSWITCH Configuration
- **File**: `serve/freeswitch-minimal/freeswitch.xml`
- **Purpose**: Main FreeSWITCH configuration with dynamic routing
- **Key Extensions**:
  - Extension 777: Hardcoded to +************ (backward compatibility)
  - Dynamic Pattern: `^(\+\d{10,15})$` routes to Octella with `${destination_number}`

#### 2. LiveKit Configuration Files
- **Directory**: `serve/livekit/conf/`
- **Files**:
  - `outbound-rule.json`: Dynamic outbound trunk configuration
  - `participant.json`: Template for outbound calls
  - `inbound-trunk.json`: Inbound trunk for receiving calls (extension 888)
  - `dispatch-rule.json`: Dispatch rules for inbound calls

#### 3. Management Files
- **File**: `outbound_trunk_config.json`
- **Purpose**: Tracks current trunk configuration and test results
- **Contains**: Trunk ID, auth info, test call history

#### 4. Utility Scripts
- **File**: `make_dynamic_call.py`
- **Purpose**: Python script for making dynamic outbound calls
- **Usage**: `python make_dynamic_call.py +********** [room_name] [participant_name]`

#### 5. Setup Scripts
- **File**: `setup_sip_trunk.py`
- **Purpose**: Script for creating/managing SIP trunks via LiveKit API
- **Note**: Used for initial setup, not needed for regular operations

## Current Trunk Configuration

### LiveKit Outbound Trunk
- **Trunk ID**: `ST_4C4GJw7zpNBX`
- **Name**: "FreeSWITCH Dynamic Outbound Trunk"
- **Address**: `*************:5080`
- **Pattern**: `\+\d{10,15}` (accepts international numbers)
- **Authentication**: `keyman/keyman123`

### FreeSWITCH Authentication
- **User**: `keyman`
- **Password**: `keyman123`
- **Domain**: `*************`

## Usage Examples

### Method 1: Python Script (Recommended)
```bash
# Call specific number
python make_dynamic_call.py +************

# Call with custom room and participant name
python make_dynamic_call.py +********** "my-call-room" "Agent Smith"
```

### Method 2: Direct LiveKit CLI
```bash
# Edit participant.json with desired number, then:
lk sip participant create serve/livekit/conf/participant.json
```

### Method 3: Programmatic
```python
from make_dynamic_call import make_outbound_call

call_details = make_outbound_call(
    phone_number="+************",
    room_name="custom-room",
    participant_name="My Agent"
)
```

## Removed Files (Obsolete)

The following files were removed during cleanup as they're no longer needed:

### Old Configuration Files
- `sip-trunk-config.json` - Old empty template
- `sip-dispatch-rule-config.json` - Old dispatch rule config
- `sip_trunk_config.json` - Old trunk config (superseded)

### Temporary Files
- `call_details_*.json` - Test call detail files (auto-generated)
- `serve/livekit/conf/participant-dynamic.json` - Duplicate config

## Maintenance

### Regular Operations
- Use `make_dynamic_call.py` for outbound calls
- Monitor calls via LiveKit dashboard
- Check FreeSWITCH logs if needed

### Configuration Updates
- Update `outbound_trunk_config.json` when trunk changes
- Modify `serve/freeswitch-minimal/freeswitch.xml` for dialplan changes
- Deploy FreeSWITCH changes with `./deploy_freeswitch_config.sh`

### Troubleshooting
- Verify trunk exists: `lk sip outbound list`
- Check FreeSWITCH connectivity: Test extension 777 first
- Verify authentication: Ensure keyman credentials are correct

## Architecture Notes

### Call Flow
1. LiveKit receives outbound call request with phone number
2. LiveKit authenticates to FreeSWITCH as user `keyman`
3. FreeSWITCH matches phone number pattern `^(\+\d{10,15})$`
4. FreeSWITCH routes call to `sofia/gateway/octella/${destination_number}`
5. Octella gateway forwards call to external phone network

### Backward Compatibility
- Extension 777 still works for hardcoded +************
- All existing functionality preserved
- Can gradually migrate to dynamic calling

### Security
- Authentication required for FreeSWITCH access
- Phone number validation in script
- No hardcoded credentials in public files