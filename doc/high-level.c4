specification {
  element system
  element actor
  element container
  element component
  {
    style {
        color sky
    }
  }
}

model {
  lead = actor 'Phone Lead' {
     style { 
        shape person
        color indigo
    }
    description 'The phone lead'
    

  }

  actor Administrator 'Administrator' {
    style { 
        shape person 
        color indigo
    }
  }

  system Twi<PERSON> 'Twilio' {
    style {
        color sky
        icon https://www.vectorlogo.zone/logos/twilio/twilio-icon.svg
    }
    container Twi<PERSON> 'Twilio'
       component sip_trunk 'SIP Trunk' {
          technology 'SIP'
          description 'SIP Trunk'
          -> lead 'call'
    }
  }

   system ViciDialer 'ViciDialer' {
    style {
        color sky

    }
    container ViciDialer 'ViciDialer' {
        component CallPlan 'Call Plan' {
            technology 'SIP'
            description 'Call Plan'
            -> Asterisk 'dial'
        }
      }

    container Asterisk 'Asterisk'
       component InboundLeadCall 'Inbound Lead Call' {
          technology 'SIP'
          description 'Inbound Lead Call'
          -> lead 'call'
       }
       component InboundCall 'Inbound Call' {
          technology 'SIP'
          description 'Inbound Call'
          -> Twilio 'call'

    }

  }

  system customerSystem 'Customer System' {
    style { 
      shape rectangle
   }
    container api 'Management API' {
      technology 'REST'
      description 'Campaigns management API'
      component schedule_call 'Schedule Call'
      component receive_call_results 'Call Results Receiver'
      
    }
  }

  system AGenSi 'A Gen Si'{
    style {
        color sky
    }
   
     container Agent_system 'Agency'{
     description 'handles conversations worklow with functions, call analysis, sentiment detection'
     style {
            color sky
        }
    
    container Agent_instance 'Agent Host'{
        description 'host 3 agents by default'
       
      component call_to_customer 'Call to customer' {
        description 'Initiates outbound call'
        style { icon https://raw.githubusercontent.com/feathericons/feather/master/icons/phone.svg }
      }
      component send_followup 'Sends followup' {
        description 'Sends follow-up message'
        style { icon https://raw.githubusercontent.com/feathericons/feather/master/icons/mail.svg }
      }
      component schedule_call_back 'Schedule Callback' {
        description 'Schedules callback'
        style { icon https://raw.githubusercontent.com/feathericons/feather/master/icons/calendar.svg }
      }

      component do_not_call 'Do not call' {
        description 'Flags number as Do Not Call'
        style { icon https://raw.githubusercontent.com/feathericons/feather/master/icons/phone-off.svg }
      }
      component SentimentsModule 'Sentiments Analyser' {
        description 'Analyzes sentiment of input'
        style { icon https://raw.githubusercontent.com/feathericons/feather/master/icons/activity.svg }
      }
      component AnalyseConversation 'Call Analyser' {
        description 'Analyzes call transcript'
        style { icon https://raw.githubusercontent.com/feathericons/feather/master/icons/file-text.svg }
      }
    }
    
    component VAD 'Voice Auto Detection' {
      technology 'Silero VAD'
      description 'Detects voice activity'
      style { icon https://raw.githubusercontent.com/feathericons/feather/master/icons/volume-2.svg }
    }
    component EOU 'End of utterance detection' {
      description 'Detects end of utterance'
      style { icon https://raw.githubusercontent.com/feathericons/feather/master/icons/stop-circle.svg }
    }
    component STT_plug 'ASR and Transcription Plugin' {
      description 'Speech-to-text plugin'
      style { icon https://raw.githubusercontent.com/feathericons/feather/master/icons/mic.svg }
      component stt_deepgram 'Deepgram Streaming'
    }
    component TTS_plug 'Voice synthesis plugin' {
      description 'Text-to-speech plugin'
      style { icon https://raw.githubusercontent.com/feathericons/feather/master/icons/volume-2.svg }
      component tts_11labs 'ElevenLabs'
      component tts_cartesia 'Cartesia'
    }
    component LLM_plug 'LLM plugin' {
      description 'LLM integration plugin'
      style { icon https://raw.githubusercontent.com/feathericons/feather/master/icons/cpu.svg }
      component OpenAI_comp 'OpenAI API compatible'
        -> AI_Infrastructure.llm_server

    }
  }

     container ManagementSystem 'Management System' {
      style { 
        shape rectangle
        color sky
      }
      
   
       component Dialer 'Dialer' {
          description 'Consumes Outbound Queue & places calls'
          technology 'Python gRPC worker'
          style { icon https://raw.githubusercontent.com/feathericons/feather/master/icons/phone-forwarded.svg }
        }
       component ExternalAPI 'AI Services Config' {
        style {
            color sky
        }
        description 'Calls management API, for Customer services'
        technology 'FrappeFramework'
      }
      component Frontend 'Control Plane' {
        style {
            color sky
        }
        technology ' FrappeFramework'
        description 'Management Interface to login, manage agents and see call history'
        component Calls 'Calls' {
          description 'List of calls with call details'
          -> lead 'contains'
        }
        -> CampaignsSvc 'schedule calls'
        -> customerSystem.api.receive_call_results 'Call Results Callback'
        CampaignsSvc -> Dialer 'pop lead'
        Dialer -> RT_room_server.TelephonyGateway 'place call (SIP)'
        Dialer -> CampaignsSvc 'create/update'
        -> Agent_instance 'Call customer'
        


      }
      component CampaignsSvc 'Campaigns' {
        description 'Creates and manages Campaigns with list of leads'
        technology 'MariaDB'
        component LeadList 'Leads' {
          description 'List of leads with phone numbers to call'
          -> lead 'contains'
        }
        
      }
    container ConversationLogDatabase 'Conversation Log Database' {
      description 'Stores Conversation logs'
      technology 'MariaDB'
    }
     }
   
    
    
   
  }


  system AI_Infrastructure 'AI Services'{
    style {
        color sky
    }
    container deepgram_services 'Transcriber'{
      technology 'Deepgram'
      description 'Tuned Speech to Text services'
      style {
          icon https://www.datocms-assets.com/96965/1683539914-logo.svg
      }
    }

    container Elabs_services 'Voice' {
      technology 'ElevenLabs'
      description 'Tuned Text to Speech services'
      style {
          icon https://www.elevenlabs.io/favicon.ico
      }
    }
    container llm_server 'LLM' {
      technology 'OpenAI, LLama '
      description 'conversation and analysis AI model'
      style {
          icon https://upload.wikimedia.org/wikipedia/commons/6/66/OpenAI_logo_2025_(symbol).svg
      }
    }
  }
 
 
  // Main system boundary
  system RT_room_server 'Livekit services' {
    style { 
            color sky
            icon https://livekit.io/brand/LK-colored.png
        } 

    container  TelephonyGateway 'Livekit SIP Gateway' {
      description 'Bridges PSTN/SIP'
      style {
        
      }
    }
    // Containers
    container RoomService 'Rooms' {
      description 'Manages room lifecycle & config'
      technology 'gRPC protocol'
    }
    RoomService -> TelephonyGateway 'Init calls'
  }

  // Relationships
  Agent_system -> RT_room_server.RoomService '-'
  RT_room_server.TelephonyGateway -> Twilio.sip_trunk 'SIP integration'
  
  Administrator -> Frontend 'Manages configuration and campaigns'
  Administrator -> ConversationLogDatabase.SessionStore 'view live sessions'
  RT_room_server.TelephonyGateway -> Twilio.sip_trunk 'Outbound calls'


  // Added relationship for incoming calls from TelephonyProviders
  
  Agent_system.STT_plug.stt_deepgram -> AI_Infrastructure.deepgram_services
  Agent_system.TTS_plug.tts_11labs -> AI_Infrastructure.Elabs_services


}

views 
{
  // System Context View: High-level view of the system and external entities
  /**
   * @likec4-generated(v1)
   * iKRoYXNo2ShkZmRiOGJkMjNjMGFjMTBlNDI2NTMwYWI0MWRiYzYwOWQzMDJhNjMyqmF1dG9MYXlvdXSBqWRpcmVjdGlvbqJCVKF40J+heQCld2lkdGjNCGimaGVpZ2h0zQYrpW5vZGVzjapWaWNpRGlhbGVygqFilNCfzQEZzQNazQEZoWPDrUFkbWluaXN0cmF0b3KCoWKUzQRqzQV3zQFAzLShY8KmQUdlblNpgqFilM0CIs0Dns0F5c0BfqFjw65SVF9yb29tX3NlcnZlcoKhYpTNA6jNAo/NAUDMtKFjwrFBSV9JbmZyYXN0cnVjdHVyZYKhYpTNBpLNAo/NAUDMtKFjwqZUd2lsaW+CoWKUzQOOzQFDzQFAzLShY8KkbGVhZIKhYpTNA44AzQFAzLShY8K1VmljaURpYWxlci5WaWNpRGlhbGVygqFilNDHzQFWzQFAzLShY8KzVmljaURpYWxlci5Bc3Rlcmlza4KhYpTNAZHN
   * AVbNAUDMtKFjwrdBR2VuU2kuTWFuYWdlbWVudFN5c3RlbYKhYpTNAkrNA9vNA5nNARmhY8OzQUdlblNpLkFnZW50X3N5c3RlbYKhYpTNBoXNBBjNAVrMtKFjwr5BR2VuU2kuTWFuYWdlbWVudFN5c3RlbS5EaWFsZXKCoWKUzQJyzQQYzQF4zLShY8LZIEFHZW5TaS5NYW5hZ2VtZW50U3lzdGVtLkZyb250ZW5kgqFilM0EWc0EGM0BYsy0oWPCpWVkZ2Vzi6Zza25vcXeDomNwkYKheMtAcwZmZmZmZqF5zQGwoWyEoXjNAUyhec0BsKV3aWR0aBqmaGVpZ2h0EqFwlJLNAXDNAumSzQGZzQLpks0Bxs0C6ZLNAfDNAumnMXhuN3ZzdYKhbISheM0FC6F5zQUkpXdpZHRozPSmaGVpZ2h0EqFwlJLNBQrNBXeSzQUKzQVGks0FCs0FCZLNBQrNBNamcjEweDhwgqFshKF4zQVFoXnN
   * Alald2lkdGg4pmhlaWdodBKhcJeSzQUgzQQYks0FQc0DfZLNBXLNAjaSzQUFzQFDks0E7s0BEJLNBMjM4ZLNBKLMu6cxcGQzZWtmgqFshKF4zQYboXnNBHKld2lkdGhapmhlaWdodBKhcJSSzQW7zQRyks0F+M0EcpLNBj3NBHKSzQZ7zQRypzF3djhobDKCoWyEoXjNBC6hec0BAaV3aWR0aBmmaGVpZ2h0EqFwlJLNBC7NAUOSzQQuzQEaks0ELszpks0ELsy+pzFqMWMwaneCoWyEoXjNBeChec0DjqV3aWR0aAmmaGVpZ2h0EqFwl5LNBr3NBBiSzQaFzQPwks0GQM0DwZLNBf7NA56SzQWozQNvks0FRM0DRZLNBPLNAyamYnkwdjl5gqFshKF4zQQ7oXnNAkild2lkdGgZpmhlaWdodBKhcJSSzQRBzQKPks0EPs0CY5LNBDnNAi6SzQQ2zQIBpnlnNWZjd4GhcJSSzQcyzQQY
   * ks0HMs0D3JLNBzLNA4uSzQcyzQNNpnFpcnB2a4KhbISheM0DuKF5zQOypXdpZHRoYqZoZWlnaHQSoXCUks0Dbs0EGJLNA5rNA9uSzQPVzQOKks0EAs0DS6cxOTZ2eHZzg6JjcJGCoXjLQIktmZmZmZqhectAegC0qCtKg6FshKF4zQNEoXnNAaCld2lkdGgZpmhlaWdodBKhcJSSzQNSzQJSks0Ddc0CNZLNA5nNAheSzQO5zQH9pmxndnI3b4OiY3CRgqF4y0CGos8d4s8eoXnLQG6oWemFnpihbISheM0DDaF5zNyld2lkdGgZpmhlaWdodBKhcJeSzQLQzQJSks0C8c0B/pLNAx/NAZeSzQNXzQFDks0Dd80BE5LNA6HM45LNA8jMuw==
   */
  view system_context {
    title 'System Context'
    include lead, Asterisk,ViciDialer.ViciDialer, Administrator, Agent_system, ManagementSystem.Frontend,
            Twilio, RT_room_server, AI_Infrastructure, AGenSi.Dialer, ViciDialer
    style RT_room_server.* {
      display none
    }
  	autoLayout BottomTop
	}


   /**
    * @likec4-generated(v1)
    * iKRoYXNo2ShjODVlN2M0YjdlMTBiODdlZWNlMTJiNTYwNTU0MjY4NzFmY2E2YjJmqmF1dG9MYXlvdXSBqWRpcmVjdGlvbqJUQqF4AKF50KGld2lkdGjNCSqmaGVpZ2h0zQVwpW5vZGVz3gASrUFkbWluaXN0cmF0b3KCoWKUzQSz0KHNAUDMtKFjwqZBR2VuU2mCoWKUzQIBAM0CJc0ExqFjw65jdXN0b21lclN5c3RlbYKhYpTNBIrNAajNAYDNAQqhY8OxQUlfSW5mcmFzdHJ1Y3R1cmWCoWKUzQZuzPXNAdbNA0mhY8OuUlRfcm9vbV9zZXJ2ZXKCoWKUAM0C4M0Bnc0CMaFjw6ZUd2lsaW+CoWKUXc0ByM0BQMy0oWPCpGxlYWSCoWKUXcywzQFAzLShY8K3QUdlblNpLk1hbmFnZW1lbnRTeXN0ZW2CoWKUzQIpPc0B1c0CMaFjw61BR2VuU2kuRGlhbGVygqFilM0CQc0C0s0B
    * eMy0oWPCs0FHZW5TaS5BZ2VudF9zeXN0ZW2CoWKUzQJmzQPqzQFazLShY8KyY3VzdG9tZXJTeXN0ZW0uYXBpgqFilM0Eqs0B3s0BQMy0oWPCvEFJX0luZnJhc3RydWN0dXJlLmxsbV9zZXJ2ZXKCoWKUzQaWzQJKzQFozLShY8LZIEFJX0luZnJhc3RydWN0dXJlLkVsYWJzX3NlcnZpY2VzgqFilM0Gvs0DYs0BSsy0oWPC2SNBSV9JbmZyYXN0cnVjdHVyZS5kZWVwZ3JhbV9zZXJ2aWNlc4KhYpTNBtLNATLNAUrMtKFjwrpSVF9yb29tX3NlcnZlci5Sb29tU2VydmljZYKhYpQ1zQQ1zQFAzLShY8K/UlRfcm9vbV9zZXJ2ZXIuVGVsZXBob255R2F0ZXdheYKhYpQozQMdzQFAzLShY8LZIEFHZW5TaS5NYW5hZ2VtZW50U3lzdGVtLkZyb250ZW5kgqFilM0CdHrNAWLMtKFj
    * wtk8QUdlblNpLk1hbmFnZW1lbnRTeXN0ZW0uQ29udmVyc2F0aW9uTG9nRGF0YWJhc2UuU2Vzc2lvblN0b3JlgqFilM0CUc0Bks0BUcy0oWPCpWVkZ2VzjqZhMGtpOWyBoXCUks0DwM0EAZLNBITNA7aSzQXDzQM7ks0Gjc0C7acxNmV1Z2l1gaFwlJLNA8DNBC+SzQSOzQQVks0F5M0D65LNBrTNA9GmOWJjajgygaFwmpLNA8DNBACSzQRizQO7ks0FWM0DR5LNBhTNArySzQZSzQKOks0GU80Cc5LNBozNAkCSzQaszQIjks0G0M0CBpLNBvPNAeynMWp3NjFkZYKhbISheM0EM6F5zQGDpXdpZHRozIimaGVpZ2h0EqFwlJLNA7DNAS6SzQQAzQFiks0EZc0BpJLNBLfNAdinMXBkM2VrZoKhbISheM0D96F5zQKIpXdpZHRoWqZoZWlnaHQSoXCaks0Dec0BLpLNA43NAUmSzQOh
    * zQFoks0Drc0BiJLNA/7NAmGSzQQbzQK6ks0Dw80DkJLNA7fNA6+SzQOjzQPLks0DjM0D46cxeG43dnN1g6JjcJGCoXjLQJHCzMzMzM2hectAVMBbBbBbBqFshKF4zQRFoXlkpXdpZHRozPSmaGVpZ2h0EqFwl5LNCSnNAgmSzQkEzQGuks0Ivs0BKJLNCE7M65LNBuUlks0E7mmSzQPgzKSnMXhjMzhqa4KhbISheM0B86F5zQRqpXdpZHRoCaZoZWlnaHQSoXCUks0CZs0EW5LNAhzNBGWSzQHIzQRwks0Bf80EeacxcmR0cml1gqFshKF4zQHZoXnNA1Old2lkdGhipmhlaWdodBKhcJSSzQJBzQNFks0B/s0DTpLNAbTNA1iSzQFyzQNhpnRkcG85NIKhbISheMzPoXnNBAmld2lkdGg3pmhlaWdodBKhcJSSzNHNBDaSzNDNBBmSzM7NA/mSzM3NA9unMXNyOXNrOIKhbISheMzi
    * oXnNAtKld2lkdGgZpmhlaWdodBKhcJSSzNbNAx6SzN3NAu+SzObNAraSzO7NAoanMXd2OGhsMoKhbISheMz9oXnNAZuld2lkdGgZpmhlaWdodBKhcJSSzP3NAciSzP3NAaySzP3NAYySzP3NAW6mY3hqNGpxgqFshKF4zQL7oXnNApKld2lkdGhapmhlaWdodBKhcJSSzQL8zQLSks0C/M0CqpLNAvvNAnqSzQL7zQJRpzF1aHZycm6DomNwkYKheMtAkcvsHhTPRaF5y0BmgzMzMzMzoWyEoXjNBCahecz0pXdpZHRoc6ZoZWlnaHQSoXCXks0JKs0CCZLNCQXNAa6SzQi/zQEnks0ITszrks0GwxeSzQSmzP+SzQOczQGOpzFvZnE3NXeCoWyEoXjNAfuhec0Be6V3aWR0aDimaGVpZ2h0EqFwlJLNAlHNAaGSzQIazQGJks0B3c0BbpLNAabNAVU=
    */
   view container_view_v_1_1 {
    title 'Container View'
    include RT_room_server, customerSystem.*, lead, Agent_system, AI_Infrastructure
    include AI_Infrastructure.llm_server, AI_Infrastructure.Elabs_services, AI_Infrastructure.deepgram_services
    include AGenSi, AGenSi.Frontend, AGenSi.Dialer, Administrator
    include RT_room_server.RoomService, RT_room_server.TelephonyGateway
    include Twilio, ConversationLogDatabase.SessionStore
    style RT_room_server.** {
    }
    style customerSystem {
      shape rectangle
      color slate
    }
     style RT_room_server {
       border solid
     }
     style AGenSi {
       border dashed
     }
  }

  /**
   * @likec4-generated(v1)
   * iKRoYXNo2ShhNGQ3NGI4MWZmOTM0OTQxOTBhZDdhYTJiNGM3ODVmZGY4NDRjOTM2qmF1dG9MYXlvdXSDqWRpcmVjdGlvbqJSTKdub2RlU2VwzNSncmFua1NlcMzroXjyoXk5pXdpZHRozQdMpmhlaWdodM0Eb6Vub2Rlc94AEKZBR2VuU2mCoWKUzQIPOc0FL80Eb6Fjw7FBSV9JbmZyYXN0cnVjdHVyZYKhYpTyzOnNAcPNA0mhY8PZIkFHZW5TaS5BZ2VudF9zeXN0ZW0uQWdlbnRfaW5zdGFuY2WCoWKUzQPdcM0DOc0EEKFjw7xBR2VuU2kuQWdlbnRfc3lzdGVtLlRUU19wbHVngqFilM0CUM0BAs0BQMy0oWPCvEFHZW5TaS5BZ2VudF9zeXN0ZW0uU1RUX3BsdWeCoWKUzQI3zQMUzQF1zLShY8K8QUdlblNpLkFnZW50X3N5c3RlbS5MTE1fcGx1Z4KhYpTNAkbNAhDNAUDM
   * tKFjwtkjQUdlblNpLk1hbmFnZW1lbnRTeXN0ZW0uRXh0ZXJuYWxBUEmCoWKUzQWgzQOZzQFbzLShY8LZIEFJX0luZnJhc3RydWN0dXJlLkVsYWJzX3NlcnZpY2VzgqFilEPNASbNAUrMtKFjwtkjQUlfSW5mcmFzdHJ1Y3R1cmUuZGVlcGdyYW1fc2VydmljZXOCoWKUOM0DVs0BSsy0oWPCvEFJX0luZnJhc3RydWN0dXJlLmxsbV9zZXJ2ZXKCoWKUGs0CPs0BaMy0oWPC2TNBR2VuU2kuQWdlbnRfc3lzdGVtLkFnZW50X2luc3RhbmNlLmNhbGxfdG9fY3VzdG9tZXKCoWKUzQQFzQOkzQFAzLShY8LZMEFHZW5TaS5BZ2VudF9zeXN0ZW0uQWdlbnRfaW5zdGFuY2Uuc2VuZF9mb2xsb3d1cIKhYpTNBa7NAaTNAUDMtKFjwtk1QUdlblNpLkFnZW50X3N5c3RlbS5BZ2VudF9p
   * bnN0YW5jZS5zY2hlZHVsZV9jYWxsX2JhY2uCoWKUzQWtzQKpzQFAzLShY8LZLkFHZW5TaS5BZ2VudF9zeXN0ZW0uQWdlbnRfaW5zdGFuY2UuZG9fbm90X2NhbGyCoWKUzQQKzQKizQFAzLShY8LZM0FHZW5TaS5BZ2VudF9zeXN0ZW0uQWdlbnRfaW5zdGFuY2UuU2VudGltZW50c01vZHVsZYKhYpTNBa7Mps0BQMy0oWPC2TZBR2VuU2kuQWdlbnRfc3lzdGVtLkFnZW50X2luc3RhbmNlLkFuYWx5c2VDb252ZXJzYXRpb26CoWKUzQQKzQGozQFAzLShY8KlZWRnZXODpmJpejdudYKiY3CRgqF4y0CArAAAAAAAoXnLQHayB+B+B+GhcJeSzQYFzQFVks0F480BRZLNBb/NATeSzQWczQEvks0EPMzaks0Ckc0BCpLNAabNATGnMTZsOTNzdYKiY3CRgqF4y0CABZmZmZmaoXnL
   * QIxew9unu+2hcJqSzQXQfZLNBORmks0DTGOSzQJGzQEvks0Bp80BqZLNAgjNAiiSzQGazQLPks0Bh80C7JLNAW/NAwiSzQFXzQMipzFseGUzdXqComNwkYKheMtAgFmZmZmZmqF5y0CD5jiMm+R9oXCdks0GLs0CbZLNBhjNAlKSzQYBzQIyks0F780CE5LNBbrNAbWSzQX1zQFtks0FnM0BL5LNBGVVks0Dqcypks0CRs0BL5LNAj7NATKSzQGwzQGuks0BSc0CCg==
   */
  view agent_component_view {
    title 'Agent Component View'
    include Agent_system.Agent_instance
    include Agent_system.Agent_instance.*
    include Agent_system.LLM_plug
    include Agent_system.STT_plug
    include Agent_system.TTS_plug
    include AI_Infrastructure
    include AI_Infrastructure.Elabs_services
    include AI_Infrastructure.deepgram_services
    include ExternalAPI
    //include AI_Infrastructure.nemo_server
    include AI_Infrastructure.llm_server
    include AGenSi
    style Agent_system.Agent_instance.SentimentsModule {
      shape rectangle
    }
    style AI_Infrastructure {
      border dotted
    }
    
  	autoLayout RightLeft 235 212
    style AGenSi {
      border dotted
    }
	}


/**
 * @likec4-generated(v1)
 * iKRoYXNo2Sg2MDIxYmJjNzczMjhiNDI1NjAyZTAyNTJjMzRhZGU3NWRlYzFlMTllqmF1dG9MYXlvdXSDqWRpcmVjdGlvbqJUQqdub2RlU2VwNadyYW5rU2VwQaF4KqF5AKV3aWR0aM0IRqZoZWlnaHTNBjGlbm9kZXOLulJUX3Jvb21fc2VydmVyLlJvb21TZXJ2aWNlgqFilM0EjADNAUDMtKFjwqZBR2VuU2mCoWKUKs0BJs0Fpc0FC6Fjw7NBR2VuU2kuQWdlbnRfc3lzdGVtgqFilFLNAV3NBVXNBKyhY8O3QUdlblNpLkFnZW50X3N5c3RlbS5WQUSCoWKUzQQzzQGUzQFAzLShY8K3QUdlblNpLkFnZW50X3N5c3RlbS5FT1WCoWKUzQQmzQKazQFZzLShY8K8QUdlblNpLkFnZW50X3N5c3RlbS5TVFRfcGx1Z4KhYpTNAj3NAp3NAXXMtKFjwrxBR2VuU2kuQWdlbnRfc3lz
 * dGVtLkxMTV9wbHVngqFilHrNA4TNAUDMtKFjwrxBR2VuU2kuQWdlbnRfc3lzdGVtLlRUU19wbHVngqFilMyLzQKUzQFAzLShY8LZIkFHZW5TaS5BZ2VudF9zeXN0ZW0uQWdlbnRfaW5zdGFuY2WCoWKUzQJVzQTIzQMFzQEZoWPD2TNBR2VuU2kuQWdlbnRfc3lzdGVtLkFnZW50X2luc3RhbmNlLlNlbnRpbWVudHNNb2R1bGWCoWKUzQJ9zQUFzQFAzLShY8LZNkFHZW5TaS5BZ2VudF9zeXN0ZW0uQWdlbnRfaW5zdGFuY2UuQW5hbHlzZUNvbnZlcnNhdGlvboKhYpTNA/LNBQXNAUDMtKFjwqVlZGdlc4qnc3RlcC0wMYOiY3CRgqF4y0CTaMvV7lc2oXnLQGrGZmZmZmahbISheM0Ey6F5zO2ld2lkdGjM2aZoZWlnaHQToXCUks0FLMy0ks0FLMzJks0FLMzhks0FLMz5p3N0
 * ZXAtMDKDomNwkYKheMtAk0sgDIHCP6F5y0CDBMzMzMzNoWyEoXjNBNOhec0CcaV3aWR0aM0BCaZoZWlnaHQjoXCUks0FEc0CKpLNBQfNAkqSzQT7zQJvks0E8c0CkKdzdGVwLTAzg6JjcJGCoXjLQJAMzMzMzM2hectAh6p5XvZoIKFshKF4zQPsoXnNAvald2lkdGjM+6ZoZWlnaHQjoXCUks0E080DTpLNBNPNA26SzQTTzQOTks0E080DtKdzdGVwLTA0g6JjcJGCoXjLQIgniE/KziGhectAjp5mZmZmZqFshKF4zQMKoXnNBCuld2lkdGjM+qZoZWlnaHQjoXCaks0Eks0EcZLNBHjNBI+SzQRWzQSuks0EMM0EwJLNBAzNBNGSzQP8zQS5ks0D180EyJLNA7fNBNWSzQOYzQTpks0Dfc0E/6dzdGVwLTA1g6JjcJGCoXjLQIKSwNTHewOhectAkx4AAAAAAKFshKF4zQIboXnN
 * BJ+ld2lkdGjNARemaGVpZ2h0I6Fwl5LNAxXNBQWSzQMOzQTeks0DAM0EspLNAuTNBJKSzQLGzQRwks0CnM0EV5LNAnLNBEWnc3RlcC0wNoOiY3CRgqF4y0ByGmZmZmZmoXnNA3KhbISheM0BI6F5zQNmpXdpZHRozP2maGVpZ2h0I6Fwl5LNASnNBCuSzOnNBDySzKTNBFqSfc0EkpJlzQS0knLNBNqSzIrNBP2nc3RlcC0wN4OiY3CRgqF4y0CAa/gSC+TuoXnLQJF/9632lbKhbISheM0CQ6F5zQR8pXdpZHRozQENpmhlaWdodBOhcJeSzQGrzQRxks0Bps0EjpLNAanNBKuSzQG9zQTAks0Bys0EzZLNAkfNBMOSzQJvzQTGp3N0ZXAtMDiDomNwkYKheMtAg+aYKhvA1qF5y0CSbbNKjkAooWyEoXjNAkKhec0EfKV3aWR0aMz5pmhlaWdodCOhcJqSzQVazQUFks0Fi80E45LN
 * BajNBLuSzQWDzQSSks0FZs0EcpLNBCnNBHeSzQP+zQRxks0Dec0EYZLNAuPNBEmSzQJzzQQ3p3N0ZXAtMDmDomNwkYKheMtAkpAJvPfQr6F5y0BypMzMzMzNoWyEoXjNBLqhec0BCaV3aWR0aMzjpmhlaWdodCKhcJ2SzQbqzQTUks0G9c0EzpLNBwDNBMeSzQcKzQTAks0Hsc0ETpLNB9XNBA6SzQgTzQNOks0IcM0CK5LNB/7NAYySzQcKzNWSzQaxzJKSzQY4dJLNBdZmp3N0ZXAtMTCCoWyEoXjNBuKhec0DIqV3aWR0aM0BF6ZoZWlnaHQToXDcABCSzQXMdpLNBiLMjZLNBofMt5LNBsPNAQSSzQb7zQFMks0G4s0BdJLNBuLNAc+SzQbizQHPks0G4s0Bz5LNBuLNBBiSzQbizQRkks0G9s0EiJLNBsPNBMCSzQaOzQT6ks0Fzc0FJ5LNBTzNBUM=
 */
dynamic view voice_pipeline_advanced1 {
  title "Advanced Voice Pipeline Flow with Stages, Interruptions, Function Handling, Sentiment Analysis & Results Analysis"
  
  RT_room_server.RoomService -> AGenSi "Stage 0: Subscriber connected"
  // Stage 1: Input Detection & Transcription
  // The agent listens to incoming speech and commits the transcript.
  AGenSi.Agent_system.VAD -> AGenSi.Agent_system.EOU "Stage 1: Detects speech boundaries & signals start-of-speech"
  AGenSi.Agent_system.EOU -> AGenSi.Agent_system.STT_plug "Stage 1: Determines end-of-speech; commits transcript"
  
  // Stage 2: Sentiment Analysis & Processing
  // The transcribed text is first sent to the Sentiments Module for analysis, then forwarded to the LLM.
  AGenSi.Agent_system.STT_plug -> AGenSi.Agent_system.SentimentsModule "Stage 2: Analyzes sentiment of user input"
  AGenSi.Agent_system.SentimentsModule -> AGenSi.Agent_system.LLM_plug "Stage 2: Provides sentiment context with transcription"
    
  // Additional processing and function call handling
  AGenSi.Agent_system.LLM_plug -> AGenSi.Agent_system.TTS_plug "Stage 2: Processes text & generates preliminary response"
  AGenSi.Agent_system.LLM_plug -> AGenSi.Agent_system.Agent_instance "Stage 2: Triggers function call requests"
  AGenSi.Agent_system.Agent_instance -> AGenSi.Agent_system.LLM_plug "Stage 2: Returns function results for enhanced reply"
  AGenSi.Agent_system.TTS_plug -> AGenSi.Agent_system.VAD "Stage 2: Synthesizes final response"
  // Stage 3: Interruption, Synthesis & Publishing
  // The agent handles interruptions, synthesizes the final response, and publishes audio.
  AGenSi.Agent_system -> RT_room_server.RoomService "Stage 3: Joins room & publishes synthesized audio"
  
  // Stage 4: Results Analysis
  // Once the call is finished, the room service forwards call data to the Call Results Analyser,
  // and analysis feedback is integrated back into the agent.
  RT_room_server.RoomService -> AnalyseConversation "Stage 4: Forwards call data for analysis"
  
  autoLayout TopBottom 65 53
  style AGenSi.Agent_system {
    border dashed
  }
}
}