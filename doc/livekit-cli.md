# LiveKit CLI (lk) Documentation

## Table of Contents
- [Overview](#overview)
- [Global Options](#global-options)
- [Commands](#commands)
  - [app](#app) - Initialize and manage applications
  - [agent](#agent) - Manage LiveKit Cloud Agents
  - [cloud](#cloud) - Interact with LiveKit Cloud services
  - [project](#project) - Manage projects
  - [room](#room) - Manage rooms
  - [token](#token) - Create access tokens
  - [dispatch](#dispatch) - Manage agent dispatches
  - [egress](#egress) - Record or stream media
  - [ingress](#ingress) - Import media sources
  - [sip](#sip) - Manage SIP functionality
  - [perf](#perf) - Performance testing

## Overview

The LiveKit CLI (`lk`) is a command-line interface for interacting with LiveKit servers and services. It provides utilities for managing rooms, tokens, recordings, and more.

## Global Options

These options can be used with any command:

- `--url URL`: URL to LiveKit instance (default: "http://localhost:7880")
- `--api-key KEY`: Your API key
- `--api-secret SECRET`: Your API secret
- `--dev`: Use developer credentials for local LiveKit server
- `--project NAME`: Name of a configured project
- `--subdomain SUBDOMAIN`: Subdomain of a configured project
- `--config TOML`: Config TOML to use (default: "livekit.toml")
- `--curl`: Print curl commands for API actions
- `--verbose`: Enable verbose output

## Commands

### app

Initialize and manage applications

```
$ lk app --help
```

**Subcommands:**
- `create`: Bootstrap a new application from a template
- `list-templates`: List available templates
- `env`: Fill environment variables

### agent

Manage LiveKit Cloud Agents

```
$ lk agent --help
```

### cloud

Interact with LiveKit Cloud services

```
$ lk cloud --help
```

### project

Add or remove projects and view existing project properties

```
$ lk project --help
```

### room

Create or delete rooms and manage existing room properties

```
$ lk room --help
```

### token

Create access tokens with granular capabilities

```
$ lk token --help
```

### dispatch

Create, list, and delete agent dispatches

```
$ lk dispatch --help
```

### egress

Record or stream media from LiveKit to elsewhere

```
$ lk egress --help
```

### ingress

Import outside media sources into a LiveKit room

```
$ lk ingress --help
```

### sip

Manage SIP Trunks, Dispatch Rules, and Participants

```
$ lk sip --help
```

### perf

Performance testing commands

```
$ lk perf --help
```

## Examples

### Create a new access token
```bash
lk token create --api-key API_KEY --api-secret API_SECRET --identity user1 --room my-room --join
```

### List rooms
```bash
lk room list --api-key API_KEY --api-secret API_SECRET
```

## Getting Help

For detailed help on any command, use the `--help` flag:

```bash
lk COMMAND --help
```

## Configuration

Create a `livekit.toml` file in your working directory to store default values for the CLI options.

## Version

Current version: 2.4.14Kit CLI (lk) Documentation

## Table of Contents

