# FreeSWITCH + LiveKit Stack Deployment Instructions

## Overview
This document provides step-by-step instructions for deploying and testing the integrated FreeSWITCH + LiveKit + Redis stack using the working configuration.

## Prerequisites

### System Requirements
- Docker and Docker Compose installed
- Ubuntu/Debian server with external IP access
- SIPP tool installed for testing
- UFW firewall configured

### Network Configuration
- **External IP**: ************* (replace with your server's IP)
- **Required Ports**:
  - 5080 (TCP/UDP) - FreeSWITCH SIP
  - 8021 (TCP) - FreeSWITCH ESL
  - 7880-7881 (TCP) - LiveKit HTTP/RTC
  - 30000-30100 (UDP) - LiveKit RTP
  - 5070 (TCP) - LiveKit SIP
  - 6379 (TCP) - Redis

### Firewall Setup
```bash
# Allow required ports
sudo ufw allow 5080/tcp
sudo ufw allow 5080/udp
sudo ufw allow 8021/tcp
sudo ufw allow 7880:7881/tcp
sudo ufw allow 30000:30100/udp
sudo ufw allow 5070/tcp
sudo ufw allow 6379/tcp
```

## Deployment Steps

### 1. Prepare Configuration Files

Ensure the following files are properly configured in your project:

#### FreeSWITCH Configuration
- **File**: `serve/freeswitch-minimal/freeswitch.xml`
- **Key Settings**:
  ```xml
  <param name="sip-port" value="5080"/>
  <domain name="*************">  <!-- Replace with your IP -->
    <user id="keyman">
      <params>
        <param name="password" value="keyman123"/>
      </params>
    </user>
  </domain>
  ```

#### Docker Compose Configuration
- **File**: `docker-compose-external.yml`
- **Key Settings**:
  ```yaml
  freeswitch:
    container_name: freeswitch-minimal
    build:
      context: ./serve/freeswitch-minimal
      dockerfile: Dockerfile
    image: freeswitch-custom
    network_mode: host
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "fs_cli", "-x", "status"]
      interval: 30s
      timeout: 10s
      retries: 3
  ```

### 2. Build and Deploy

```bash
# Navigate to project directory
cd /path/to/your/project

# Build and start all services
docker-compose -f docker-compose-external.yml up -d

# Verify all containers are running
docker ps
```

### 3. Verify Deployment

#### Check Container Status
```bash
# All containers should show "Up" status
docker ps --format "table {{.Names}}\t{{.Status}}"

# Expected containers:
# - freeswitch-minimal (Up, healthy)
# - livekit (Up)
# - livekit-sip (Up)
# - redis-agent (Up)
```

#### Check FreeSWITCH Ports
```bash
# Verify FreeSWITCH is listening on correct ports
docker exec freeswitch-minimal netstat -tuln | grep :5080
docker exec freeswitch-minimal netstat -tuln | grep :8021
```

#### Check Service Logs
```bash
# FreeSWITCH logs
docker logs freeswitch-minimal --tail 20

# LiveKit logs
docker logs livekit --tail 10
docker logs livekit-sip --tail 10

# Redis logs
docker logs redis-agent --tail 5
```

## Testing Procedures

### 1. SIP Registration Test

Create test registration scenario (`test_register.xml`):
```xml
<?xml version="1.0" encoding="ISO-8859-1" ?>
<scenario name="Register">
  <send retrans="500">
    <![CDATA[
      REGISTER sip:[remote_ip]:[remote_port] SIP/2.0
      Via: SIP/2.0/UDP [local_ip]:[local_port];branch=[branch]
      From: <sip:keyman@[remote_ip]>;tag=[pid]SIPpTag00[call_number]
      To: <sip:keyman@[remote_ip]>
      Call-ID: [call_id]
      CSeq: 1 REGISTER
      Contact: <sip:keyman@[local_ip]:[local_port]>
      Content-Length: 0
    ]]>
  </send>

  <recv response="401" auth="true">
  </recv>

  <send retrans="500">
    <![CDATA[
      REGISTER sip:[remote_ip]:[remote_port] SIP/2.0
      Via: SIP/2.0/UDP [local_ip]:[local_port];branch=[branch]
      From: <sip:keyman@[remote_ip]>;tag=[pid]SIPpTag00[call_number]
      To: <sip:keyman@[remote_ip]>
      Call-ID: [call_id]
      CSeq: 2 REGISTER
      Contact: <sip:keyman@[local_ip]:[local_port]>
      [authentication username=keyman password=keyman123]
      Content-Length: 0
    ]]>
  </send>

  <recv response="200" rtd="true">
  </recv>
</scenario>
```

Run registration test:
```bash
sipp -sf test_register.xml *************:5080 -m 1
```

**Expected Result**: 
- 401 Unauthorized (auth challenge)
- 200 OK (successful registration)

### 2. Basic Call Test

Create basic call scenario (`test_call_999.xml`):
```xml
<?xml version="1.0" encoding="ISO-8859-1" ?>
<scenario name="UAC">
  <!-- Registration first -->
  <send retrans="500">
    <![CDATA[
      REGISTER sip:[remote_ip]:[remote_port] SIP/2.0
      Via: SIP/2.0/UDP [local_ip]:[local_port];branch=[branch]
      From: <sip:keyman@[remote_ip]>;tag=[pid]SIPpTag00[call_number]
      To: <sip:keyman@[remote_ip]>
      Call-ID: [call_id]
      CSeq: 1 REGISTER
      Contact: <sip:keyman@[local_ip]:[local_port]>
      Content-Length: 0
    ]]>
  </send>

  <recv response="401" auth="true">
  </recv>

  <send retrans="500">
    <![CDATA[
      REGISTER sip:[remote_ip]:[remote_port] SIP/2.0
      Via: SIP/2.0/UDP [local_ip]:[local_port];branch=[branch]
      From: <sip:keyman@[remote_ip]>;tag=[pid]SIPpTag00[call_number]
      To: <sip:keyman@[remote_ip]>
      Call-ID: [call_id]
      CSeq: 2 REGISTER
      Contact: <sip:keyman@[local_ip]:[local_port]>
      [authentication username=keyman password=keyman123]
      Content-Length: 0
    ]]>
  </send>

  <recv response="200" rtd="true">
  </recv>

  <pause milliseconds="500"/>

  <!-- Call to extension 999 -->
  <send retrans="500">
    <![CDATA[
      INVITE sip:999@[remote_ip] SIP/2.0
      Via: SIP/2.0/UDP [local_ip]:[local_port];branch=[branch]
      From: <sip:keyman@[remote_ip]>;tag=1
      To: <sip:999@[remote_ip]>
      Call-ID: [call_id]
      CSeq: 1 INVITE
      Contact: <sip:keyman@[local_ip]:[local_port]>
      [authentication username=keyman password=keyman123]
      Content-Type: application/sdp
      Content-Length: [len]

      v=0
      o=user1 53655765 2353687637 IN IP4 [local_ip]
      s=-
      c=IN IP4 [local_ip]
      t=0 0
      m=audio [auto_media_port] RTP/AVP 0
      a=rtpmap:0 PCMU/8000
    ]]>
  </send>

  <recv response="100" optional="true">
  </recv>

  <recv response="180" optional="true">
  </recv>

  <recv response="183" optional="true">
  </recv>

  <recv response="200" rtd="true">
  </recv>

  <send>
    <![CDATA[
      ACK sip:999@[remote_ip] SIP/2.0
      Via: SIP/2.0/UDP [local_ip]:[local_port];branch=[branch]
      From: <sip:keyman@[remote_ip]>;tag=1
      To: <sip:999@[remote_ip]>[peer_tag_param]
      Call-ID: [call_id]
      CSeq: 1 ACK
      Contact: <sip:keyman@[local_ip]:[local_port]>
      Content-Length: 0
    ]]>
  </send>

  <pause milliseconds="2000"/>

  <send retrans="500">
    <![CDATA[
      BYE sip:999@[remote_ip] SIP/2.0
      Via: SIP/2.0/UDP [local_ip]:[local_port];branch=[branch]
      From: <sip:keyman@[remote_ip]>;tag=1
      To: <sip:999@[remote_ip]>[peer_tag_param]
      Call-ID: [call_id]
      CSeq: 2 BYE
      Contact: <sip:keyman@[local_ip]:[local_port]>
      Content-Length: 0
    ]]>
  </send>

  <recv response="200">
  </recv>
</scenario>
```

Run call test:
```bash
sipp -sf test_call_999.xml *************:5080 -m 1
```

### 3. Audio Capture Test

For audio testing, use the capture scenario and check for recorded files:
```bash
sipp -sf capture_audio.xml *************:5080 -m 1
ls -la *.wav
```

## Configuration Details

### User Credentials
- **Username**: keyman
- **Password**: keyman123
- **Domain**: ************* (your server IP)

### Service Endpoints
- **FreeSWITCH SIP**: sip:*************:5080
- **FreeSWITCH ESL**: *************:8021
- **LiveKit HTTP**: http://*************:7880
- **LiveKit SIP**: *************:5070
- **Redis**: *************:6379

### Test Extensions
- **999**: Echo/test service with TTS response
- **keyman**: Registered SIP user for testing

## Troubleshooting

### Common Issues

#### 1. Container Won't Start
```bash
# Check container logs
docker logs freeswitch-minimal
docker logs livekit

# Rebuild if needed
docker-compose -f docker-compose-external.yml down
docker-compose -f docker-compose-external.yml up --build -d
```

#### 2. SIP Registration Fails
- Check if port 5080 is accessible: `nc -zv ************* 5080`
- Verify UFW allows port 5080: `sudo ufw status`
- Check FreeSWITCH logs: `docker logs freeswitch-minimal`

#### 3. Authentication Issues
- Verify user credentials in freeswitch.xml
- Check domain name matches server IP
- Ensure password is correctly set

#### 4. Audio Problems
- Verify RTP ports 30000-30100 are open
- Check audio modules are loaded in FreeSWITCH
- Test with simple echo extension (999)

#### 5. Network Connectivity
```bash
# Test port connectivity
nc -zv ************* 5080  # FreeSWITCH SIP
nc -zv ************* 7880  # LiveKit HTTP
nc -zv ************* 5070  # LiveKit SIP

# Check container networking
docker exec freeswitch-minimal netstat -tuln
```

### Health Checks

#### Service Status Commands
```bash
# Quick health check
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# FreeSWITCH CLI access
docker exec -it freeswitch-minimal fs_cli

# In FreeSWITCH CLI:
# show registrations
# sofia status
# show calls
```

#### Log Monitoring
```bash
# Real-time log monitoring
docker logs -f freeswitch-minimal
docker logs -f livekit
docker logs -f livekit-sip
```

## Maintenance

### Regular Tasks
1. Monitor container health: `docker ps`
2. Check disk space for logs
3. Backup configuration files
4. Update containers when needed

### Configuration Updates
1. Stop services: `docker-compose -f docker-compose-external.yml down`
2. Update configuration files
3. Rebuild and restart: `docker-compose -f docker-compose-external.yml up --build -d`
4. Run tests to verify functionality

## Success Criteria

A successful deployment should show:
1. ✅ All containers running and healthy
2. ✅ SIP registration returns 200 OK
3. ✅ Ports 5080, 7880, 5070 accessible
4. ✅ FreeSWITCH shows loaded audio modules
5. ✅ LiveKit services connected and operational
6. ✅ Redis accepting connections and saving data

## Debug on Local

### Local Development Setup

You can debug your agent locally while connecting to the deployed external LiveKit server. This allows for faster development cycles without rebuilding containers.

#### Prerequisites
- PDM package manager installed
- Python 3.12
- Agent source code (`src/main.py`)

#### Configuration Steps

1. **Update Local Environment Variables**
   
   Edit your local `.env` file to point to the external server:
   ```bash
   # LiveKit Configuration for External Server
   LIVEKIT_URL=ws://32016-51127.bacloud.info:7880
   LIVEKIT_API_KEY=lk_2f7c8d9a1b3e4f6g
   LIVEKIT_API_SECRET=lk_3h9k1m2n4p6q8r0s5t7v9w0x2z4y6a8b
   
   # Comment out local/docker configurations
   #LIVEKIT_URL=ws://livekit:7880
   #LIVEKIT_URL=ws://localhost:7880
   ```

2. **Install Dependencies**
   ```bash
   # Install all dependencies using PDM
   pdm sync
   ```

3. **Verify Configuration**
   ```bash
   # Test configuration loading
   cd src && python -c "from app.config import get_config; config = get_config(); print(f'LiveKit URL: {config.livekit.url}'); print(f'API Key: {config.livekit.api_key}')"
   ```

4. **Test Network Connectivity**
   ```bash
   # Verify external server is reachable
   nc -zv 32016-51127.bacloud.info 7880
   ```

#### Running the Agent Locally

1. **Start the Agent**
   ```bash
   # Run with default agent (emma.json)
   pdm run python src/main.py dev
   
   # Or specify a different agent
   pdm run python src/main.py dev --agent noah.json
   ```

2. **Expected Output**
   ```
   INFO: registered worker {"id": "AW_XYZ123", "url": "ws://32016-51127.bacloud.info:7880", "region": "", "protocol": 15}
   ```

3. **Debug Interface**
   The agent provides a local debugging interface at `http://localhost:64144/debug` when running in development mode.

#### Available Agent Configurations
- `emma.json` - Default agent
- `noah.json` - Alternative agent configuration  
- `richard.json` - Alternative agent configuration
- `amber.json` - Alternative agent configuration

#### Debugging Tips

1. **Check Agent Status**
   ```bash
   # Monitor agent logs in real-time
   pdm run python src/main.py dev --log-level DEBUG
   ```

2. **Common Environment Variables**
   Ensure these are properly set in your `.env`:
   ```bash
   # Core APIs
   CORE_API_URL=https://dev.slife.guru
   CONVERSATION_API_URL=https://dev.slife.guru
   
   # Speech-to-Text
   STT_PROVIDER=deepgram
   DEEPGRAM_API_KEY=your_key_here
   
   # Text-to-Speech  
   VOICE_PROVIDER=playai
   ELEVENLABS_API_KEY=your_key_here
   PLAYHT_API_KEY=your_key_here
   
   # LLM
   OPENAI_API_KEY=your_key_here
   LLM_MODEL=gpt-4o
   ```

3. **Troubleshooting Connection Issues**
   ```bash
   # Test DNS resolution
   nslookup 32016-51127.bacloud.info
   
   # Test port connectivity  
   telnet 32016-51127.bacloud.info 7880
   
   # Check firewall rules on server
   sudo ufw status
   ```

4. **Agent Metadata Files**
   Agent configurations are stored in `src/meta_test/`:
   - `emma.json` - Banking agent configuration
   - `noah.json` - Custom agent configuration
   - `richard.json` - Custom agent configuration  
   - `amber.json` - Custom agent configuration

#### Development Workflow

1. **Make Code Changes** - Edit files in `src/` directory
2. **Test Locally** - Run agent with `pdm run python src/main.py dev`
3. **Debug Issues** - Use debug interface at `http://localhost:64144/debug`
4. **Deploy Changes** - Update deployed containers when ready

This setup allows you to develop and test your agent locally while using the production LiveKit infrastructure, significantly speeding up the development process.

## Support Information

- **Configuration files**: `serve/freeswitch-minimal/freeswitch.xml`
- **Docker compose**: `docker-compose-external.yml`
- **Test scenarios**: `test_register.xml`, `test_call_999.xml`
- **Server IP**: Replace `*************` with your server's external IP
- **Default credentials**: keyman/keyman123