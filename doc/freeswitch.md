========================
CODE SNIPPETS
========================
TITLE: Example Output: show dialplan Command
DESCRIPTION: This output from the `show dialplan` command displays the type, name, and internal key (ikey) for each loaded dialplan module.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/index.mdx#_snippet_1

LANGUAGE: text
CODE:
```
type,name,ikey
dialplan,LUA,mod_lua
dialplan,XML,mod_dialplan_xml
dialplan,asterisk,mod_dialplan_asterisk
dialplan,inline,mod_dptools
dialplan,signalwire,mod_signalwire

5 total.

```

----------------------------------------

TITLE: Execute Show Info FreeSWITCH Dialplan
DESCRIPTION: This dialplan entry executes the 'info' application and then disconnects the call. Output is visible on the FreeSWITCH console.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/Default-Dialplan-QRF_9634422.mdx#_snippet_49

LANGUAGE: Dialplan
CODE:
```
9192
```

----------------------------------------

TITLE: Listing Loaded Dialplans (fs_cli)
DESCRIPTION: Use the `show dialplan` command on the FreeSWITCH command line interface (fs_cli) to list the currently loaded dialplan modules and their associated modules.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/index.mdx#_snippet_0

LANGUAGE: cli
CODE:
```
show dialplan
```

----------------------------------------

TITLE: Show Dialplan
DESCRIPTION: Displays the current dialplan configuration.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Interoperability/Software-Interfaces/Asterisk/Rosetta-Stone_1966991.mdx#_snippet_14

LANGUAGE: Asterisk Console
CODE:
```
dialplan show
```

LANGUAGE: FreeSWITCH CLI
CODE:
```
xml_locate dialplan
```

----------------------------------------

TITLE: SIP Profile with Inline Dialplan Parameter (XML)
DESCRIPTION: Shows how to configure the 'dialplan' parameter in a FreeSWITCH SIP profile to use an inline dialplan directly, executing specified applications.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod-dptools/6586667.mdx#_snippet_7

LANGUAGE: XML
CODE:
```
<param name="dialplan" value="inline:playback:woohoo.wav,hangup:BUHbye"/>
```

----------------------------------------

TITLE: Calling Lua Confirmation Script (Dialplan)
DESCRIPTION: This FreeSWITCH dialplan snippet shows how to configure the dialplan to execute the Lua confirmation script. It sets the 'group_confirm_key' to 'exec' and 'group_confirm_file' to the path of the Lua script, allowing the script to be triggered for call confirmation.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/Dialplan-FollowMe_9634428.mdx#_snippet_6

LANGUAGE: XML
CODE:
```
<action application="set" data="group_confirm_key=exec"/>
<action application="set" data="group_confirm_file=lua B_leg_confirm.lua"/>
```

----------------------------------------

TITLE: Asterisk IVR Dialplan Example
DESCRIPTION: Demonstrates a simple IVR configuration in Asterisk's dialplan format, showing how it handles initial prompts, waits for DTMF, and routes calls based on input.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Interoperability/Software-Interfaces/Asterisk/Convert-Asterisk-Dialplans-to-FreeSWITCH/index.mdx#_snippet_0

LANGUAGE: XML
CODE:
```
[IVR-2617559]
include => queue-inject
exten => s,1,Background(IVR/00200003/brand)
exten => s,n,Background(IVR/00200003/greeting)
exten => s,n,WaitExten(5)
exten => s,n,Goto(3,1)

exten => 2,1,Playback(IVR/00200003/ivr2)
exten => 2,n,Set(IVROPT=DSL Issue)
exten => 2,n,Playback(IVR/callmonitoring)
exten => 2,n,Queue(L2-00200003)
exten => 2,n,Hangup()

exten => 3,1,Playback(IVR/00200003/ivr3)
exten => 3,n,Set(IVROPT=Dial-up Support)
exten => 3,n,Playback(IVR/callmonitoring)
exten => 3,n,Queue(L1-00200003)
exten => 3,n,Hangup()

exten => i,1,Goto(s,2)
```

----------------------------------------

TITLE: Execute Lua Script from Dialplan - FreeSWITCH Dialplan XML
DESCRIPTION: This XML fragment shows the simple dialplan action required to execute an external Lua script. The `lua` application is used with the script filename (`test.lua`) as data.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod-dptools/6586414.mdx#_snippet_11

LANGUAGE: XML
CODE:
```
 <action application="lua" data="test.lua"/>
```

----------------------------------------

TITLE: Transferring Call from XML Dialplan to Lua Dialplan (XML)
DESCRIPTION: Shows an XML dialplan action that uses the transfer application to direct the call to a specific extension (123) which will be handled by a Lua script (some-dialplan.lua).

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod-lua/Serving-Configuration-with-Lua_3965134.mdx#_snippet_15

LANGUAGE: xml
CODE:
```
<action application="transfer" data="123 LUA some-dialplan.lua"/>
```

----------------------------------------

TITLE: Basic Inline Dialplan Syntax - FreeSWITCH Dialplan
DESCRIPTION: Shows the fundamental comma-separated 'app:arg' structure for defining a sequence of applications and their arguments directly within a command or configuration.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/Inline-Dialplan_13173434.mdx#_snippet_0

LANGUAGE: FreeSWITCH Dialplan
CODE:
```
'app1:arg1,app2:arg2,app3:arg3' inline
```

----------------------------------------

TITLE: XML Dialplan Command to Run Lua Script
DESCRIPTION: This XML dialplan snippet shows how to execute the `dialer.lua` script from within a FreeSWITCH dialplan using the `luarun` application, passing the destination number as an argument.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod_com_amd_4653131.mdx#_snippet_29

LANGUAGE: XML
CODE:
```
luarun dialer.lua 55552222
```

----------------------------------------

TITLE: Default SIP Profile Dialplan Setting - XML
DESCRIPTION: Shows the standard XML configuration parameter within a FreeSWITCH SIP profile that specifies the use of the main XML dialplan.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/Inline-Dialplan_13173434.mdx#_snippet_7

LANGUAGE: XML
CODE:
```
<param name="dialplan" value="XML"/>
```

----------------------------------------

TITLE: Originate with Inline B-Leg Dialplan (XML)
DESCRIPTION: Shows how to use the originate command where the dialplan for the B-leg is defined inline, executing applications like setting variables and playing audio.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod-dptools/6586667.mdx#_snippet_4

LANGUAGE: XML
CODE:
```
originate sofia/gateway/my_gw/<EMAIL> 'set:myvar=myvalue,info:,playback:foo.wav' inline
```

----------------------------------------

TITLE: Setting SIP Diversion Header (FreeSWITCH Dialplan)
DESCRIPTION: Shows how to set the SIP Diversion header using the `export` application within the dialplan, including the diversion URI and reason, typically used for call forwarding scenarios.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/XML-Dialplan/index.mdx#_snippet_78

LANGUAGE: xml
CODE:
```
<action application="export" data="sip_h_Diversion=<sip:2134445555@*******>;reason=unavailable"/>
```

----------------------------------------

TITLE: Originate with Inline B-Leg Dialplan - FreeSWITCH Dialplan
DESCRIPTION: Shows how to use the `originate` command where the dialplan logic for the destination leg (B-leg) is specified inline, including setting a channel variable and playing audio.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/Inline-Dialplan_13173434.mdx#_snippet_5

LANGUAGE: FreeSWITCH Dialplan
CODE:
```
originate sofia/gateway/my_gw/<EMAIL> 'set:myvar=myvalue,info:,playback:foo.wav' inline
```

----------------------------------------

TITLE: Invoking mod_lcr as a Dialplan Application
DESCRIPTION: Shows how to execute mod_lcr as a standalone application within the FreeSWITCH dialplan to determine the route, followed by bridging using the resulting channel variable.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod_lcr_6587457.mdx#_snippet_2

LANGUAGE: FreeSWITCH XML Dialplan
CODE:
```
<action application="lcr" data="$1 [$profile]"/>
<action application="bridge" data="${lcr_auto_route}"/>
```

----------------------------------------

TITLE: uuid_transfer with Inline Dialplan - FreeSWITCH Dialplan
DESCRIPTION: Shows how to use the `uuid_transfer` command with an inline dialplan containing multiple applications (`set`, `playback`) that will be executed on the channel after the transfer.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/Inline-Dialplan_13173434.mdx#_snippet_3

LANGUAGE: FreeSWITCH Dialplan
CODE:
```
uuid_transfer 2bde6598-0f1a-48fe-80bc-a457a31b0055 'set:test=blah blah,playback:foo.wav' inline
```

----------------------------------------

TITLE: Dialplan Example Before Sofia Contact Lookup
DESCRIPTION: Shows an excerpt from a FreeSWITCH dialplan (default.xml) demonstrating a typical bridge action for an intercom extension before implementing the sofia_contact lookup for multiple registrations.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Configuration/Sofia-SIP-Stack/index.mdx#_snippet_54

LANGUAGE: XML
CODE:
```
<extension name="extension-intercom">
   <condition field="destination_number" expression="^8(10[01][0-9])$">
      <action application="set" data="dialed_extension=$1"/>
      <action application="export" data="sip_auto_answer=true"/>
      <!-- needed for multiple-registrations=true and multi extension ringing -->
      <!-- <action application="bridge" data="user/${dialed_extension}@${domain_name}"/> -->
      <action application="bridge" data="${sofia_contact(${dialed_extension})}"/>
   </condition>
</extension>
```

----------------------------------------

TITLE: FreeSWITCH Dialplan Configuration for mod_managed
DESCRIPTION: Shows how to configure a FreeSWITCH dialplan extension to route calls to a managed application using the `managed` application and specifying the class name.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod_managed_13173447.mdx#_snippet_7

LANGUAGE: XML
CODE:
```
<extension name="testapp">
  <condition field="destination_number" expression="^(1024)">
     <action application="managed" data="yourclassname"/>
  </condition>
</extension>
```

----------------------------------------

TITLE: Example FreeSWITCH XML Dialplan Context
DESCRIPTION: Illustrates the basic structure of a FreeSWITCH XML dialplan context containing multiple extensions. Shows how conditions match destination numbers and execute actions like bridging calls, answering, sleeping, and transferring to voicemail.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/XML-Dialplan-archive_6586601.mdx#_snippet_2

LANGUAGE: XML
CODE:
```
<context name="example">
    <extension name="500">
        <condition field="destination_number" expression="^500$">
            <action application="bridge" data="user/500"/> 
        </condition>
    </extension>
 
    <extension name="501">
        <condition field="destination_number" expression="^501$">
            <action application="bridge" data="user/501"/>
            <action application="answer"/>
            <action application="sleep" data="1000"/>
            <action application="bridge" data="loopback/app=voicemail:default ${domain_name} ${dialed_extension}"/>
        </condition>
    </extension>
</context>
```

----------------------------------------

TITLE: Full FreeSWITCH XML Dialplan Document Structure
DESCRIPTION: Shows the complete XML wrapper for a FreeSWITCH dialplan document, including the document type, section, and a default context. Provides the standard boilerplate for defining dialplan contexts.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/XML-Dialplan-archive_6586601.mdx#_snippet_3

LANGUAGE: XML
CODE:
```
<?xml version="1.0"?>
    <document type="freeswitch/xml">
        <section name="dialplan" description="Regex/XML Dialplan">
        <!-- the default context is a safe start -->
            <context name="default">
                <!-- one or more extension tags -->
            </context>
            <!-- more optional contexts -->
        </section>
    </document>
```

----------------------------------------

TITLE: Inline Dialplan with Single App (XML)
DESCRIPTION: Shows an example where single quotes are not required for an inline dialplan consisting of a single application and argument pair without spaces.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod-dptools/6586667.mdx#_snippet_1

LANGUAGE: XML
CODE:
```
uuid_transfer <uuid> playback:/foo.wav inline
```

----------------------------------------

TITLE: FreeSWITCH: Call Screening Dialplan (XML)
DESCRIPTION: This dialplan extension screens incoming calls by asking the caller for a name, announcing it to the called party, and allowing the called party to accept or reject the call. If rejected, the caller is sent to voicemail.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/XML-Dialplan-archive_6586601.mdx#_snippet_50

LANGUAGE: XML
CODE:
```
<extension name="screen">
   <condition field="destination_number" expression="^(\d{4})$">
     <action application="set" data="call_screen_filename=/tmp/${caller_id_number}-name.wav"/>
     <action application="set" data="hangup_after_bridge=true" />
     <action application="answer"/>
     <action application="sleep" data="1000"/>
     <action application="phrase" data="voicemail_record_name"/>
     <action application="playback" data="tone_stream://%(500, 0, 640)"/>
     <action application="set" data="playback_terminators=#*0123456789"/>
     <action application="record" data="${call_screen_filename} 7 200 2"/>
     <action application="set" data="group_confirm_key=1"/>
     <action application="set" data="fail_on_single_reject=true"/>
     <action application="set" data="group_confirm_file=phrase:screen_confirm:${call_screen_filename}"/>
     <action application="set" data="continue_on_fail=true"/>
     <action application="bridge" data="user/$1"/>
     <action application="voicemail" data="default $${domain} $1"/>
     <action application="hangup"/>
   </condition>
 </extension>
```

----------------------------------------

TITLE: Standard SIP Profile Dialplan Parameter (XML)
DESCRIPTION: Displays the default configuration for the 'dialplan' parameter within a FreeSWITCH SIP profile, typically set to use XML dialplan.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod-dptools/6586667.mdx#_snippet_6

LANGUAGE: XML
CODE:
```
<param name="dialplan" value="XML"/>
```

----------------------------------------

TITLE: Using presence Application in Dialplan XML
DESCRIPTION: Shows the basic syntax for using the presence dialplan application. It takes the user, Rich Presence Information Document (RPID), and a message as arguments.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod-dptools/6586515.mdx#_snippet_0

LANGUAGE: XML
CODE:
```
presence <user> <rpid> <message>
```

----------------------------------------

TITLE: Using pre_answer in XML Dialplan
DESCRIPTION: This snippet shows the basic syntax for invoking the `pre_answer` dialplan application within an XML dialplan context. It establishes early media, allowing audio to flow before the call is officially answered.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod-dptools/6586998.mdx#_snippet_0

LANGUAGE: XML
CODE:
```
<action application="pre_answer"/>
```

----------------------------------------

TITLE: Dial by SIP URI - FreeSWITCH Dialplan
DESCRIPTION: Allows dialing a destination using a full SIP URI format (e.g., sip:<EMAIL>).

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/Default-Dialplan-QRF_9634422.mdx#_snippet_20

LANGUAGE: FreeSWITCH Dialplan
CODE:
```
sip:<EMAIL>
```

----------------------------------------

TITLE: FreeSWITCH Dialplan Extension Configuration
DESCRIPTION: This snippet shows the applications configured within the matching dialplan extension definition ('outside_call' with number '003468888646444') as found in the callflow section of the CDR. This represents the intended application flow defined in the dialplan.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Configuration/CDR/index.mdx#_snippet_3

LANGUAGE: XML
CODE:
```
<application app_name="set" app_data="outside_call=true"></application>
<application app_name="export" app_data="RFC2822_DATE=${strftime(%a, %d %b %Y %T %z)}"></application>
```

----------------------------------------

TITLE: Basic ring_ready Application Usage (XML)
DESCRIPTION: Shows the minimal syntax for using the `ring_ready` dialplan application within a FreeSWITCH dialplan action.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod-dptools/6586994.mdx#_snippet_0

LANGUAGE: XML
CODE:
```
ring_ready
```

----------------------------------------

TITLE: Creating a Basic Extension Template in FreeSWITCH XML Dialplan
DESCRIPTION: Shows the general structure of an extension within a FreeSWITCH XML dialplan context. Extensions define call destinations and routing logic. They contain one or more `condition` tags.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/XML-Dialplan/index.mdx#_snippet_3

LANGUAGE: XML
CODE:
```
<extension name="Your extension name here">
  <condition/>
  <condition...
    <action .../>
    <anti-action .../>
  </condition>
</extension>
```

----------------------------------------

TITLE: Configure Voicemail Callback Dialplan
DESCRIPTION: This parameter specifies the dialplan used for voicemail callbacks. The example sets it to 'XML'.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod_voicemail_6587070.mdx#_snippet_63

LANGUAGE: XML
CODE:
```
<param name="callback-dialplan" value="XML"/>
```

----------------------------------------

TITLE: Routing Inbound Fax Calls in FreeSWITCH Public Dialplan XML
DESCRIPTION: This FreeSWITCH public dialplan example shows how to route incoming calls intended for a specific fax number to the predefined fax_receive extension in the default dialplan. It sets the domain name variable and then transfers the call to the target extension (9178) within the default context.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/XML-Dialplan/index.mdx#_snippet_56

LANGUAGE: XML
CODE:
```
<include>
    <extension name="incoming-fax">
        <condition field="destination_number" expression="^$${local_fax_number}$">
            <action application="set" data="domain_name=$${domain}"/>
            <action application="transfer" data="9178 XML default"/>
        </condition>
    </extension>
</include>
```

----------------------------------------

TITLE: Dialplan Example Passing Variable to JavaScript
DESCRIPTION: An XML Dialplan example showing how to pass a channel variable (caller_id_number) as an argument to a JavaScript script executed via the 'javascript' application.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Client-and-Developer-Interfaces/JavaScript/index.mdx#_snippet_3

LANGUAGE: XML
CODE:
```
<extension ...>
  <condition ...>
     <action application="javascript" data="script.js ${caller_id_number}"/>
   </condition>
 </extension>
```

----------------------------------------

TITLE: Call Support Group - FreeSWITCH Dialplan
DESCRIPTION: Dials the literal string 2001 to route the call to the 'support' call group, ringing all members defined in the directory.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/Default-Dialplan-QRF_9634422.mdx#_snippet_16

LANGUAGE: FreeSWITCH Dialplan
CODE:
```
2001
```

----------------------------------------

TITLE: Transferring Call from Lua Dialplan to XML Dialplan (Lua)
DESCRIPTION: Shows a Lua code snippet that adds a transfer action to the ACTIONS table, directing the call to a specific extension (123) within an XML dialplan context (some-context).

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod-lua/Serving-Configuration-with-Lua_3965134.mdx#_snippet_14

LANGUAGE: lua
CODE:
```
table.insert(ACTIONS, {"transfer", "123 XML some-context"})
```

----------------------------------------

TITLE: Dialplan Action Using Sofia Contact Lookup
DESCRIPTION: Illustrates the specific dialplan action that utilizes the sofia_contact lookup function to retrieve all registered contacts for a given extension, enabling simultaneous ringing of multiple devices.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Configuration/Sofia-SIP-Stack/index.mdx#_snippet_55

LANGUAGE: XML
CODE:
```
<action application="bridge" data="${sofia_contact(${dialed_extension})}"/>
```

----------------------------------------

TITLE: Setting Dialplan to ENUM in FreeSWITCH XML
DESCRIPTION: XML configuration snippet showing how to set the dialplan for an endpoint (e.g., a SIP profile) to 'enum', directing calls received by this endpoint to be processed by the ENUM dialplan interface.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod_enum_6587333.mdx#_snippet_2

LANGUAGE: XML
CODE:
```
<param name="dialplan" value="enum"/>
```

----------------------------------------

TITLE: Call Sales Group - FreeSWITCH Dialplan
DESCRIPTION: Dials the literal string 2000 to route the call to the 'sales' call group, ringing all members defined in the directory.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/Default-Dialplan-QRF_9634422.mdx#_snippet_15

LANGUAGE: FreeSWITCH Dialplan
CODE:
```
2000
```

----------------------------------------

TITLE: Using wait_for_answer in FreeSWITCH Dialplan
DESCRIPTION: This snippet shows the basic usage of the wait_for_answer application within a FreeSWITCH XML dialplan. It pauses the dialplan execution until the call session is answered.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod-dptools/6586885.mdx#_snippet_0

LANGUAGE: XML
CODE:
```
<action application="wait_for_answer"/>
```

----------------------------------------

TITLE: Loopback to Extension, Context, and Dialplan
DESCRIPTION: Example showing how to specify both a context (e.g., companyA) and a dialplan (e.g., XML) when using the loopback endpoint, routing to extension 1000 within that specific context and dialplan.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/Loopback-Endpoint_6587541.mdx#_snippet_4

LANGUAGE: FreeSWITCH Dialplan/CLI
CODE:
```
loopback/1000/companyA/XML
```

----------------------------------------

TITLE: Setting Dialplan Parameter for Translation (XML)
DESCRIPTION: FreeSWITCH profile parameter configuration to specify the dialplan execution order. Setting 'dialplan' to 'translate,XML' ensures that the mod_translate module processes the call details before the standard XML dialplan is consulted.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod_translate_10682835.mdx#_snippet_5

LANGUAGE: xml
CODE:
```
<param name="dialplan" value="translate,XML"/>
```

----------------------------------------

TITLE: Speaking Clock using Flite - FreeSWITCH Dialplan XML
DESCRIPTION: Demonstrates how to use the Flite text-to-speech engine to speak the current time when a specific destination number (2910) is called. Requires the 'mod_flite' module and uses the 'speak' application.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/XML-Dialplan/index.mdx#_snippet_54

LANGUAGE: XML
CODE:
```
<include>
    <extension name="SpeakTime">
        <condition field="destination_number" expression="^2910$">
            <action application="set" data="actime=${strftime(%H:%M)}"/>
            <action application="set" data="tts_engine=flite"/>
            <action application="set" data="tts_voice=slt"/>
            <action application="speak" data="It is +${actime}"/>
        </condition>
    </extension>
</include>
```

----------------------------------------

TITLE: Test Dialplan via Loopback (Basic) - FreeSWITCH CLI
DESCRIPTION: This FreeSWITCH command line example shows a basic method to test a dialplan extension using the `originate` command with the `loopback` endpoint. It simulates an incoming call to a specified `<destination number>` within a `<mycontext>`, immediately hanging up (`hangup`) after the dialplan execution completes (`inline`). This is useful for quickly verifying dialplan logic.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/XML-Dialplan-archive_6586601.mdx#_snippet_73

LANGUAGE: FreeSWITCH CLI
CODE:
```
originate loopback/<destination number>/<mycontext> hangup inline
```

----------------------------------------

TITLE: Matching Multiple Conditions (AND Logic) - FreeSWITCH Dialplan XML
DESCRIPTION: This FreeSWITCH dialplan extension shows how to implement AND logic by using multiple conditions within a single extension. Both the network address and the destination number must match for the actions within the last condition to be executed. It uses $0 to capture the entire matched destination number.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/XML-Dialplan-archive_6586601.mdx#_snippet_35

LANGUAGE: XML
CODE:
```
<extension name="Test2">
  <condition field="network_addr" expression="^192\.168\.1\.1$"/>
  <condition field="destination_number" expression="^1(\d+)$">
    <action application="bridge" data="sofia/profilename/$0@***********"/>
  </condition>
</extension>
```

----------------------------------------

TITLE: Single App Inline Dialplan Example - FreeSWITCH Dialplan
DESCRIPTION: Demonstrates a simple inline dialplan with a single application and argument, illustrating a case where single quotes might be optional if there are no spaces.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/Inline-Dialplan_13173434.mdx#_snippet_1

LANGUAGE: FreeSWITCH Dialplan
CODE:
```
uuid_transfer <uuid> playback:/foo.wav inline
```

----------------------------------------

TITLE: Executing Inline Actions in XML Dialplan
DESCRIPTION: This snippet shows how to use the 'inline="true"' attribute on an action. This causes the action to be executed during the hunting phase of the dialplan, allowing variables set by this action to be available for subsequent conditions.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/XML-Dialplan/index.mdx#_snippet_32

LANGUAGE: XML
CODE:
```
<action inline="true" application="set" data="some_var=some_val"/>
```

----------------------------------------

TITLE: Dialplan Configuration
DESCRIPTION: Defines the phone's dialplan rules using a digitmap. Specifies patterns for matching dialed numbers and sets a timeout for digit collection.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Interoperability/Phones/Polycom/Polycom-Configuration_1967052.mdx#_snippet_11

LANGUAGE: XML
CODE:
```
  <dialplan>
  <dialplan.impossibleMatchHandling="2" dialplan.removeEndOfDial="1">
      <digitmap dialplan.digitmap="8xxxx|2xxxx|6xxxx|0[23489]xxxxxxx|0[57]xxxxxxxx|1[78]00xxxxxx|*8|*xxxx|1[069]x|1234|00x.T" dialplan.digitmap.timeOut="3"/>
   </dialplan>
```

----------------------------------------

TITLE: FreeSWITCH Dialplan Nested Conditions XML
DESCRIPTION: Demonstrates execution flow in FreeSWITCH dialplan with nested conditions where the outer condition has `require-nested="false"`. Shows how actions outside the nested condition execute before the nested actions.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/XML-Dialplan/index.mdx#_snippet_21

LANGUAGE: XML
CODE:
```
<extension name="nested example">
  <condition field="destination_number" expression="^2901$" require-nested="false">
    <action application="log" data="ERR 1 Nest Level 1a"/>
    <action application="log" data="ERR 2 Nest Level 1b"/>
    <condition field="${direction}" expression="inbound" break="never">
      <action application="log" data="ERR 3 Nest Level 2a"/>
      <action application="log" data="ERR 4 Nest Level 2b"/>
      <condition field="${direction}" expression="inbound" break="never">
        <action application="log" data="ERR 5 Nest Level 3a"/>
        <action application="log" data="ERR 6 Nest Level 3b"/>
      </condition>
      <action application="log" data="ERR 7 Nest Level 2c"/>
      <action application="log" data="ERR 8 Nest Level 2d"/>
    </condition>
    <action application="log" data="ERR 9 Nest Level 1c"/>
    <action application="log" data="ERR 10 Nest Level 1d"/>
  </condition>
</extension>
```

----------------------------------------

TITLE: FreeSWITCH XML IVR Dialplan Conversion
DESCRIPTION: Shows the equivalent IVR configuration in FreeSWITCH XML dialplan format, using conditions and applications like playback, play_and_get_digits, set, transfer, and fifo to replicate the Asterisk behavior.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Interoperability/Software-Interfaces/Asterisk/Convert-Asterisk-Dialplans-to-FreeSWITCH/index.mdx#_snippet_1

LANGUAGE: XML
CODE:
```
<include>
  <extension name="IVR Conversion">
    <condition field="destination_number" expression="^2617559$"/>
    <condition field="${ivrlevel}" expression="^$" break="never">
      <action application="set" data="domain_name=$${domain}"/>
      <action application="playback" data="IVR/00200003/brand.wav"/>
      <action application="play_and_get_digits" data="0 1 1 5000 # IVR/00200003/greeting.wav /invalid.wav ivrsel (2|3)"/>
      <action application="set" data="ivrlevel=2"/>
      <action application="transfer" data="${destination_number}"/>
    </condition>
    <condition field="${ivrlevel}-${ivrsel}" expression="^2-2$" break="never">
      <action application="playback" data="IVR/00200003/ivr2.wav"/>
      <action application="set" data="ivropt=DSL Issue"/>
      <action application="playback" data="IVR/callmonitoring.wav"/>
      <action application="fifo" data="L2-00200003 in /tmp/exit-message.wav /tmp/music-on-hold.wav"/>
    </condition>
    <condition field="${ivrlevel}-${ivrsel}" expression="^2-(3|)$">
      <action application="playback" data="IVR/00200003/ivr3.wav"/>
      <action application="set" data="ivropt=Dial-up Support"/>
      <action application="playback" data="IVR/callmonitoring.wav"/>
      <action application="fifo" data="L1-00200003 in /tmp/exit-message.wav /tmp/music-on-hold.wav"/>
    </condition>
  </extension>
</include>
```

----------------------------------------

TITLE: Basic Inline Dialplan Syntax (XML)
DESCRIPTION: Illustrates the fundamental syntax for defining an inline dialplan as a comma-separated list of application and argument pairs.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod-dptools/6586667.mdx#_snippet_0

LANGUAGE: XML
CODE:
```
'app1:arg1,app2:arg2,app3:arg3' inline
```

----------------------------------------

TITLE: FreeSWITCH Dialplan Integration (XML)
DESCRIPTION: This XML snippet shows how to integrate the dial_by_name_directory.js script into the FreeSWITCH dialplan by matching a destination number and executing the JavaScript application.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Configuration/Dial-by-name-directory_13173394.mdx#_snippet_0

LANGUAGE: XML
CODE:
```
<extension name="dial_by_name">
	<condition field="destination_number" expression="^6000$">
		<action application="javascript" data="dial_by_name_directory.js" />
	</condition>
</extension>
```

----------------------------------------

TITLE: FreeSWITCH Log Output Nested Conditions Example 1
DESCRIPTION: Shows the log output corresponding to the `nested example` FreeSWITCH dialplan, illustrating the execution order of actions in nested conditions with `require-nested="false"`.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/XML-Dialplan/index.mdx#_snippet_22

LANGUAGE: Plain text
CODE:
```
EXECUTE verto.rtc/2901 log(ERR 1 Nest Level 1a)
EXECUTE verto.rtc/2901 log(ERR 2 Nest Level 1b)
EXECUTE verto.rtc/2901 log(ERR 9 Nest Level 1c)
EXECUTE verto.rtc/2901 log(ERR 10 Nest Level 1d)
EXECUTE verto.rtc/2901 log(ERR 3 Nest Level 2a)
EXECUTE verto.rtc/2901 log(ERR 4 Nest Level 2b)
EXECUTE verto.rtc/2901 log(ERR 7 Nest Level 2c)
EXECUTE verto.rtc/2901 log(ERR 8 Nest Level 2d)
EXECUTE verto.rtc/2901 log(ERR 5 Nest Level 3a)
EXECUTE verto.rtc/2901 log(ERR 6 Nest Level 3b)
```

----------------------------------------

TITLE: Transferring Calls to mod_lcr in Dialplan
DESCRIPTION: Illustrates how to transfer a call to mod_lcr within the FreeSWITCH dialplan, allowing mod_lcr to handle the routing logic for the destination number.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod_lcr_6587457.mdx#_snippet_3

LANGUAGE: FreeSWITCH XML Dialplan
CODE:
```
<action application="transfer" data="$1 lcr"/>
```

----------------------------------------

TITLE: Testing Dialplan with Variables (FreeSWITCH Dialplan)
DESCRIPTION: Extends the loopback testing example by demonstrating how to set channel variables, such as `toll_allow`, before originating the call to simulate specific dialplan conditions.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/XML-Dialplan/index.mdx#_snippet_77

LANGUAGE: text
CODE:
```
originate {toll_allow=international}loopback/*************/default hangup inline
```

----------------------------------------

TITLE: Logging Messages in FreeSWITCH Dialplan XML
DESCRIPTION: Demonstrates how to use the 'log' application within a FreeSWITCH XML dialplan to output messages to the console. Shows examples with the default log level (DEBUG) and explicitly specifying INFO and DEBUG levels.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Troubleshooting-Debugging/Logging_1048649.mdx#_snippet_0

LANGUAGE: XML
CODE:
```
<action application="log" data="DIALING Extension DialURI [${sip_uri_to_dial}]"/>
<action application="log" data="INFO DIALING Extension DialURI [${sip_uri_to_dial}]"/>
<action application="log" data="DEBUG DIALING Extension DialURI [${sip_uri_to_dial}]"/>
```

----------------------------------------

TITLE: Dialplan API Call to Python
DESCRIPTION: Example FreeSWITCH dialplan action showing how to execute a Python script using the 'python' application and capture its output into a channel variable. The script's output is determined by what is written to the 'stream' object within the Python function.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod_python_1048940.mdx#_snippet_12

LANGUAGE: xml
CODE:
```
<action application="set" data="foo=${python(my_script)}"/>
```

----------------------------------------

TITLE: Basic FreeSWITCH XML Dialplan Extension Structure
DESCRIPTION: Outlines the fundamental structure of an extension element within a FreeSWITCH XML dialplan context. Shows the required 'name' attribute and the nesting of condition and action tags.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/XML-Dialplan-archive_6586601.mdx#_snippet_4

LANGUAGE: XML
CODE:
```
<extension name="Your extension name here">
    <condition(s)...
        <action(s) .../>
    </condition>
</extension>
```

----------------------------------------

TITLE: Handle calls matching no extension - FreeSWITCH Dialplan XML
DESCRIPTION: Shows how to create a catch-all extension at the bottom of the dialplan to handle calls that do not match any specific extension, typically playing an invalid extension message using the 'playback' application.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/XML-Dialplan/index.mdx#_snippet_51

LANGUAGE: XML
CODE:
```
<extension name="catchall">
    <condition field="destination_number" expression=".*" continue="true">
        <action application="playback" data="misc/invalid_extension.wav"/>
    </condition>
</extension>
```

----------------------------------------

TITLE: Example Dialplan Entry Using mod_directory in FreeSWITCH
DESCRIPTION: Demonstrates a FreeSWITCH dialplan extension that matches the destination number '411' and invokes the mod_directory application with the 'default' profile, using the current domain and transferring to the 'default' context upon selection.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod_directory_1049013.mdx#_snippet_3

LANGUAGE: XML
CODE:
```
<extension name="directory" continue="true">
 <condition field="destination_number" expression="^411$">
  <action application="directory" data="default $${domain} default"/> 
 </condition>
</extension>
```

----------------------------------------

TITLE: Setting and Using Variable Across Conditions - FreeSWITCH Dialplan XML
DESCRIPTION: This FreeSWITCH dialplan extension demonstrates how to correctly capture a variable (dialed number) in one condition using the 'set' application and then use that variable (${dialed_number}) in the action of a subsequent condition.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/XML-Dialplan-archive_6586601.mdx#_snippet_34

LANGUAGE: XML
CODE:
```
<extension name="Test1_2">
  <condition field="destination_number" expression="^(\d+)$">
    <action application="set" data="dialed_number=$1"/>
  </condition>
  <condition field="network_addr" expression="^192\.168\.1\.1$">
    <action application="bridge" data="sofia/profilename/${dialed_number}@***********"/>
  </condition>
</extension>
```

----------------------------------------

TITLE: Dialplan Action to Send Call to Voicemail
DESCRIPTION: This XML snippet shows a sequence of FreeSWITCH dialplan actions. It first answers the incoming call using the `answer` application. Then, it uses the `voicemail` application to send the call to voicemail, specifying the voicemail profile (`default`), the domain (`$${domain}`), and the dialed extension (`$1`). This is typically used when a called party is unavailable.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod_voicemail_6587070.mdx#_snippet_56

LANGUAGE: XML
CODE:
```
<action application="answer"/>
<action application="voicemail" data="default $${domain} $1"/>
```

----------------------------------------

TITLE: Changing SIP Contact User (FreeSWITCH Dialplan)
DESCRIPTION: Shows how to override the default SIP Contact user (mod_sofia) to a custom value (e.g., 'foo') by setting the `sip_contact_user` channel variable before originating a call.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/XML-Dialplan/index.mdx#_snippet_74

LANGUAGE: text
CODE:
```
{sip_contact_user=foo}sofia/my_profile/1234@***********;transport=tcp
```

----------------------------------------

TITLE: Configuring FreeSWITCH mod_xml_curl for Dialplan
DESCRIPTION: This XML configuration snippet shows how to set up mod_xml_curl in FreeSWITCH to fetch dialplan information. It defines a binding named 'dialplan fetcher' that instructs FreeSWITCH to make an HTTP request to 'http://localhost:8080/freeswitch/curl.fetch' whenever a dialplan lookup is required.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod-xml-curl/mod_xml_curl-C-sharp-example_1049005.mdx#_snippet_3

LANGUAGE: XML
CODE:
```
<configuration name=\"xml_curl.conf\" description=\"cURL XML Gateway\">\n  <bindings>\n    <binding name=\"dialplan fetcher\">\n      <param name=\"gateway-url\" value=\"http://localhost:8080/freeswitch/curl.fetch\" bindings=\"dialplan\"/>\n    </binding>\n  </bindings>\n</configuration>
```

----------------------------------------

TITLE: Basic Eavesdrop Dialplan Example - FreeSWITCH - XML Dialplan
DESCRIPTION: A complete dialplan example showing a 'global' extension to store caller information and an 'eavesdrop' extension that answers calls to 88 followed by an extension number, looks up the target channel UUID from a database, and starts eavesdropping.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod-dptools/6586529.mdx#_snippet_4

LANGUAGE: XML
CODE:
```
<extension name="global" continue="true">
  <condition>
    <action application="info"/>
    <action application="db" data="insert/spymap/${caller_id_number}/${uuid}"/>
    <action application="db" data="insert/last_dial/${caller_id_number}/${destination_number}"/>
    <action application="db" data="insert/last_dial/global/${uuid}"/>
  </condition>
</extension>
<extension name="eavesdrop">
  <condition field="destination_number" expression="^88(.*)$|^*0(.*)$">
    <action application="answer"/>
    <action application="eavesdrop" data="${db(select/spymap/$1$2)}"/>
  </condition>
</extension>
```

----------------------------------------

TITLE: Dial ClueCon Info FreeSWITCH Dialplan
DESCRIPTION: This dialplan entry connects the caller to someone who can provide information about ClueCon.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/Default-Dialplan-QRF_9634422.mdx#_snippet_48

LANGUAGE: Dialplan
CODE:
```
9191
```

----------------------------------------

TITLE: Intercept Ringing Call (Global) - FreeSWITCH Dialplan
DESCRIPTION: Matches the dial pattern 886 to perform a global intercept of any ringing call within the FreeSWITCH instance.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/Default-Dialplan-QRF_9634422.mdx#_snippet_0

LANGUAGE: FreeSWITCH Dialplan
CODE:
```
^886$
```

----------------------------------------

TITLE: Dial Demo IVR FreeSWITCH Dialplan
DESCRIPTION: This dialplan entry calls the included demo IVR application.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/Default-Dialplan-QRF_9634422.mdx#_snippet_29

LANGUAGE: Dialplan
CODE:
```
5000
```

----------------------------------------

TITLE: Using LCR for Endpoint Transfer in FreeSWITCH Dialplan
DESCRIPTION: Shows how to use the LCR module to determine the destination for a transfer action within the FreeSWITCH dialplan. It uses the digits captured by the condition.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod_lcr_6587457.mdx#_snippet_9

LANGUAGE: FreeSWITCH XML Dialplan
CODE:
```
<action application="transfer" data="$1 lcr"/>
```

----------------------------------------

TITLE: Routing Calls to sip-force-user via sofia_contact in FreeSWITCH Dialplan (XML)
DESCRIPTION: This XML snippet defines a dialplan extension that matches incoming calls based on the destination number. It uses the `sofia_contact` application to bridge the call to the user associated with the matched number and the domain, leveraging the `sip-force-user` setting in the directory. It includes actions for setting variables, bridging, answering, and handling voicemail. This is typically placed in `conf/dialplan/`.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Directory/XML-User-Directory/Contact-Mapping_7143467.mdx#_snippet_1

LANGUAGE: XML
CODE:
```
<!-- this is an example extension in conf/dialplan/ -->
<extension name="Call_User">
   <condition field="destination_number" expression="^(d+)$">
     <action application="set" data="call_timeout=20"/>
     <action application="set" data="hangup_after_bridge=true"/>
     <action application="set" data="continue_on_fail=true"/>
     <action application="bridge" data="${sofia_contact($1@$${domain})}"/>
     <action application="answer"/>
     <action application="set" data="vm_exten=$1" />
     <action application="sleep" data="1000"/>
     <action application="voicemail" data="default ${domain_name} $1"/>
   </condition>
</extension>
```

----------------------------------------

TITLE: FreeSWITCH Dialplan: Operator Extension (XML)
DESCRIPTION: This FreeSWITCH dialplan extension handles calls to *operator or 0. It sets the ringback music and then transfers the call to extension 1000 within the features context, typically used for an operator or attendant.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Auxiliary-Knowledge-and-Utilities/Multi-home-tutorial/index.mdx#_snippet_20

LANGUAGE: XML
CODE:
```
<!-- voicemail operator extension -->
    <extension name="operator">
      <condition field="destination_number" expression="^\*operator$|^0$">
        <action application="set" data="transfer_ringback=$${hold_music}"/>
        <action application="transfer" data="1000 XML features"/>
      </condition>
    </extension>
```

----------------------------------------

TITLE: Examples of mkdir dptool in XML Dialplan
DESCRIPTION: These examples demonstrate how to use the 'mkdir' dialplan application within a FreeSWITCH XML dialplan. They show creating a static directory path and a dynamic path based on the current date using variable expansion.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod-dptools/6586626.mdx#_snippet_1

LANGUAGE: XML
CODE:
```
<action application="mkdir" data="/path/to/new/directory"/>
<action application="mkdir" data="${recordings_dir}/archive/${strftime(%Y)}/${strftime(%b)}/${strftime(%d)}/"/>
<action application="mkdir" data="/usr/local/freeswitch/sounds/custom/blabla"/>
```

----------------------------------------

TITLE: Prepare FreeSWITCH Dialplan File - Bash
DESCRIPTION: This bash script changes the current directory to the FreeSWITCH dialplan configuration directory and creates a new file named `internal.xml`. This file will contain the dial plan rules for the internal context.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Client-and-Developer-Interfaces/Lua-API-Reference/Lua-examples/Lua-ASR-TTS-Directory-example_1049011.mdx#_snippet_6

LANGUAGE: bash
CODE:
```
#!/bin/bash
cd /usr/local/freeswitch/conf/dialplan
touch internal.xml
```

----------------------------------------

TITLE: Basic Bridge Dialplan Action
DESCRIPTION: This XML snippet shows the basic structure for using the 'bridge' dialplan application within a FreeSWITCH dialplan context. It specifies the application name and the data argument which defines the target endpoint.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod-dptools/6586484.mdx#_snippet_0

LANGUAGE: XML
CODE:
```
<action application="bridge" data="endpoint/gateway/gateway_name/address"/>
```

----------------------------------------

TITLE: Setting mod_fifo Consumer Variables in Dialplan (XML)
DESCRIPTION: Shows a FreeSWITCH dialplan extension that a consumer can dial to retrieve a caller from a fifo. It uses the 'set' application to configure 'fifo_record_template' and then enters the fifo using the 'fifo' application, also setting 'nowait'.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod_fifo_3966031.mdx#_snippet_27

LANGUAGE: XML
CODE:
```
 <extension name="Get_Fifo_Call">
   <condition field="destination_number" expression="^56789$"?
     <action application="set" data="fifo_record_template=$${base_dir}/recordings/myfifo_call.wav"/>
     <action application="answer"/>
     <action application="fifo" data="MyFifo@$${domain} out nowait"/>
   </condition>
 </extension>
```

----------------------------------------

TITLE: Set Variable with cond (Dialplan XML)
DESCRIPTION: Shows how to use the `cond` dialplan application within an `<action>` tag to set a channel variable (`voicemail_authorized`) based on the evaluation of a simple condition (`sip_authorized == true`).

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod_commands_1966741.mdx#_snippet_15

LANGUAGE: FreeSWITCH Dialplan XML
CODE:
```
<action application="set" data="voicemail_authorized=${cond(${sip_authorized} == true ? true : false)}"/>
```

----------------------------------------

TITLE: FreeSWITCH Log Output Nested Conditions Example 2
DESCRIPTION: Shows the log output corresponding to the `another nested example` FreeSWITCH dialplan, illustrating the sequential execution of multiple nested conditions.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/XML-Dialplan/index.mdx#_snippet_24

LANGUAGE: Plain text
CODE:
```
EXECUTE verto.rtc/2902 log(ERR 1 Nest Level 1a)
EXECUTE verto.rtc/2902 log(ERR 2 Nest Level 1b)
EXECUTE verto.rtc/2902 log(ERR 3 Nest Level 2a)
EXECUTE verto.rtc/2902 log(ERR 4 Nest Level 2b)
EXECUTE verto.rtc/2902 log(ERR 5 Nest Level 3a)
EXECUTE verto.rtc/2902 log(ERR 6 Nest Level 3b)
EXECUTE verto.rtc/2902 log(ERR 7 Nest Level 2c)
EXECUTE verto.rtc/2902 log(ERR 8 Nest Level 2d)
EXECUTE verto.rtc/2902 log(ERR 9 Nest Level 1c)
EXECUTE verto.rtc/2902 log(ERR 10 Nest Level 1d)
```

----------------------------------------

TITLE: Configuring FreeSWITCH SIP Profile for Lua Dialplan (XML)
DESCRIPTION: Shows how to configure a FreeSWITCH SIP profile (phones) to use a Lua script (dialplan-from-phones.lua) instead of a static XML file for dialplan processing.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod-lua/Serving-Configuration-with-Lua_3965134.mdx#_snippet_12

LANGUAGE: xml
CODE:
```
<profile name="phones">
  <!-- ... -->
  <settings>
    <param name="dialplan" value="LUA"/>
    <param name="context" value="dialplan-from-phones.lua"/>
    <!-- ... -->
  </settings>
</profile>
```

----------------------------------------

TITLE: FreeSWITCH FollowMe Dialplan - Ring Multiple Targets (XML)
DESCRIPTION: This FreeSWITCH dialplan XML snippet configures a FollowMe extension (destination '0') that attempts to ring multiple targets sequentially. It first tries a set of internal VoIP extensions for 12 seconds, then attempts to ring the same VoIP extensions concurrently with an external cell phone gateway for 60 seconds. It utilizes bind_meta_app to enable features like transferring or recording the call and includes speak and sleep actions to inform the caller during the transfer attempt to the cell phone. Dependencies include configured users/extensions and a gateway (e.g., 'flowroute').

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/Dialplan-FollowMe_9634428.mdx#_snippet_2

LANGUAGE: XML
CODE:
```
<!-- in dialplan/public.xml -->
   <extension name="KJV">
      <condition field="destination_number" expression="^(0)$">
        <!-- Make sure the caller hears ringing if we're ringing a phone and we already answered -->
      	<action application="set" data="transfer_ringback=${us-ring}"/>

        <!-- This was added some time ago, not sure if it's still needed -->
      	<action application="ring_ready"/>

        <!-- Hanging up after the bridge is generally a good idea -->
      	<action application="set" data="hangup_after_bridge=true"/>

        <!-- Now that we're not dealing with VoicePulse let's see if things are sane again -->
      	<action application="set" data="continue_on_fail=NORMAL_TEMPORARY_FAILURE,USER_BUSY,NO_ANSWER,TIMEOUT,NO_ROUTE_DESTINATION"/>
        <!-- Voicepulse workaround - Seems they always answer regardless of whether or not they managed to place the call. -->
        <!--<action application="set" data="continue_on_fail=true"/>-->

         <!-- For the first try, let's set the call timeout to 12 seconds-->
        <action application="set" data="call_timeout=12"/>

        <!-- Add in some features that help make freeswitch exceptionally versatile -->
        <action application="bind_meta_app" data="1 b s execute_extension::dx XML features"/>
        <action application="bind_meta_app" data="2 b s record_session::$${base_dir}/recordings/${caller_id_number}.${strftime(%Y-%m-%d-%H-%M-%S)}.wav"/>
        <action application="bind_meta_app" data="3 b s execute_extension::cf XML features"/>

        <!-- Now we try the VoIP phones only. -->
        <action application="bridge" data="{ignore_early_media=true}user/7001@$${domain},user/7010@$${domain},user/7022@$${domain},user/7007@$${domain}"/>

        <!-- Now we will try all VoIP phones, AND the cell phone -->
        <!-- Subsequently we want the timer a little longer than 12 seconds -->
        <!-- If you don't want it to hit your cell or work voice mail try a lower setting than 60 seconds below-->
        <action application="set" data="call_timeout=60"/>

        <!-- Make sure we have Allison tell the caller what's happening so they don't get impatient and hang up while I'm out driving around -->
        <action application="speak" data="cepstral|allison|outside transfer"/>
        <action application="sleep" data="1000"/>
        <!-- This next line may not be necessary -->
        <action application="ring_ready"/>

        <!-- Perform the bridge to all VoIP and Cell -->
        <action application="bridge" data="{ignore_early_media=true}user/7001@$${domain},user/7010@$${domain},user/7022@$${domain},user/7007@$${domain},sofia/gateway/flowroute/12345678901"/>

      <!-- You can put other stuff here, but I happen to like iPhone voice mail -->

      </condition>
    </extension>
```

----------------------------------------

TITLE: Usage Syntax for mod_dptools: transfer
DESCRIPTION: Shows the command-line or dialplan syntax for the transfer application, specifying the required destination number and optional dialplan and context.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod-dptools/6586616.mdx#_snippet_1

LANGUAGE: Syntax
CODE:
```
transfer <destination_number> [<dialplan> [<context>]]
```

----------------------------------------

TITLE: Dialplan Condition for 10 or 11 Digit Dialing
DESCRIPTION: This XML snippet shows an alternative condition for the dialplan extension. It uses a regular expression to match destination numbers that are either 10 digits long or 11 digits long starting with '1', allowing for more flexible dialing patterns.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Interoperability/Providers-ITSPs/2883882.mdx#_snippet_3

LANGUAGE: XML
CODE:
```
<condition field="destination_number" expression="^1?(\d{10})$">
```

----------------------------------------

TITLE: Call Billing Group - FreeSWITCH Dialplan
DESCRIPTION: Dials the literal string 2002 to route the call to the 'billing' call group, ringing all members defined in the directory.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/Default-Dialplan-QRF_9634422.mdx#_snippet_17

LANGUAGE: FreeSWITCH Dialplan
CODE:
```
2002
```

----------------------------------------

TITLE: Call Return Last Caller - FreeSWITCH Dialplan
DESCRIPTION: Dials *69, 869, or lcr to call back the last number that called the originating extension.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Dialplan/Default-Dialplan-QRF_9634422.mdx#_snippet_8

LANGUAGE: FreeSWITCH Dialplan
CODE:
```
\*69
```

LANGUAGE: FreeSWITCH Dialplan
CODE:
```
869
```

LANGUAGE: FreeSWITCH Dialplan
CODE:
```
lcr
```

----------------------------------------

TITLE: Unsetting sip_h_Referred-By in FreeSWITCH XML Dialplan
DESCRIPTION: This snippet shows how to remove or unset the `sip_h_Referred-By` SIP header using the `unset` application in a FreeSWITCH XML dialplan.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/Channel-Variables-Catalog/sip_h_Referred-By_16354288.mdx#_snippet_1

LANGUAGE: XML
CODE:
```
<action application="unset" data="sip_h_referred-by"/>
```

----------------------------------------

TITLE: Configure Public Dialplan for Incoming H.323 Calls (FreeSWITCH XML)
DESCRIPTION: This XML snippet shows a FreeSWITCH dialplan extension typically found in public.xml. It matches specific destination numbers (e.g., 1000-1099) and transfers the call to the 'default' context using the XML dialplan. This is used to route incoming calls received via mod_h323 to the main dialplan.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod_h323_6587443.mdx#_snippet_15

LANGUAGE: XML
CODE:
```
  <extension name="public_extensions">
    <condition field="destination_number" expression="^(10[01][0-9])$">
      <action application="transfer" data="$1 XML default"/>
    </condition>
  </extension>
```

----------------------------------------

TITLE: Example Asterisk-like Dialplan Context (XML)
DESCRIPTION: This snippet shows an example configuration for the 'default' context using the mod_dialplan_asterisk module. It demonstrates standard extension matching, pattern matching, caller-id matching, and FreeSWITCH-specific features like PCRE regex matching (using '~') and matching based on channel variables.

SOURCE: https://github.com/signalwire/freeswitch-docs/blob/main/docs/FreeSWITCH-Explained/Modules/mod_dialplan_asterisk_3966416.mdx#_snippet_0

LANGUAGE: XML
CODE:
```
[default]
; and you can use comments the same way
; here is a demo that is close to the extensions.conf in tree that installs by default

[default]
; Things you're used to....
exten => music,n,Dial(SIP/<EMAIL>|120)

; similar pattern matching and caller-id match.
exten => _1XXXXX,n,set(cool=${EXTEN})
exten => _1XXXXX,n,set(myvar=true)
exten => _1XXXXX,n,Goto(default|music)
exten => 2137991400/1000,n,Goto(default|music)


; we also embellished things a bit and added some of our own goodies
; Some new magic you can do.... if you start the exten string with a ~ it implies PCRE regex
; *NOTE* the ,n, is there for familiarity purposes we do not parse it anyway nor will we honor line numbers.
exten => ~^(18(0{2}|8{2}|7{2}|6{2})d{7})$,n,enum($1)
exten => ~^(18(0{2}|8{2}|7{2}|6{2})d{7})$,n,bridge(${enum_auto_route})


; instead of exten, put anyting about the call you would rather match on.
; either the names of a field in caller_profile or a string of variables to expand.
caller_id_number => 2137991400,n,Goto(default|music)
${sip_from_user} => bill,n,Goto(default|music)
```