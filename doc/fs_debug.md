🔍 FreeSWITCH Debugging — When and How

Use debug mode only while diagnosing issues (e.g., failed SIP registration, one-way audio, TLS/SRTP problems).
Remember to turn it back down afterwards–verbose logging will quickly fill /opt/freeswitch-logs.

1  Why enable debug?

Symptom	What debug shows
4xx/5xx responses to REGISTER/INVITE	Full SIP exchange, auth headers, NAT info
No audio / RTP time-outs	RTP port negotiation, codec list, ICE candidates
Module load errors	Missing/invalid modules, XML parse errors
ESL / app-level bugs	Application events, variable sets, dialplan branches

2  Enable debug on the running conainer

# 1) Attach to the CLI
ssh <EMAIL> \
  "docker exec -it freeswitch-minimal fs_cli"

# 2) Raise log level (0-7; 7 = DEBUG)
fs_cli> console loglevel debug     # or: console loglevel 7
# Optional: trace SIP & RTP
fs_cli> sofia global siptrace on
fs_cli> sofia global capture on

Logs now stream to both the CLI session and to the mounted directory /opt/freeswitch-logs/ on the host.

3  Run the whole daemon in foreground (deep dive)

If you need to see startup output (module load order, XML validation, etc.):

ssh <EMAIL>
docker stop freeswitch-minimal
docker run --rm --network host \
  -v /opt/freeswitch-config:/etc/freeswitch \
  -v /opt/freeswitch-logs:/var/log/freeswitch \
  freeswitch-custom \
  freeswitch -nonat -c          # -c = stay in console

Terminate with Ctrl-C when done, then docker-compose up -d freeswitch to return to normal service.

4  Reset to normal logging

fs_cli> console loglevel info    # 3 = INFO (default)
fs_cli> sofia global siptrace off
fs_cli> sofia global capture off

Confirm with:

docker logs --tail 20 freeswitch-minimal

5  Useful one-liners for quick checks

# Show active registrations & their IPs
docker exec freeswitch-minimal fs_cli -x "show registrations"

# Show live calls
docker exec freeswitch-minimal fs_cli -x "show calls count"
