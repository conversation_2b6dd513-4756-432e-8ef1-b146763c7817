<?xml version="1.0"?>
<document type="freeswitch/xml">
  <!-- Main FreeSWITCH Configuration -->
  <X-No-Pre-Process cmd="set" data="domain=*************"/>
  <X-No-Pre-Process cmd="set" data="domain_name=*************"/>
  <X-No-Pre-Process cmd="set" data="hold_music=silence"/>
    <X-PRE-PROCESS cmd="set" data="timezone=UTC"/>
  
  <!-- Configuration Section -->
  <section name="configuration" description="Various Configuration">
    
    <!-- Load minimal modules -->
    <configuration name="modules.conf" description="Modules">
      <modules>
        <load module="mod_console"/>
        <load module="mod_logfile"/>
        <load module="mod_event_socket"/>
        <load module="mod_sofia"/>
        <load module="mod_commands"/>
        <load module="mod_dptools"/>
        <load module="mod_dialplan_xml"/>
        <load module="mod_sndfile"/>
        <load module="mod_native_file"/>
        <load module="mod_tone_stream"/>
        <load module="mod_flite"/>
        <load module="mod_say_en"/>
        <load module="mod_g729"/>
        <load module="mod_gsm"/>
        <load module="mod_db"/>
        <load module="mod_hash"/>
        <load module="mod_conference"/>
      </modules>
    </configuration>
    
    <!-- Switch Configuration -->
    <configuration name="switch.conf" description="Core Configuration">
      <settings>
        <param name="colorize-console" value="true"/>
        <param name="max-sessions" value="100"/>
        <param name="sessions-per-second" value="10"/>
        <param name="loglevel" value="info"/>
      </settings>
    </configuration>
    
    <!-- Event Socket Configuration -->
    <configuration name="event_socket.conf" description="Socket Client">
      <settings>
        <param name="listen-ip" value="0.0.0.0"/>
        <param name="listen-port" value="8021"/>
        <param name="password" value="ClueCon"/>
        <param name="apply-inbound-acl" value="loopback.auto"/>
      </settings>
    </configuration>
    
    <!-- Flite TTS Configuration -->
    <configuration name="flite.conf" description="Flite TTS">
      <settings>
        <param name="voice" value="kal"/>
      </settings>
    </configuration>
    
    <!-- Sofia SIP Configuration -->
    <configuration name="sofia.conf" description="SIP Profiles">
      <global_settings>
        <param name="log-level" value="0"/>
        <param name="auto-restart" value="false"/>
      </global_settings>
      
      <profiles>
        <profile name="internal">
          <aliases/>
          <gateways>
            <gateway name="octella">
              <param name="username" value="0128050"/>
              <param name="password" value="BPyFPM3vth"/>
              <param name="realm" value="**************:9060"/>
              <param name="proxy" value="**************:9060"/>
              <param name="register" value="true"/>
              <param name="caller-id-in-from" value="false"/>
              <param name="extension-in-contact" value="true"/>
            </gateway>
          </gateways>
          <domains>
            <domain name="all" alias="false" parse="true"/>
          </domains>
          <settings>
            <param name="sip-port" value="5080"/>
            <param name="dialplan" value="XML"/>
            <param name="context" value="default"/>
            <param name="rtp-ip" value="*************"/>
            <param name="sip-ip" value="*************"/>
            <param name="ext-rtp-ip" value="auto-nat"/>
            <param name="ext-sip-ip" value="auto-nat"/>
            <param name="rtp-timer-name" value="soft"/>
            <param name="auth-calls" value="true"/>
            <param name="inbound-codec-prefs" value="PCMU,PCMA,GSM"/>
            <param name="outbound-codec-prefs" value="PCMU,PCMA,GSM"/>
            <param name="inbound-late-negotiation" value="false"/>
            <param name="disable-transcoding" value="false"/>
            <param name="challenge-realm" value="auto_from"/>
            <param name="nonce-ttl" value="60"/>
            <param name="rtp-timeout-sec" value="300"/>
            <param name="rtp-hold-timeout-sec" value="1800"/>
            <param name="session-timeout" value="0"/>
            <param name="minimum-session-expires" value="0"/>
          </settings>
        </profile>
      </profiles>
    </configuration>
    
  </section>

  <!-- Dialplan Section -->
  <section name="dialplan" description="Regex/XML Dialplan">
    <context name="default">
      <!-- Extension 999 - Welcome message -->
      <extension name="welcome">
        <condition field="destination_number" expression="^999$">
          <action application="set" data="bypass_media=false"/>
          <action application="set" data="proxy_media=false"/>
          <action application="set" data="absolute_codec_string=PCMU"/>
          <action application="answer"/>
          <action application="sleep" data="1000"/>
          <action application="record_session" data="/tmp/recording_${uuid}.wav"/>
          <action application="playback" data="tone_stream://%(4000,4000,400)"/>
          <action application="sleep" data="2000"/>
          <action application="stop_record_session" data="/tmp/recording_${uuid}.wav"/>
          <action application="hangup"/>
        </condition>
      </extension>
      
      <!-- Extension 998 - Play recorded audio -->
      <extension name="playback">
        <condition field="destination_number" expression="^998$">
          <action application="answer"/>
          <action application="sleep" data="1000"/>
          <action application="playback" data="/tmp/recording_latest.wav"/>
          <action application="hangup"/>
        </condition>
      </extension>
      
      <!-- Extension 888 - LiveKit Agent -->
      <extension name="livekit_agent">
        <condition field="destination_number" expression="^888$">
          <action application="set" data="bypass_media=false"/>
          <action application="set" data="proxy_media=false"/>
          <action application="set" data="absolute_codec_string=PCMU"/>
          <action application="answer"/>
          <action application="sleep" data="500"/>
          <action application="bridge" data="sofia/internal/sip:888@**********:5070"/>
        </condition>
      </extension>
      
      <!-- Extension 777 - Outbound call via Octella trunk (hardcoded) -->
      <extension name="octella_outbound">
        <condition field="destination_number" expression="^777$">
          <action application="set" data="bypass_media=false"/>
          <action application="set" data="proxy_media=false"/>
          <action application="set" data="absolute_codec_string=PCMU,PCMA,G729"/>
           <action application="set" data="bridge_generate_comfort_noise=true"/>
          <action application="set" data="send_silence_when_idle=400"/>
          <action application="set" data="suppress_cng=true"/>
          <action application="set" data="effective_caller_id_number=6498012138"/>
          <action application="set" data="effective_caller_id_name=Keyman"/>
          <action application="set" data="ringback=${us-ring}"/>
          <action application="set" data="transfer_ringback=${us-ring}"/>
          <action application="record_session" data="/opt/recordings/+380961717124.wav"/>
          <action application="bridge" data="sofia/gateway/octella/+380961717124"/>
        </condition>
      </extension>
      
      <!-- Dynamic outbound calls via Octella trunk -->
      <extension name="octella_dynamic">
        <condition field="destination_number" expression="^(\+\d{10,15})$">
          <action application="set" data="bypass_media=false"/>
          <action application="set" data="proxy_media=false"/>
          <action application="set" data="absolute_codec_string=PCMU,PCMA,G729"/>
          <action application="set" data="effective_caller_id_number=6498012138"/>
          <action application="set" data="effective_caller_id_name=Keyman"/>
          <action application="set" data="ringback=${us-ring}"/>
          <action application="set" data="transfer_ringback=${us-ring}"/>
           <action application="set" data="bridge_generate_comfort_noise=true"/>
          <action application="set" data="send_silence_when_idle=400"/>
          <action application="set" data="suppress_cng=true"/>
            <action application="set" data="timezone=UTC"/>
            <action application="record_session"
                    data="/opt/recordings/call-${destination_number}-${strftime(%Y%m%d_%H)}.wav"/>
          <action application="bridge" data="sofia/gateway/octella/${destination_number}"/>
        </condition>
      </extension>
      
      <!-- User extension - keyman -->
      <extension name="keyman">
        <condition field="destination_number" expression="^(keyman)$">
          <action application="bridge" data="user/${destination_number}@${domain_name}"/>
        </condition>
      </extension>
    </context>
  </section>

  <!-- Directory Section -->
  <section name="directory" description="User Directory">
    <domain name="*************">
      <params>
        <param name="dial-string" value="{presence_id=${dialed_user}@${dialed_domain}}user/${dialed_user}@${dialed_domain}"/>
      </params>
      
      <variables>
        <variable name="record_stereo" value="true"/>
      </variables>
      
      <groups>
        <group name="default">
          <users>
            <user id="keyman">
              <params>
                <param name="password" value="keyman123"/>
                <param name="vm-password" value="keyman"/>
              </params>
              <variables>
                <variable name="toll_allow" value="domestic,international,local"/>
                <variable name="accountcode" value="keyman"/>
                <variable name="user_context" value="default"/>
                <variable name="effective_caller_id_name" value="Keyman"/>
                <variable name="effective_caller_id_number" value="keyman"/>
                <variable name="outbound_caller_id_name" value="Keyman"/>
                <variable name="outbound_caller_id_number" value="keyman"/>
              </variables>
            </user>
          </users>
        </group>
      </groups>
    </domain>
  </section>
</document>