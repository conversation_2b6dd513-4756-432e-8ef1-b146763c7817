FROM safarov/freeswitch:latest

# Copy our complete minimal configuration
COPY . /etc/freeswitch/

# Create and set permissions for runtime directories
RUN mkdir -p /var/run/freeswitch /var/log/freeswitch /var/lib/freeswitch /opt/recordings && \
    chmod 755 /var/run/freeswitch /var/log/freeswitch /var/lib/freeswitch /opt/recordings && \
    chmod -R 644 /etc/freeswitch/ && \
    chmod 755 /etc/freeswitch/ && \
    find /etc/freeswitch -type d -exec chmod 755 {} \;

# Expose ports (though we'll use host networking)
EXPOSE 5080/tcp 5080/udp 8021/tcp 10000-15000/udp

# Use the default entrypoint and user from base image