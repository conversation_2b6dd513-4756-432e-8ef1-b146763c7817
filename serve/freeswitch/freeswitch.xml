<?xml version="1.0"?>
<!--
    NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE

    This is the FreeSWITCH default config.  Everything you see before you now traverses
    down into all the directories including files which include more files.  The default
    config comes out of the box already working in most situations as a PBX.  This will
    allow you to get started testing and playing with various things in FreeSWITCH.

    Before you start to modify this default please visit this wiki page:

    https://developer.signalwire.com/freeswitch/FreeSWITCH-Explained/Configuration/Default-Configuration_6587388/#6-configuration-files

    If all else fails you can read our FAQ located at:

    https://developer.signalwire.com/freeswitch/FreeSWITCH-Explained/Miscellaneous/FAQ/

    NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE
-->
<document type="freeswitch/xml">
  <!--#comment
      All comments starting with #command will be preprocessed and never sent to the xml parser
      Valid instructions:
      #include ==> Include another file to this exact point
                   (partial xml should be encased in <include></include> tags)
      #set     ==> Set a global variable (can be expanded during preprocessing with $$ variables)
                   (note the double $$ which denotes preprocessor variables)
      #comment ==> A general comment such as this

      The preprocessor will compile the full xml document to ${prefix}/log/freeswitch.xml.fsxml
      Don't modify it while freeswitch is running cos it is mem mapped in most cases =D

      The same can be achieved with the <X-PRE-PROCESS> tag where the attrs 'cmd' and 'data' are
      parsed in the same way.
  -->
  <!--#comment
      vars.xml contains all the #set directives for the preprocessor.
  -->
  <X-PRE-PROCESS cmd="include" data="vars.xml"/>

  <section name="configuration" description="Various Configuration">
    <X-PRE-PROCESS cmd="include" data="autoload_configs/*.xml"/>
  </section>

  <section name="dialplan" description="Regex/XML Dialplan">
    <X-PRE-PROCESS cmd="include" data="dialplan/*.xml"/>
  </section>

  <section name="chatplan" description="Regex/XML Chatplan">
    <X-PRE-PROCESS cmd="include" data="chatplan/*.xml"/>
  </section>

  <!-- mod_dingaling is reliant on the vcard data in the "directory" section. -->
  <!-- mod_sofia is reliant on the user data for authorization -->
  <section name="directory" description="User Directory">
    <X-PRE-PROCESS cmd="include" data="directory/*.xml"/>
  </section>

  <!-- languages section (under development still) -->
  <section name="languages" description="Language Management">
    <X-PRE-PROCESS cmd="include" data="lang/de/*.xml"/>
    <X-PRE-PROCESS cmd="include" data="lang/en/*.xml"/>
    <X-PRE-PROCESS cmd="include" data="lang/fr/*.xml"/>
    <X-PRE-PROCESS cmd="include" data="lang/ru/*.xml"/>
    <X-PRE-PROCESS cmd="include" data="lang/he/*.xml"/>
    <X-PRE-PROCESS cmd="include" data="lang/es/es_ES.xml"/>
    <X-NO-PRE-PROCESS cmd="include" data="lang/es/es_MX.xml"/>
    <X-PRE-PROCESS cmd="include" data="lang/pt/pt_BR.xml"/>
    <X-NO-PRE-PROCESS cmd="include" data="lang/pt/pt_PT.xml"/>
    <X-NO-PRE-PROCESS cmd="include" data="lang/sv/*.xml"/>
  </section>
</document>
