[default]

; Things you're used to....
exten => music,n,Di<PERSON>(SIP/<EMAIL>|120)

exten => _1XXXXX,n,set(cool=${EXTEN})
exten => _1XXXXX,n,set(myvar=true)
exten => _1XXXXX,n,<PERSON><PERSON>(default|music)
exten => 2137991400/1000,n,<PERSON><PERSON>(default|music)


; Some new magic you can do....
exten => ~^(18(0{2}|8{2}|7{2}|6{2})\d{7})$,n,enum($1)
exten => ~^(18(0{2}|8{2}|7{2}|6{2})\d{7})$,n,bridge(${enum_auto_route})

; instead of exten, put anything about the call you would rather match on.
; either the names of a field in caller_profile or a string of variables to expand.
caller_id_number => 2137991400,n,<PERSON><PERSON>(default|music)
${sip_from_user} => bill,n,<PERSON><PERSON>(default|music)


