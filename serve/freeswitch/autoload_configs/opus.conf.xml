<configuration name="opus.conf">
    <settings>
        <param name="use-vbr" value="1"/>
        <!--<param name="use-dtx" value="1"/>-->
        <param name="complexity" value="10"/>
	<!-- Set the initial packet loss percentage 0-100 -->
        <!--<param name="packet-loss-percent" value="10"/>-->
	<!-- Support asymmetric sample rates -->
        <!--<param name="asymmetric-sample-rates" value="1"/>-->

	<!-- Enable bitrate negotiation -->
        <!--<param name="bitrate-negotiation" value="1"/>-->

	<!-- Keep FEC Enabled -->
        <param name="keep-fec-enabled" value="1"/>
	<!--<param name="use-jb-lookahead" value="true"/> -->
        <!--
           maxaveragebitrate: the maximum average codec bitrate (values: 6000 to 510000 in bps) 0 is not considered
           maxplaybackrate: the maximum codec internal frequency (values: 8000, 12000, 16000, 24000, 48000 in Hz) 0 is not considered
           This will set the local encoder and instruct the remote encoder trough specific "fmtp" attibute in the SDP.

           Example: if you receive "maxaveragebitrate=20000" from SDP and you have set "maxaveragebitrate=24000" in this configuration
                    the lowest will prevail in this case "20000" is set on the encoder and the corresponding fmtp attribute will be set
                    to instruct the remote encoder to do the same.
        -->
        <param name="maxaveragebitrate" value="0"/>
        <param name="maxplaybackrate" value="0"/>
	<!-- Max capture rate, 8000, 12000, 16000, 24000 and 48000 are valid options -->
        <!--<param name="sprop-maxcapturerate" value="0"/>-->
	<!-- Enable automatic bitrate variation during the call based on RTCP feedback -->
	<!--<param name="adjust-bitrate" value="1"/>-->
	<!-- will enforce mono even if the remote party wants stereo. must be used in conjunction with param "max-audio-channels" set to 1 in switch.conf.xml. -->
		<param name="mono" value="0"/>
    </settings>
</configuration>
