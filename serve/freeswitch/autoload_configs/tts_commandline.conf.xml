<configuration name="tts_commandline.conf" description="TextToSpeech Commandline configuration">
    <settings>
	<!--
	Some variables will be replaced :
	${text}: input text (quoted)
	${rate}: sample rate (example: 8000)
	${voice}: voice_name passed to TTS(quoted)
	${file}: output file (quoted, including .wav extension)
    
    Example commands can be found at:
    https://freeswitch.org/confluence/display/FREESWITCH/mod_tts_commandline#mod_tts_commandline-Examplecommands
	-->
	<param name="command" value="echo ${text} | text2wave -f ${rate} > ${file}"/>
    </settings>
</configuration>
