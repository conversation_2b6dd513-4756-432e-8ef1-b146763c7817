<include><!--This line will be ignored it's here to validate the xml and is optional -->

	<macro name="directory_intro">
		<input pattern="^(last_name)" break_on_match="false">
			<match>
				<action function="speak-text" data="Por favor marque as primeiras letras do apelido da pessoa"/>
			</match>
		</input>
		<input pattern="^(first_name)" break_on_match="false">
			<match>
				<action function="speak-text" data="Por favor marque as primeiras letras do nome da pessoa"/>
			</match>
		</input>
		<input pattern="^(last_name):([0-9#*])$" break_on_match="false">
			<match>
				<action function="speak-text" data="Para buscar por primeiro nome, digite $2"/>
			</match>
		</input>
		<input pattern="^(first_name):([0-9#*])$" break_on_match="false">
			<match>
				<action function="speak-text" data="Para buscar por apelido, digite $2"/>
			</match>
		</input>
	</macro>

	<macro name="directory_min_search_digits">
		<input pattern="^(.*)$">
			<match>
				<action function="speak-text" data="Você precisa dizer um mínimo de $1 letras do nome da pessoa, tente novamente."/>
			</match>
		</input>
	</macro>

	<macro name="directory_result_count">
		<input pattern="^0$" break_on_match="true">
			<match>
				<action function="speak-text" data="Não foram encontrados resultados, tente novamente."/>
			</match>
		</input>
		<input pattern="^(.*)$">
			<match>
				<action function="speak-text" data="$1 resultados encontrados para a sua busca."/>
			</match>
		</input>
	</macro>

	<macro name="directory_result_count_too_large">
		<input pattern="^(.*)$">
			<match>
				<action function="speak-text" data="A  sua busca devolveu muitos resultados, por favor tente novamente."/>
			</match>
		</input>

	</macro>

	<macro name="directory_result_last">
		<input pattern="^(.*)$">
			<match>
				<action function="speak-text" data="Não há mais resultados."/>
			</match>
		</input>

	</macro>

	<macro name="directory_result_item">
		<input pattern="^(.*)$">
			<match>
				<action function="speak-text" data="Resultado número $1"/>
			</match>
		</input>
	</macro>

	<macro name="directory_result_menu">
		<input pattern="^([0-9#*]),([0-9#*]),([0-9#*]),([0-9#*])$">
			<match>
				<action function="speak-text" data="Para escolher este resultado digite $1, Para o resultado seguinte digite $2, Para o resultado anterior digite $3, Para uma nova busca digite $4"/>
			</match>
		</input>
	</macro>

	<macro name="directory_result_at">
		<input pattern="^(.*)$">
			<match>
				<action function="speak-text" data="No ramal $1"/>
			</match>
		</input>
	</macro>
	<macro name="directory_result_say_name">
		<input pattern="^(.*)$">
			<match>
				<action function="speak-text" data="$1"/>
			</match>
		</input>
	</macro>

</include><!--This line will be ignored it's here to validate the xml and is optional -->
