<?xml version="1.0" encoding="Windows-1252"?>
<include><!--This line will be ignored it's here to validate the xml and is optional -->

  <macro name="new_demo_ivr_main_menu" pause="100"> <!-- See conf/ivr_menus/ivr.conf.xml for an example on how to use this macro in an IVR -->
    <input pattern="(.*)">
      <match>
        <action function="play-file" data="ivr/ivr-welcome_to_freeswitch.wav"/>
        <action function="play-file" data="silence_stream://500"/>

        <!-- Menu option 1: For information about FreeSWITCH... -->
        <action function="play-file" data="misc/misc-information_about_freeswitch.wav"/>
        <action function="play-file" data="digits/1.wav"/>

        <!-- Menu option 2: To learn more about FreeSWITCH Solutions... -->
        <action function="play-file" data="misc/misc-learn_more_about_freeswitch_solutions.wav"/>
        <action function="play-file" data="digits/2.wav"/>

        <!-- Menu option 3: To hear about ClueCon -->
        <action function="play-file" data="misc/misc-to_hear_about_cluecon.wav"/>
        <action function="play-file" data="digits/3.wav"/>

        <!-- Menu option 4: For other options -->
        <action function="play-file" data="ivr/ivr-for_other_options.wav"/>
        <action function="play-file" data="digits/4.wav"/>

        <!-- Menu option 9: Repeat these options -->
        <action function="play-file" data="ivr/ivr-to_repeat_these_options.wav"/>
        <action function="play-file" data="ivr/ivr-please.wav"/>
        <action function="play-file" data="voicemail/vm-press.wav"/>
        <action function="play-file" data="digits/9.wav"/>
        <action function="play-file" data="silence_stream://2000"/>
      </match>
    </input>
  </macro>

  <!-- The following macro is the same as new_demo_ivr_main_menu except it is the "short" version -->
  <!-- The short version has all the options but not the initial greeting -->
  <macro name="new_demo_ivr_main_menu_short" pause="100"> 
    <input pattern="(.*)">
      <match>
        <!-- Menu option 1: For information about FreeSWITCH... -->
        <action function="play-file" data="misc/misc-information_about_freeswitch.wav"/>
        <action function="play-file" data="digits/1.wav"/>

        <!-- Menu option 2: To learn more about FreeSWITCH Solutions... -->
        <action function="play-file" data="misc/misc-learn_more_about_freeswitch_solutions.wav"/>
        <action function="play-file" data="digits/2.wav"/>

        <!-- Menu option 3: To hear about ClueCon -->
        <action function="play-file" data="misc/misc-to_hear_about_cluecon.wav"/>
        <action function="play-file" data="digits/3.wav"/>

        <!-- Menu option 4: For other options -->
        <action function="play-file" data="ivr/ivr-for_other_options.wav"/>
        <action function="play-file" data="digits/4.wav"/>

        <!-- Menu option 9: Repeat these options -->
        <action function="play-file" data="ivr/ivr-to_repeat_these_options.wav"/>
        <action function="play-file" data="ivr/ivr-please.wav"/>
        <action function="play-file" data="voicemail/vm-press.wav"/>
        <action function="play-file" data="digits/9.wav"/>
        <action function="play-file" data="silence_stream://2000"/>

      </match>
    </input>
  </macro>

  <!-- More information about FreeSWITCH... -->
  <macro name="learn_about_freeswitch_sub_menu">
    <input pattern="(.*)">
      <match>
        <!-- Information about FreeSWITCH and OSTAG... -->
        <action function="play-file" data="misc-freeswitch_is_state_of_the_art.wav"/>
        <action function="play-file" data="silence_stream://50"/>
        <action function="play-file" data="misc-it_is_stable_scalable_extensible.wav"/>
        <action function="play-file" data="silence_stream://50"/>
        <action function="play-file" data="misc-free_to_download.wav"/>
        <action function="play-file" data="silence_stream://50"/>
        <action function="play-file" data="misc-freeswitch_sponsored_by_ostag.wav"/>
        <action function="play-file" data="silence_stream://50"/>
        <action function="play-file" data="misc-ostag_learn_more.wav"/>
        <action function="play-file" data="silence_stream://50"/>

        <!-- Menu option 9: Repeat this information -->
        <action function="play-file" data="ivr/ivr-repeat_this_information.wav"/>
        <action function="play-file" data="ivr/ivr-please.wav"/>
        <action function="play-file" data="voicemail/vm-press.wav"/>
        <action function="play-file" data="digits/9.wav"/>
        <action function="play-file" data="silence_stream://2000"/>

        <!-- Menu option *: Return to top menu -->
        <action function="play-file" data="ivr/ivr-to_return_to_previous_menu.wav"/>
        <action function="play-file" data="ivr/ivr-please.wav"/>
        <action function="play-file" data="voicemail/vm-press.wav"/>
        <action function="play-file" data="digits/star.wav"/>

      </match>
    </input>
  </macro>

  <!-- More information about FreeSWITCH Solutions... -->
  <macro name="learn_about_freeswitch_solutions_sub_menu">
    <input pattern="(.*)">
      <match>
        <!-- Information about FreeSWITCH Solutions... -->
        <action function="play-file" data="[[sounds from tony/brian]]"/>
        <action function="play-file" data="silence_stream://50"/>
        <action function="play-file" data="[[sounds from tony/brian]]"/>
        <action function="play-file" data="silence_stream://50"/>
        <action function="play-file" data="[[sounds from tony/brian]]"/>
        <action function="play-file" data="silence_stream://50"/>
        <action function="play-file" data="[[sounds from tony/brian]]"/>
        <action function="play-file" data="silence_stream://50"/>
        <action function="play-file" data="[[sounds from tony/brian]]"/>
        <action function="play-file" data="silence_stream://50"/>

        <!-- Menu option 9: Repeat this information -->
        <action function="play-file" data="ivr/ivr-repeat_this_information.wav"/>
        <action function="play-file" data="ivr/ivr-please.wav"/>
        <action function="play-file" data="voicemail/vm-press.wav"/>
        <action function="play-file" data="digits/9.wav"/>
        <action function="play-file" data="silence_stream://2000"/>

        <!-- Menu option *: Return to top menu -->
        <action function="play-file" data="ivr/ivr-to_return_to_previous_menu.wav"/>
        <action function="play-file" data="ivr/ivr-please.wav"/>
        <action function="play-file" data="voicemail/vm-press.wav"/>
        <action function="play-file" data="digits/star.wav"/>

      </match>
    </input>
  </macro>

  <!-- More information about ClueCon -->
  <macro name="learn_about_cluecon_sub_menu">
    <input pattern="(.*)">
      <match>
        <!-- Information about ClueCon... -->
        <action function="play-file" data="misc-cluecon_is_premier_conference.wav"/>
        <action function="play-file" data="silence_stream://50"/>
        <action function="play-file" data="misc-chicago_each_summer.wav"/>
        <action function="play-file" data="silence_stream://50"/>
        <action function="play-file" data="misc-wide_range_of_persons.wav"/>
        <action function="play-file" data="silence_stream://50"/>
        <action function="play-file" data="misc-support_open_source_by_attending.wav"/>
        <action function="play-file" data="silence_stream://500"/>
        <action function="play-file" data="ivr-register_for_cluecon.wav"/>
        <action function="play-file" data="ivr/ivr-please.wav"/>
        <action function="play-file" data="voicemail/vm-press.wav"/>
        <action function="play-file" data="digits/1.wav"/>
        <action function="play-file" data="silence_stream://500"/>

        <!-- Menu option 9: Repeat this information -->
        <action function="play-file" data="ivr/ivr-repeat_this_information.wav"/>
        <action function="play-file" data="ivr/ivr-please.wav"/>
        <action function="play-file" data="voicemail/vm-press.wav"/>
        <action function="play-file" data="digits/9.wav"/>
        <action function="play-file" data="silence_stream://2000"/>

        <!-- Menu option *: Return to top menu -->
        <action function="play-file" data="ivr/ivr-to_return_to_previous_menu.wav"/>
        <action function="play-file" data="ivr/ivr-please.wav"/>
        <action function="play-file" data="voicemail/vm-press.wav"/>
        <action function="play-file" data="digits/star.wav"/>

      </match>
    </input>
  </macro>


</include><!--This line will be ignored it's here to validate the xml and is optional -->
