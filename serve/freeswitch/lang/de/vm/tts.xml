<include>

  <macro name="voicemail_enter_id">
    <input pattern="(.*)">
      <match>
        <action function="speak-text" data="Bitte geben Sie Ihren Benutzernamen ein, gefolgt von $1."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_enter_pass">
    <input pattern="(.*)">
      <match>
        <action function="speak-text" data="Bitte geben Sie Ihr Passwort ein, gefolgt von $1."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_fail_auth">
    <input pattern="(.*)">
      <match>
        <action function="speak-text" data="Falsche Benutzerdaten."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_hello">
    <input pattern="(.*)">
      <match>
        <action function="speak-text" data="Willkommen in Ihrem Postfach."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_goodbye">
    <input pattern="(.*)">
      <match>
        <action function="speak-text" data="Auf Wiedersehen."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_abort">
    <input pattern="(.*)">
      <match>
        <action function="speak-text" data="Zu viele Fehlversuche."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_message_count">
    <input pattern="^1:(.*)$">
      <match>
        <action function="speak-text" data="Sie haben 1 $2 Nachricht im Ordner ${voicemail_current_folder}."/>
      </match>
    </input>
    <input pattern="^([0,2-9]+):(.*)$">
      <match>
        <action function="speak-text" data="Sie haben $1 $2 Nachrichten im Ordner ${voicemail_current_folder}."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_menu">
    <input pattern="^([0-9#*]):([0-9#*]):([0-9#*]):([0-9#*])$">
      <match>
        <action function="speak-text"
                data="Um neue Nachrichten zu hören, drücken Sie $1. Um gespeicherte Nachrichten zu hören, drücken Sie $2, Für erweiterte Optionen, drücken Sie $3. Zum beenden drücken Sie $4."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_config_menu">
    <input pattern="^([0-9#*]):([0-9#*]):([0-9#*]):([0-9#*])$">
      <match>
        <action function="speak-text"
                data="Um eine Ansage aufzunehmen, drücken Sie $1. Um eine Ansage auszuwählen, drücken Sie $2. Um ihren Namen aufzunehmen, drücken Sie $3. Um zum Hauptmenü zurückzukehren, drücken Sie $4."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_record_name">
    <input pattern="^(.*)$">
      <match>
        <action function="speak-text" data="Sprechen Sie Ihren Namen nach dem Ton, drücken Sie eine beliebige Taste oder hören Sie auf zu sprechen um die Aufnahme zu beenden."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_record_file_check">
    <input pattern="^([0-9#*]):([0-9#*]):([0-9#*])$">
      <match>
        <action function="speak-text"
                data="Um die Aufzeichnung anzuhören, drücken Sie $1. Um die Aufzeichnung zu speichern, drücken Sie $2. Für eine erneute Aufnahme drücken Sie $3."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_record_urgent_check">
    <input pattern="^([0-9#*]):([0-9#*])$">
      <match>
        <action function="speak-text"
                data="Drücken Sie $1 um diese Nachricht als wichtig zu markieren. Um fortzufahren drücken Sie $2."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_listen_file_check">
    <input pattern="^([0-9#*]):([0-9#*]):([0-9#*]):([0-9#*])$">
      <match>
        <action function="speak-text"
                data="Drücken Sie $1 um die Nachricht erneut zu hören. Um die Nachricht zu speichern, drücken Sie $2. Zum löschen der Nachricht drücken Sie $3. Für die Weiterleitung als Email, drücken Sie $4."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_choose_greeting">
    <input pattern="^(.*)$">
      <match>
        <action function="speak-text" data="Wählen Sie eine Ansage zwischen 1 und 3."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_choose_greeting_fail">
    <input pattern="^(.*)$">
      <match>
        <action function="speak-text" data="Ungültige Eingabe."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_record_greeting">
    <input pattern="^(.*)$">
      <match>
        <action function="speak-text" data="Zeichnen Sie Ihre Ansage nach dem Ton auf. Drücken Sie eine beliebige Taste oder hören Sie auf zu sprechen um die Aufnahme zu beenden."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_record_message">
    <input pattern="^(.*)$">
      <match>
        <action function="speak-text" data="Sprechen Sie nach dem Ton. Drücken Sie eine beliebige Taste oder hören Sie auf zu sprechen um die Aufnahme zu beenden."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_greeting_selected">
    <input pattern="^(.*)$">
      <match>
        <action function="speak-text" data="Ansage $1 ausgewählt."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_play_greeting">
    <input pattern="^(.*)$">
      <match>
        <action function="speak-text" data="$1 ist nicht verfügbar."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_say_number">
    <input pattern="^(.*)$">
      <match>
        <action function="speak-text" data="$1"/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_say_message_number">
    <input pattern="^([a-z]+):(.*)$">
      <match>
        <action function="speak-text" data="$1 Nachricht Nummer $2."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_say_phone_number">
    <input pattern="^(.*)$">
      <match>
        <action function="speak-text" data="$1."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_say_name">
    <input pattern="^(.*)$">
      <match>
        <action function="speak-text" data="$1."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_ack">
    <input pattern="^(.*)$">
      <match>
        <action function="speak-text" data="Nachricht $1"/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_say_date">
    <input pattern="^(.*)$">
      <match>
        <action function="speak-text" data="$strftime($1|%A, %B %d %Y, %I %M %p)"/>
      </match>
    </input>
  </macro>

</include>
<!--
For Emacs:
Local Variables:
mode:xml
indent-tabs-mode:nil
tab-width:2
c-basic-offset:2
End:
For VIM:
vim:set softtabstop=2 shiftwidth=2 tabstop=2 expandtab:
-->
