<include>
  <macro name="msgcount">
    <input pattern="(.*)">
      <match>
        <action function="execute" data="sleep(1000)"/>
        <action function="play-file" data="voicemail/vm-you_have.wav"/>
        <action function="say" data="$1" method="pronounced" type="items"/>
        <action function="play-file" data="voicemail/vm-messages.wav"/>
        <!-- or -->
        <!--<action function="speak-text" data="Sie haben $1 Nachrichten"/>-->
      </match>
    </input>
  </macro>
  <macro name="saydate">
    <input pattern="(.*)">
      <match>
        <action function="say" data="$1" method="pronounced" type="current_date_time"/>
      </match>
    </input>
  </macro>
  <macro name="timespec">
    <input pattern="(.*)">
      <match>
        <action function="say" data="$1" method="pronounced" type="time_measurement"/>
      </match>
    </input>
  </macro>
  <macro name="ip-addr">
    <input pattern="(.*)">
      <match>
        <action function="say" data="$1" method="iterated" type="ip_address"/>
        <action function="say" data="$1" method="pronounced" type="ip_address"/>
      </match>
    </input>
  </macro>
  <macro name="spell">
    <input pattern="(.*)">
      <match>
        <action function="say" data="$1" method="pronounced" type="name_spelled"/>
      </match>
    </input>
  </macro>
  <macro name="spell-phonetic">
    <input pattern="(.*)">
      <match>
        <action function="say" data="$1" method="pronounced" type="name_phonetic"/>
      </match>
    </input>
  </macro>
  <macro name="tts-timeleft">
    <!-- The parser will visit each <input> tag and execute the actions in <match> or <nomatch> depending on the pattern param -->
    <!-- If the function "break" is encountered all parsing will cease -->
    <input pattern="(\d+):(\d+)">
      <match>
        <action function="speak-text" data="Sie haben $1 Minuten, $2 Sekunden übrig $strftime(%Y-%m-%d)"/>
        <action function="break"/>
      </match>
      <nomatch>
        <action function="speak-text" data="Die Eingabe war ungültig."/>
      </nomatch>
    </input>
    <input pattern="(\d+) min (\d+) sek">
      <match>
        <action function="speak-text" data="Sie haben $1 Minuten, $2 Sekunden übrig $strftime(%Y-%m-%d)"/>
      </match>
      <nomatch>
        <action function="speak-text" data="Die Eingabe war ungültig."/>
      </nomatch>
    </input>
  </macro>
</include>
<!--
For Emacs:
Local Variables:
mode:xml
indent-tabs-mode:nil
tab-width:2
c-basic-offset:2
End:
For VIM:
vim:set softtabstop=2 shiftwidth=2 tabstop=2 expandtab:
-->
