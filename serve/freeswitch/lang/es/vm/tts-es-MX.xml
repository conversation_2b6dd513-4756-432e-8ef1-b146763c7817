<include><!--This line will be ignored it's here to validate the xml and is optional -->
  <macro name="voicemail_enter_id">
    <input pattern="(.*)">
      <match>
	<action function="speak-text" data="Por favor ingrese su número de usuario, seguido por $1."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_enter_pass">
    <input pattern="(.*)">
      <match>
	<action function="speak-text" data="Por favor ingrese su contraseña, seguido por $1."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_fail_auth">
    <input pattern="(.*)">
      <match>
	<action function="speak-text" data="Inicio de sesión incorrecto."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_hello">
    <input pattern="(.*)">
      <match>
	<action function="speak-text" data="Bienvenido a su buzón de voz."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_goodbye">
    <input pattern="(.*)">
      <match>
	<action function="speak-text" data="Adiós."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_abort">
    <input pattern="(.*)">
      <match>
	<action function="speak-text" data="Demasiados intentos fallidos."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_message_count">
    <input pattern="^1:(.*)$" break_on_match="true">
      <match>
	<action function="speak-text" data="Usted tiene 1 $1 mensaje en la bandeja de entrada ${voicemail_current_folder}."/>
      </match>
    </input>
    <input pattern="^(\d+):(.*)$">
      <match>
	<action function="speak-text" data="Usted tiene $1 $2 mensajes en la bandeja de entrada ${voicemail_current_folder}."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_menu">
    <input pattern="^([0-9#*]):([0-9#*]):([0-9#*]):([0-9#*])$">
      <match>
	<action function="speak-text" 
		data="Para escuchar mensajes nuevos, pulse $1. Para escuchar mensajes guardados, pulse $2. Para opciones avanzadas, pulse $3. Para salir, pulse $4."/>
      </match>
    </input>
  </macro>


  <macro name="voicemail_config_menu">
    <input pattern="^([0-9#*]):([0-9#*]):([0-9#*]):([0-9#*]):([0-9#*])$">
      <match>
	<action function="speak-text" 
		data="Para grabar un saludo, pulse $1. Para elegir un saludo, pulse $2. Para grabar su nombre, pulse $3. Para cambiar su contraseña, pulse $5. Para el menú principal, pulse $5."/>
      </match>
    </input>
  </macro>


  <macro name="voicemail_record_name">
    <input pattern="^(.*)$">
      <match>
	<action function="speak-text" data="Después del tono, por favor anote su nombre, pulse cualquier tecla o deje de hablar para poner fin a la grabación."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_record_file_check">
    <input pattern="^([0-9#*]):([0-9#*]):([0-9#*])$">
      <match>
	<action function="speak-text" 
		data="Para escuchar la grabación, pulse $1. Para guardar la grabación, pulse $2. Para regrabar, pulse $3."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_record_urgent_check">
    <input pattern="^([0-9#*]):([0-9#*])$">
      <match>
	<action function="speak-text" 
		data="Para marcar este mensaje como urgente, pulse $1. Para continuar, pulse $2."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_invalid_extension">
    <input pattern="^([0-9#*])$">
      <match>
	<action function="speak-text" data="$1 no es una extensión válida."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_forward_message_enter_extension">
    <input pattern="^([0-9#*])$">
      <match>
	<action function="speak-text" data="Introduzca la extensión a la cual quiere enviar este mensaje, seguido de $1"/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_forward_prepend">
    <input pattern="^([0-9#*])$">
      <match>
	<action function="speak-text" data="Para grabar un saludo, pulse $1."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_listen_file_check">
    <input pattern="^([0-9#*]):([0-9#*]):([0-9#*]):([0-9#*]):([0-9#*]):([0-9#*])$">
      <match>
	<action function="speak-text" 
		data="Para escuchar la grabación, pulse $1. Para guardar la grabación, pulse $2. Para eliminar la grabación, pulse $3. Para enviar la grabación a su e-mail, pulse $4. Para devolver la llamada, pulse $5. Para enviar este mensaje a otra extensión, pulse $6."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_choose_greeting">
    <input pattern="^(.*)$">
      <match>
	<action function="speak-text" data="Elija un saludo entre 1 y 9."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_choose_greeting_fail">
    <input pattern="^(.*)$">
      <match>
	<action function="speak-text" data="Valor inválido."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_record_greeting">
    <input pattern="^(.*)$">
      <match>
	<action function="speak-text" data="Grabe su saludo después del tono, pulse cualquier tecla o deje de hablar para poner fin a la grabación."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_record_message">
    <input pattern="^(.*)$">
      <match>
	<action function="speak-text" data="Grabe su mensaje después del tono, pulse cualquier tecla o deje de hablar para poner fin a la grabación."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_greeting_selected">
    <input pattern="^(.*)$">
      <match>
	<action function="speak-text" data="Saludo $1 seleccionado."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_play_greeting">
    <input pattern="^(.*)$">
      <match>
	<action function="speak-text" data="$1 no está disponible."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_say_number">
    <input pattern="^(.*)$">
      <match>
	<action function="speak-text" data="$1"/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_say_message_number">
    <input pattern="^([a-z]+):(.*)$">
      <match>
	<action function="speak-text" data="$1 mensaje número $2."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_say_phone_number">
    <input pattern="^(.*)$">
      <match>
	<action function="speak-text" data="$1."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_say_name">
    <input pattern="^(.*)$">
      <match>
	<action function="speak-text" data="$1."/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_ack">
    <input pattern="^(.*)$">
      <match>
	<action function="speak-text" data="mensaje $1"/>
      </match>
    </input>
  </macro>

  <macro name="voicemail_say_date">
    <input pattern="^(.*)$">
      <match>
	<action function="speak-text" data="${strftime($1|%A, %B %d %Y, %I:%M %p)}"/>
      </match>
    </input>
  </macro>

</include><!--This line will be ignored it's here to validate the xml and is optional -->
