<include><!--This line will be ignored it's here to validate the xml and is optional -->

	<macro name="directory_intro">
		<input pattern="^(last_name)" break_on_match="false">
			<match>
				<action function="speak-text" data="Ingrese las primeras letras del apellido de la persona."/>
			</match>
		</input>
		<input pattern="^(first_name)" break_on_match="false">
			<match>
				<action function="speak-text" data="Ingrese las primeras letras del nombre de la persona."/>
			</match>
		</input>
		<input pattern="^(last_name):([0-9#*])$" break_on_match="false">
			<match>
				<action function="speak-text" data="Para buscar por apellido, pulse $2"/>
			</match>
		</input>
		<input pattern="^(first_name):([0-9#*])$" break_on_match="false">
			<match>
				<action function="speak-text" data="Para buscar por nombre, pulse $2"/>
			</match>
		</input>
	</macro>

	<macro name="directory_min_search_digits">
		<input pattern="^(.*)$">
			<match>
				<action function="speak-text" data="Necesita especificar un mínimo de $1 letras del nombre buscado. Por favor intente de nuevo."/>
			</match>
		</input>
	</macro>

	<macro name="directory_result_count">
		<input pattern="^0$" break_on_match="true">
			<match>
				<action function="speak-text" data="No hay resultados que coincidan con su busqueda. Por favor intente de nuevo."/>
			</match>
		</input>
		<input pattern="^(.*)$">
			<match>
				<action function="speak-text" data="$1 resultados coinciden con su busqueda."/>
			</match>
		</input>
	</macro>

	<macro name="directory_result_count_too_large">
		<input pattern="^(.*)$">
			<match>
				<action function="speak-text" data="Su busqueda genera demasiados resultados. Por favor intente de nuevo."/>
			</match>
		</input>

	</macro>

	<macro name="directory_result_last">
		<input pattern="^(.*)$">
			<match>
				<action function="speak-text" data="No hay más resultados."/>
			</match>
		</input>

	</macro>

	<macro name="directory_result_item">
		<input pattern="^(.*)$">
			<match>
				<action function="speak-text" data="Resultado número $1"/>
			</match>
		</input>
	</macro>

	<macro name="directory_result_menu">
		<input pattern="^([0-9#*]),([0-9#*]),([0-9#*]),([0-9#*])$">
			<match>
				<action function="speak-text" data="Para seleccionar este resultado pulse $1, para el resultado siguiente pulse $2, para el resultado previo pulse $3, para empezar una nueva busqueda pulse $4"/>
			</match>
		</input>
	</macro>

	<macro name="directory_result_at">
		<input pattern="^(.*)$">
			<match>
				<action function="speak-text" data="en la extensión $1"/>
			</match>
		</input>
	</macro>
	<macro name="directory_result_say_name">
		<input pattern="^(.*)$">
			<match>
				<action function="speak-text" data="$1"/>
			</match>
		</input>
	</macro>

</include><!--This line will be ignored it's here to validate the xml and is optional -->
