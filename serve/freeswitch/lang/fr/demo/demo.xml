<include>
  <macro name="msgcount">
    <input pattern="(.*)">
      <match>
        <action function="play-file" data="tuas.wav"/>
        <action function="say" data="$1" method="pronounced" type="items"/>
        <action function="play-file" data="messages.wav"/>
      </match>
    </input>
  </macro>
  <macro name="timeleft">
    <input pattern="(\d+):(\d+)">
      <match>
        <action function="speak-text" data="il reste $1 minutes et $2 secondes"/>
      </match>
    </input>
  </macro>
</include>
<!--
For Emacs:
Local Variables:
mode:xml
indent-tabs-mode:nil
tab-width:2
c-basic-offset:2
End:
For VIM:
vim:set softtabstop=2 shiftwidth=2 tabstop=2 expandtab:
-->
