<include>
  <language name="fr" say-module="fr" sound-prefix="$${sounds_dir}/fr/ca/june" tts-engine="cepstral" tts-voice="david">
    <phrases>
      <macros>
        <X-PRE-PROCESS cmd="include" data="demo/demo.xml"/>
        <!-- voicemail_fr_tts is purely implemented with tts, we need a files based implementation too -->
        <X-PRE-PROCESS cmd="include" data="vm/sounds.xml"/>
        <X-PRE-PROCESS cmd="include" data="dir/sounds.xml"/> <!-- dir/tts.xml if you want to use tts and have cepstral -->
      </macros>
    </phrases>
  </language>
</include>
<!--
For Emacs:
Local Variables:
mode:xml
indent-tabs-mode:nil
tab-width:2
c-basic-offset:2
End:
For VIM:
vim:set softtabstop=2 shiftwidth=2 tabstop=2 expandtab:
-->
