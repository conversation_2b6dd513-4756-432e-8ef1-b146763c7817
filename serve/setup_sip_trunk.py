#!/usr/bin/env python3
"""
<PERSON>ript to create SIP trunk and dispatch rule using LiveKit API directly
"""

import requests
import json
import base64
import hashlib
import hmac
import time
import os
from datetime import datetime, timedelta

# LiveKit API configuration
LIVEKIT_URL = "http://32016-51127.bacloud.info:7880"
LIVEKIT_API_KEY = "lk_2f7c8d9a1b3e4f6g"
LIVEKIT_API_SECRET = "lk_3h9k1m2n4p6q8r0s5t7v9w0x2z4y6a8b"

def generate_access_token(api_key, api_secret, grant=None):
    """Generate LiveKit access token"""
    import jwt
    
    # Create payload with admin permissions
    now = int(time.time())
    payload = {
        "iss": api_key,
        "sub": api_key,
        "iat": now,
        "exp": now + 3600,  # 1 hour expiry
        "nbf": now - 10,    # Not before (10 seconds ago)
        "video": {
            "room": "*",
            "roomCreate": True,
            "roomList": True,
            "roomRecord": True,
            "roomAdmin": True,
            "roomJoin": True,
            "canPublish": True,
            "canSubscribe": True,
            "canPublishData": True,
            "canUpdateOwnMetadata": True
        },
        "sip": {
            "admin": True,
            "call": True
        }
    }
    
    if grant:
        payload.update(grant)
    
    # Generate token
    token = jwt.encode(payload, api_secret, algorithm="HS256")
    return token

def make_api_request(method, endpoint, data=None):
    """Make authenticated API request to LiveKit"""
    try:
        import jwt
    except ImportError:
        print("PyJWT library required. Install with: pip install PyJWT")
        return None
    
    # Generate access token
    token = generate_access_token(LIVEKIT_API_KEY, LIVEKIT_API_SECRET)
    
    # Prepare headers
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Make request
    url = f"{LIVEKIT_URL.replace('ws://', 'http://').replace('wss://', 'https://')}/twirp/livekit.SIP/{endpoint}"
    
    print(f"Making {method} request to: {url}")
    
    if method.upper() == "POST":
        response = requests.post(url, headers=headers, json=data)
    elif method.upper() == "GET":
        response = requests.get(url, headers=headers)
    else:
        raise ValueError(f"Unsupported method: {method}")
    
    print(f"Response status: {response.status_code}")
    print(f"Response headers: {dict(response.headers)}")
    
    if response.status_code == 200:
        try:
            return response.json()
        except:
            return response.text
    else:
        print(f"Error response: {response.text}")
        return None

def create_sip_trunk():
    """Create SIP outbound trunk"""
    trunk_data = {
        "trunk": {
            "name": "FreeSWITCH Dynamic Outbound Trunk",
            "metadata": "Outbound trunk for LiveKit calls",
            "address": "*************:5080",
            "transport": "UDP",
            "numbers": ["\\+\\d{10,15}"],
            "auth_username": "keyman",
            "auth_password": "keyman123"
        }
    }
    
    print("Creating SIP outbound trunk...")
    result = make_api_request("POST", "CreateSIPOutboundTrunk", trunk_data)
    
    if result:
        print(f"Trunk created successfully: {json.dumps(result, indent=2)}")
        return result.get("sip_trunk_id")
    else:
        print("Failed to create trunk")
        return None

def delete_sip_trunk(trunk_id):
    """Delete SIP outbound trunk"""
    delete_data = {
        "sip_trunk_id": trunk_id
    }
    
    print(f"Deleting SIP outbound trunk {trunk_id}...")
    result = make_api_request("POST", "DeleteSIPOutboundTrunk", delete_data)
    
    if result:
        print(f"SIP outbound trunk deleted successfully")
        return True
    else:
        print("Failed to delete SIP outbound trunk")
        return False

def delete_dispatch_rule(rule_id):
    """Delete SIP dispatch rule"""
    delete_data = {
        "sip_dispatch_rule_id": rule_id
    }
    
    print(f"Deleting dispatch rule {rule_id}...")
    result = make_api_request("POST", "DeleteSIPDispatchRule", delete_data)
    
    if result:
        print(f"Dispatch rule deleted successfully")
        return True
    else:
        print("Failed to delete dispatch rule")
        return False

def create_dispatch_rule(trunk_id):
    """Create SIP dispatch rule"""
    dispatch_data = {
        "trunk_ids": [trunk_id],
        "name": "extension-888-rule",
        "metadata": "Route extension 888 to agent room",
        "rule": {
            "dispatchRuleIndividual": {
                "roomPrefix": "inbound-call-"
            }
        }
    }
    
    print(f"Creating dispatch rule for trunk {trunk_id}...")
    result = make_api_request("POST", "CreateSIPDispatchRule", dispatch_data)
    
    if result:
        print(f"Dispatch rule created successfully: {json.dumps(result, indent=2)}")
        return result.get("sip_dispatch_rule_id")
    else:
        print("Failed to create dispatch rule")
        return None

def list_trunks():
    """List existing SIP outbound trunks"""
    print("Listing existing SIP outbound trunks...")
    result = make_api_request("POST", "ListSIPOutboundTrunk", {})
    
    if result:
        print(f"Existing outbound trunks: {json.dumps(result, indent=2)}")
        return result
    else:
        print("Failed to list outbound trunks")
        return None

def list_dispatch_rules():
    """List existing dispatch rules"""
    print("Listing existing dispatch rules...")
    result = make_api_request("POST", "ListSIPDispatchRule", {})
    
    if result:
        print(f"Existing dispatch rules: {json.dumps(result, indent=2)}")
        return result
    else:
        print("Failed to list dispatch rules")
        return None

def cleanup_existing_outbound_trunks():
    """Delete all existing SIP outbound trunks"""
    print("Cleaning up existing outbound trunks...")
    trunks = list_trunks()
    
    deleted_any = False
    if trunks and "items" in trunks:
        for trunk in trunks["items"]:
            trunk_id = trunk["sip_trunk_id"]
            print(f"🗑️  Deleting existing outbound trunk: {trunk_id}")
            if delete_sip_trunk(trunk_id):
                deleted_any = True
                print(f"✅ Deleted trunk {trunk_id}")
            else:
                print(f"❌ Failed to delete trunk {trunk_id}")
    
    if deleted_any:
        print("✅ Cleanup completed")
    else:
        print("ℹ️  No existing outbound trunks found")
    
    return deleted_any

def main():
    """Main function"""
    print("=== LiveKit SIP Trunk and Dispatch Rule Setup ===\n")
    
    # Check if PyJWT is available
    try:
        import jwt
    except ImportError:
        print("ERROR: PyJWT library is required.")
        print("Install with: pip install PyJWT")
        return
    
    # Clean up existing outbound trunks
    print("1. Cleaning up existing outbound trunks...")
    cleanup_existing_outbound_trunks()
    print()
    
    # Create new outbound trunk
    print("2. Creating new SIP outbound trunk...")
    trunk_id = create_sip_trunk()
    
    if not trunk_id:
        print("Failed to create outbound trunk. Exiting.")
        return
    
    print(f"✅ Created outbound trunk with ID: {trunk_id}")
    print()
    
    # Create dispatch rule
    print("3. Creating dispatch rule...")
    rule_id = create_dispatch_rule(trunk_id)
    
    if not rule_id:
        print("Failed to create dispatch rule. Exiting.")
        return
    
    print(f"✅ Created dispatch rule with ID: {rule_id}")
    print()
    
    # Save configuration for persistence
    config = {
        "trunk_id": trunk_id,
        "dispatch_rule_id": rule_id,
        "created_at": datetime.now().isoformat()
    }
    
    with open("sip_trunk_config.json", "w") as f:
        json.dump(config, f, indent=2)
    
    print("✅ Configuration saved to sip_trunk_config.json")
    print("\n=== Setup Complete ===")
    print(f"Trunk ID: {trunk_id}")
    print(f"Dispatch Rule ID: {rule_id}")
    print("Extension 888 is now configured to route to LiveKit agent rooms!")

if __name__ == "__main__":
    main()