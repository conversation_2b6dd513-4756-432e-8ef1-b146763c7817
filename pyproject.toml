[project]
name = "AdvAgency_main"
description = "Default template for PDM package"
version = "0.1.0"
dependencies = [
    "typer[all]>=0.9.0",
    "rich>=13.0.0",
    "livekit==1.0.12",
    "livekit-agents==1.2.6",
    "livekit-plugins-deepgram==1.2.6",
    "livekit-plugins-elevenlabs==1.2.6",
    "livekit-plugins-openai==1.2.6",
    "livekit-plugins-turn-detector==1.2.6",
    "livekit-plugins-silero==1.2.6",
    "livekit-plugins-playai==1.2.6",
    "livekit-plugins-anam==1.2.6",
    "livekit-plugins-bey==1.2.6",
    "livekit-plugins-groq==1.2.6",
    "pydantic-settings==2.8.1",
    "sentry_sdk==2.35.1",
     "numpy==2.2.4",
  "protobuf>=5.29.4",
  "aiofiles==24.1.0",
  "aiohappyeyeballs==2.4.4",
  "aiohttp==3.11.11",
  "aiosignal==1.3.2",
  "anyio==4.8.0",
  "attrs==24.2.0",
  "click==8.1.7",
  "frozenlist==1.5.0",
  "idna==3.10",
  "av==14.3.0",
  "multidict==6.1.0",
  "PyJWT==2.10.1",
  "sniffio==1.3.1",
  "typing_extensions==4.12.2",
  "watchfiles==1.0.5",
  "yarl==1.18.0",
  "langfuse==2.60.2",
  "annotated-types==0.7.0",
  "certifi==2024.8.30",
  "charset-normalizer==3.4.0",
  "coloredlogs==15.0.1",
  "distro==1.9.0",
  "filelock==3.16.1",
  "flatbuffers==24.3.25",
  "h11==0.14.0",
  "httpcore==1.0.7",
  "httpx==0.28.1",
  "humanfriendly==10.0",
  "Jinja2==3.1.4",
  "jiter==0.8.0",
  "MarkupSafe==3.0.2",
  "mpmath==1.3.0",
  "onnxruntime==1.20.1",
    "openai==1.100.2",
  "packaging==24.2",
  "pillow==11.2.1",
  "propcache==0.2.0",
  "psutil==7.0.0",
  "pydantic==2.10.4",
  "pydantic_core==2.27.2",
  "pydantic_settings==2.8.1",
  "pydub==0.25.1",
  "PyYAML==6.0.2",
  "regex==2024.11.6",
  "requests==2.32.3",
  "sympy==1.13.3",
  "tqdm==4.67.1",
    "urllib3==2.3.0",
    "redis==5.0.1",
    "livekit-plugins-noise-cancellation==0.2.4",

    "pandas>=2.0.0",
    "python-dotenv>=1.0.0",
    "tensorflow>=2.13.0",
    "opentelemetry-api>=1.20.0",
    "opentelemetry-sdk>=1.20.0",
    "opentelemetry-exporter-otlp>=1.20.0",
    "opentelemetry-instrumentation>=0.41b0",
    "opentelemetry-distro>=0.41b0",
    "opentelemetry-instrumentation-logging>=0.57b0",
]
requires-python = "==3.12.*"
authors = [
    {name = "kvikster", email = "<EMAIL>"},
]
readme = "README.md"
license = {text = "MIT"}

[project.optional-dependencies]
extra = [
]
[tool.pdm]
distribution = false
[tool.pdm.dev-dependencies]
test = [
]
