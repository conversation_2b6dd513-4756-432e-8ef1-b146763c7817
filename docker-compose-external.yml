name: advagency_main
services:
  app-agent:
    build:
      context: .
      dockerfile: Dockerfile

    environment:
      - ENV=development
      - LIVEKIT_URL=${LIVEKIT_URL}
      - LIVEKIT_API_KEY=${LIVEKIT_API_KEY}
      - LIVEKIT_API_SECRET=${LIVEKIT_API_SECRET}
      - ELEVENLABS_API_KEY=${ELEVENLABS_API_KEY}
      - LANGFUSE_SECRET_KEY=${LANGFUSE_SECRET_KEY}
      - LANGFUSE_PUBLIC_KEY=${LANGFUSE_PUBLIC_KEY}
      - LANGFUSE_HOST=${LANGFUSE_HOST}
      - DEEPGRAM_API_KEY=${DEEPGRAM_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - CORE_API_URL=${CORE_API_URL}
      - CORE_API_LOGIN=${CORE_API_LOGIN}
      - CORE_API_PASSWORD=${CORE_API_PASSWORD}
      - CONVERSATION_API_URL=${CONVERSATION_API_URL}
      - CONVERSATION_API_LOGIN=${CONVERSATION_API_LOGIN}
      - CONVERSATION_API_PASSWORD=${CONVERSATION_API_PASSWORD}
      - CALL_AGENT_ID=${CALL_AGENT_ID}
      - STT_PLUGIN=${STT_PLUGIN}
      - ASSEMBLYAI_API_KEY=${ASSEMBLYAI_API_KEY}
      - SENTRY_DSN=${SENTRY_DSN}
    networks:
      - call-agent
      - traefik-public
    extra_hosts:
      - "dev.slife.guru:**********"
    env_file:
      - .env

  freeswitch:
    container_name: freeswitch-minimal
    build:
      context: ./serve/freeswitch-minimal
      dockerfile: Dockerfile
    image: freeswitch-custom
    platform: linux/amd64
    network_mode: host
    privileged: true
    volumes:
      - /opt/freeswitch-recordings:/opt/recordings
    environment:
      - SOUND_RATES=8000:16000
      - SOUND_TYPES=music:en-us-callie
      - FREESWITCH_IP=*************
      - FS_EXTERNAL_IP=*************
      - FS_RTP_PORT_RANGE=15000-20000
      - RTP_EXTERNAL_IP=*************
      - SIP_EXTERNAL_IP=*************
      - EVENT_SOCKET_PORT=8021
    restart: unless-stopped
    cap_add:
      - SYS_NICE

  livekit-sip:
    container_name: livekit-sip
    image: livekit/sip:latest
    ports:
      - '5070:5070/tcp'
      - '5070:5070/udp'
      - '15000-15050:15000-15050/udp'
    networks:
      - call-agent
    environment:
      SIP_CONFIG_BODY: |
        api_key: 'lk_2f7c8d9a1b3e4f6g'
        api_secret: 'lk_3h9k1m2n4p6q8r0s5t7v9w0x2z4y6a8b'
        ws_url: 'ws://livekit:7880'
        redis:
          address: 'redis-agent:6379'
          password: 'call-agent'
        sip_port: 5070
        rtp_port: 15000-15050
        use_external_ip: true
        inbound_numbers:
          - number: "888"
            agent:
              name: "banking_agent"
              metadata:
                agent_type: "emma"
        logging:
          level: debug

  redis:
    image: redis:7.2.4-alpine
    container_name: redis-agent
    command: redis-server --requirepass call-agent
    ports:
      - '6379:6379'
    networks:
      - call-agent

  livekit:
    container_name: livekit
    image: livekit/livekit-server:v1.8.0
    ports:
      - '30000-30100:30000-30100/udp'
      - '7881:7881/tcp'
      - '7880:7880'
    environment:
      LIVEKIT_KEYS: "lk_2f7c8d9a1b3e4f6g: lk_3h9k1m2n4p6q8r0s5t7v9w0x2z4y6a8b"
      REDIS_HOST: redis:6379
      REDIS_PASSWORD: call-agent
      UDP_PORT: 30000-30100
      LIVEKIT_NODE_IP: "0.0.0.0"
      LIVEKIT_CONFIG: |
        logging:
           level: debug
    networks:
      call-agent:

networks:
  call-agent:
    name: call-agent
  traefik-public:
    external: true

volumes:
  jaeger_badger_data:


