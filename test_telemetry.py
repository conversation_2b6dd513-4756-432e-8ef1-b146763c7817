#!/usr/bin/env python3
"""
Simple test script to verify telemetry integration with <PERSON><PERSON><PERSON>
"""
import time
import logging
from src.telemetry_config import setup_telemetry, get_tracer

# Setup logging
logging.basicConfig(level=logging.INFO)

def test_telemetry():
    """Test telemetry setup and create sample traces"""
    print("Setting up telemetry...")
    setup_telemetry()
    
    # Get tracer
    tracer = get_tracer("test-telemetry")
    
    print("Creating test traces...")
    
    # Create a simple trace
    with tracer.start_as_current_span("test_operation") as span:
        span.set_attribute("test.operation", "sample_test")
        span.set_attribute("test.version", "1.0.0")
        
        print("Executing test operation...")
        time.sleep(0.1)  # Simulate some work
        
        # Create a child span
        with tracer.start_as_current_span("test_child_operation") as child_span:
            child_span.set_attribute("child.operation", "sample_child")
            child_span.set_attribute("child.duration", "100ms")
            
            print("Executing child operation...")
            time.sleep(0.05)  # Simulate some work
        
        span.set_attribute("test.completed", True)
    
    print("Test traces created successfully!")
    print("Check <PERSON>aeger UI at http://localhost:16686 to view traces")
    print("Look for service: 'advagency-conversation-manager'")

if __name__ == "__main__":
    test_telemetry()