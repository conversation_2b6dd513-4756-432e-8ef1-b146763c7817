# Telemetry and Environment Modes

This application supports multiple telemetry and environment modes that automatically configure based on your setup.

## 🏠 Local Development Mode

**When to use:** Local development, testing, debugging

**How to run:**
```bash
./run_local.sh dev --agent amber.json
```

**What happens:**
- ✅ Environment automatically set to `'local'`
- ✅ Telemetry/SigNoz disabled
- ✅ Logs only to console and `app.log` file
- ✅ No external dependencies
- ✅ Fast startup

**Automatic triggers:**
- No telemetry configuration provided
- `OTEL_SDK_DISABLED=true`
- Using default SigNoz credentials (detected as development)

---

## 🔭 Instrumented Mode (SigNoz)

**When to use:** Production, staging, monitoring

**How to run:**
```bash
./run_with_telemetry.sh dev --agent amber.json
```

**What happens:**
- ✅ Full telemetry enabled
- ✅ Logs and traces sent to SigNoz
- ✅ Environment set to `'production'` (or `'local'` if using defaults)
- ✅ Complete observability
- ✅ Performance metrics

**Configuration:**
Update `run_with_telemetry.sh` with your SigNoz credentials:
```bash
export OTEL_EXPORTER_OTLP_ENDPOINT=https://your-signoz-instance.com:443
export OTEL_EXPORTER_OTLP_HEADERS=signoz-access-token=your-actual-token
```

---

## 🤖 Automatic Environment Detection

The application automatically detects the appropriate environment:

| Scenario | Environment | Telemetry | Description |
|----------|-------------|-----------|-------------|
| No config | `local` | Disabled | Pure local development |
| SDK disabled | `local` | Disabled | Explicitly disabled |
| Default credentials | `local` | Enabled | Development with SigNoz |
| Custom credentials | `production` | Enabled | Production deployment |

---

## 📊 Environment Variables

### Core Variables
- `OTEL_DEPLOYMENT_ENVIRONMENT` - Deployment environment
- `ENV` - Application environment
- `OTEL_SDK_DISABLED` - Disable telemetry entirely

### SigNoz Configuration
- `OTEL_EXPORTER_OTLP_ENDPOINT` - SigNoz endpoint
- `OTEL_EXPORTER_OTLP_HEADERS` - Authentication headers
- `SIGNOZ_ENDPOINT` - Alternative endpoint variable
- `SIGNOZ_INGESTION_KEY` - Alternative auth variable

### Service Information
- `OTEL_SERVICE_NAME` - Service name in SigNoz
- `OTEL_SERVICE_VERSION` - Service version
- `LOG_LEVEL` - Logging level

---

## 🔍 Verification

### Check Current Mode
Look for these log messages on startup:

**Local Mode:**
```
No telemetry configuration found - setting environment to 'local'
```

**Instrumented Mode:**
```
Setting up telemetry manually
Telemetry configured successfully. Endpoint: https://...
```

### SigNoz Dashboard
When in instrumented mode, check your SigNoz dashboard for:
- Service: `advagency-conversation-manager`
- Logs with proper correlation to traces
- Performance metrics and spans

---

## 🛠️ Troubleshooting

### Logs Not Appearing in SigNoz
1. Check endpoint and credentials in `run_with_telemetry.sh`
2. Verify SigNoz is accessible from your network
3. Look for telemetry setup messages in console output

### Duplicate Logs
- Should be resolved automatically
- If issues persist, check for multiple logging handlers

### Environment Not Set Correctly
- Check the startup logs for environment detection messages
- Verify your configuration matches the expected patterns

---

## 📝 Examples

### Local Development
```bash
# Simple local development
./run_local.sh dev --agent amber.json

# Or just run without any telemetry config
pdm run python src/main.py dev --agent amber.json
```

### Production Deployment
```bash
# With proper SigNoz configuration
export OTEL_EXPORTER_OTLP_ENDPOINT=https://your-signoz.com:443
export OTEL_EXPORTER_OTLP_HEADERS=signoz-access-token=your-token
./run_with_telemetry.sh start --agent amber.json
```

### Custom Environment
```bash
# Override environment manually
export OTEL_DEPLOYMENT_ENVIRONMENT=staging
./run_with_telemetry.sh start --agent amber.json
```
