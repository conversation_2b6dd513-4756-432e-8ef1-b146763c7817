#!/usr/bin/env python3
"""
<PERSON>ript to make dynamic outbound calls via LiveKit to any phone number
Supports both single calls and batch processing from CSV files
"""

import asyncio
import csv
import json
import os
import shutil
import subprocess
import sys
import tempfile
from datetime import datetime, timezone
from pathlib import Path

from call_monitor import CallMonitor

# Try to import LiveKit SDK for monitoring
try:
    from livekit import api

    LIVEKIT_SDK_AVAILABLE = True
except ImportError:
    LIVEKIT_SDK_AVAILABLE = False


# Try to load environment variables from .env file
try:
    from dotenv import load_dotenv

    load_dotenv()
    print("🔧 Environment variables loaded from .env file")
except ImportError:
    print("⚠️  python-dotenv not available, using system environment variables")

# Interval between status checks (seconds)
CHECK_INTERVAL = 3

# Results directory
RESULTS_DIR = "call_results"

# Recording configuration
REMOTE_HOST = "<EMAIL>"
REMOTE_RECORDINGS_DIR = "/opt/freeswitch-recordings"
LOCAL_RECORDINGS_DIR = "./recordings"


async def create_livekit_room(room_name):
    """
    Create a new LiveKit room.

    Args:
        room_name (str): The name of the room to create.

    Returns:
        The created room object.
    """
    livekit_api = api.LiveKitAPI(os.getenv("LIVEKIT_URL"), os.getenv("LIVEKIT_API_KEY"), os.getenv("LIVEKIT_API_SECRET"))
    try:
        room = await livekit_api.room.create_room(api.CreateRoomRequest(name=room_name))
        print(f"✅ Room '{room_name}' created successfully.")
        return room
    finally:
        await livekit_api.aclose()


async def download_call_recording(phone_number, room_name, target_dir=None, timeout_seconds=30):
    """
    Download call recording for a specific phone number and move it to target directory
    
    Args:
        phone_number (str): Phone number to find recording for
        room_name (str): Room name for naming the recording file
        target_dir (Path): Target directory to save recording (defaults to RESULTS_DIR)
        timeout_seconds (int): Maximum time to wait for recording to appear
    
    Returns:
        str: Path to downloaded recording file, or None if not found
    """
    try:
        # Clean phone number for filename matching
        expected_filename = f"{phone_number}.wav"
        
        print(f"   🎵 Looking for recording: {expected_filename}")
        
        # Create local recordings directory
        recordings_path = Path(LOCAL_RECORDINGS_DIR)
        recordings_path.mkdir(exist_ok=True)
        
        # Use target directory or default to RESULTS_DIR
        results_path = target_dir if target_dir else Path(RESULTS_DIR)
        results_path.mkdir(parents=True, exist_ok=True)
        
        # Wait for recording to appear and download it (max 3 attempts)
        max_attempts = 3
        for attempt in range(max_attempts):
            try:
                # Download recordings from remote host
                print(f"   📥 Downloading recordings from remote host (attempt {attempt + 1}/{max_attempts})...")
                result = subprocess.run([
                    'rsync', '-avz', '--progress',
                    f'{REMOTE_HOST}:{REMOTE_RECORDINGS_DIR}/',
                    f'{LOCAL_RECORDINGS_DIR}/'
                ], capture_output=True, text=True, timeout=30)
                
                if result.returncode != 0:
                    print(f"   ⚠️  rsync failed: {result.stderr}")
                    if attempt < max_attempts - 1:  # Don't sleep on last attempt
                        await asyncio.sleep(5)
                    continue
                
                # Check if our recording file exists
                recording_file = recordings_path / expected_filename
                if recording_file.exists():
                    # Move recording to target directory with room name
                    final_recording_path = results_path / f"recording_{room_name}.wav"
                    shutil.copy2(recording_file, final_recording_path)
                    
                    print(f"   ✅ Recording downloaded and saved: {final_recording_path}")
                    return str(final_recording_path)
                
                if attempt < max_attempts - 1:  # Don't wait after last attempt
                    print(f"   ⏳ Recording not ready yet, waiting 5 seconds...")
                    await asyncio.sleep(5)
                
            except subprocess.TimeoutExpired:
                print(f"   ⚠️  rsync timeout, retrying...")
                if attempt < max_attempts - 1:  # Don't sleep on last attempt
                    await asyncio.sleep(2)
                continue
            except Exception as e:
                print(f"   ⚠️  Error during recording download: {e}")
                if attempt < max_attempts - 1:  # Don't sleep on last attempt
                    await asyncio.sleep(5)
                continue
        
        print(f"   ❌ Recording not found after {max_attempts} attempts")
        return None
        
    except Exception as e:
        print(f"   ❌ Failed to download recording: {e}")
        return None


async def make_outbound_call(phone_number, room_name=None, participant_name=None, agent_identity="agent", metadata=None):
    """
    Make an outbound call by first creating a room, waiting for an agent to join,
    and then adding the remote participant.

    Args:
        phone_number (str): Phone number in international format.
        room_name (str): Optional custom room name.
        participant_name (str): Optional custom participant name.
        agent_identity (str): The identity of the agent to wait for.
        metadata (dict): Additional metadata to include in the dispatch request.

    Returns:
        dict: Call details.
    """
    if not phone_number.startswith('+'):
        phone_number = "+" + phone_number

    if not room_name:
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H")
        room_name = f"call-{phone_number}-{timestamp}"

    if not participant_name:
        participant_name = f"Caller to {phone_number}"

    # 1. Create a room
    await create_livekit_room(room_name)

    # 2. Prepare metadata
    call_metadata = {
        "context":{
            "participant":{
                "phone": phone_number,
                "name": participant_name,
                "email": metadata.get("email", ""),
                "country": metadata.get("country", ""),
                "timestamp": datetime.now().isoformat()
            }
        }
    }
    
    # Add any additional metadata
    # if metadata and isinstance(metadata, dict):
    #     call_metadata.update(metadata)

    print(f"metadata: {metadata}")
    
    # 2.1. Set room metadata (this is the correct way to set room metadata)
    livekit_api = api.LiveKitAPI(os.getenv("LIVEKIT_URL"), os.getenv("LIVEKIT_API_KEY"), os.getenv("LIVEKIT_API_SECRET"))
    try:
        await livekit_api.room.update_room_metadata(
            api.UpdateRoomMetadataRequest(
                room=room_name,
                metadata=json.dumps(call_metadata, ensure_ascii=False)
            )
        )
        print(f"✅ Room metadata set successfully for room '{room_name}'")
    except Exception as e:
        print(f"⚠️ Warning: Failed to set room metadata: {e}")
    finally:
        await livekit_api.aclose()

    # 3. Dispatch an agent to the room
    print(f"🚀 Dispatching agent '{agent_identity}' to room '{room_name}'...")
    livekit_api = api.LiveKitAPI(os.getenv("LIVEKIT_URL"), os.getenv("LIVEKIT_API_KEY"), os.getenv("LIVEKIT_API_SECRET"))
    try:
        dispatch = await livekit_api.agent_dispatch.create_dispatch(
            api.CreateAgentDispatchRequest(
                agent_name=agent_identity,
                room=room_name,
                metadata=json.dumps(call_metadata, ensure_ascii=False),
            )
        )
        print(f"✅ Agent dispatch created successfully. Dispatch ID: {dispatch.id}")
        # The agent worker will now automatically join the room.
        # A short delay to allow the agent to connect before adding the participant.
        await asyncio.sleep(10)
    finally:
        await livekit_api.aclose()


    # 4. Add the remote participant
    print(f"📞 Adding remote participant {phone_number} to the room...")
    participant_config = {
        "sip_trunk_id": "ST_EzgYeBNxFiPd",
        "sip_call_to": phone_number,
        "room_name": room_name,
        "participant_identity": f"caller-{phone_number.replace('+', '').replace(' ', '')}",
        "participant_name": participant_name
    }

    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
        json.dump(participant_config, temp_file, indent=2)
        temp_file_path = temp_file.name

    try:
        result = subprocess.run(
            ['lk', 'sip', 'participant', 'create', temp_file_path],
            capture_output=True, text=True, check=True
        )

        output_lines = result.stdout.strip().split('\n')
        call_details = {}
        for line in output_lines:
            if line.startswith('SIPCallID:'):
                call_details['call_id'] = line.split(': ')[1]
            elif line.startswith('ParticipantID:'):
                call_details['participant_id'] = line.split(': ')[1]

        call_details.update({
            'room_name': room_name,
            'phone_number': phone_number,
            'participant_name': participant_name,
            'timestamp': datetime.now().isoformat(),
            'config_used': participant_config
        })
        return call_details
    except subprocess.CalledProcessError as e:
        raise RuntimeError(f"Failed to create SIP participant: {e.stderr}")
    finally:
        os.unlink(temp_file_path)


async def monitor_call_status(room_name, participant_id, timeout_seconds=1800):
    """
    Monitor call status using enhanced CallMonitor class
    
    Args:
        room_name (str): LiveKit room name
        participant_id (str): Participant ID to monitor
        timeout_seconds (int): Maximum time to wait for call status
    
    Returns:
        dict: Enhanced call status information with detailed metrics
    """
    monitor = CallMonitor(room_name, participant_id)
    result = await monitor.start_monitoring(timeout_seconds)
    
    # Print detailed timeline for debugging
    if result.get('events'):
        print(f"\n📊 Call Timeline for {room_name}:")
        timeline = monitor._generate_timeline()
        for entry in timeline:
            print(f"   {entry['time']}: {entry['description']}")
    
    # Print metrics summary
    metrics = result.get('metrics', {})
    if metrics:
        print(f"\n📈 Call Metrics:")
        if metrics.get('time_to_first_join'):
            print(f"   ⏱️  Time to first join: {metrics['time_to_first_join']:.1f}s")
        if metrics.get('actual_call_duration'):
            print(f"   📞 Actual call duration: {metrics['actual_call_duration']:.1f}s")
        if metrics.get('max_concurrent_participants'):
            print(f"   👥 Max participants: {metrics['max_concurrent_participants']}")
        print(f"   🔍 Total monitoring time: {metrics.get('monitoring_duration', 0):.1f}s")
    
    return result


def export_results_to_csv(results, csv_filename):
    """
    Export call results to CSV file

    Args:
        results (dict): Results from batch processing
        csv_filename (str): Output CSV filename
    """
    try:
        # Create results directory if it doesn't exist
        results_path = Path(RESULTS_DIR)
        results_path.mkdir(exist_ok=True)
        
        # Save CSV in results directory
        csv_output_path = results_path / csv_filename
        
        with open(csv_output_path, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'row', 'name', 'phone', 'call_status', 'call_id',
                'participant_id', 'room_name', 'connected', 'duration',
                'call_duration', 'disconnect_reason', 'sip_status', 'error_reason', 'timestamp'
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            # Write successful calls
            for call in results.get('successful_calls', []):
                call_details = call['call_details']
                status_info = call_details.get('status_info', {})

                # Use business status directly from CallMonitor
                business_status = status_info.get('business_call_status', 'UNKNOWN')
                
                writer.writerow({
                    'row': call['row'],
                    'name': call['name'],
                    'phone': call_details['phone_number'],
                    'call_status': business_status,
                    'call_id': call_details.get('call_id', ''),
                    'participant_id': call_details.get('participant_id', ''),
                    'room_name': call_details.get('room_name', ''),
                    'connected': status_info.get('connected', False),
                    'duration': status_info.get('duration', 0),
                    'call_duration': status_info.get('call_duration', 0),
                    'disconnect_reason': status_info.get('disconnect_reason', ''),
                    'sip_status': status_info.get('sip_status', ''),
                    'error_reason': '',
                    'timestamp': call_details.get('timestamp', '')
                })

            # Write failed calls
            for call in results.get('failed_calls', []):
                writer.writerow({
                    'row': call['row'],
                    'name': call['name'],
                    'phone': call['phone'],
                    'call_status': 'failed',
                    'call_id': '',
                    'participant_id': '',
                    'room_name': '',
                    'connected': False,
                    'duration': 0,
                    'call_duration': 0,
                    'disconnect_reason': 'initiation_failed',
                    'sip_status': '',
                    'error_reason': call['error'],
                    'timestamp': datetime.now().isoformat()
                })

        print(f"📊 Results exported to CSV: {csv_output_path}")

    except Exception as e:
        print(f"⚠️  Failed to export CSV: {e}")


def map_call_status(status_info):
    """
    Map technical monitoring data to clear business call statuses
    
    Args:
        status_info (dict): Status information from call monitoring
        
    Returns:
        str: Clear business status (ANSWERED, NOT_ANSWERED, HANGUP, BUSY, etc.)
    """
    if not status_info:
        return 'FAILED'
    
    connected = status_info.get('connected', False)
    disconnect_reason = status_info.get('disconnect_reason', '')
    call_duration = status_info.get('call_duration', 0)
    sip_status = status_info.get('sip_status', '')
    
    # Call was never connected
    if not connected:
        if disconnect_reason == 'no_answer':
            return 'NOT_ANSWERED'
        elif disconnect_reason == 'call_rejected':
            return 'BUSY'
        elif disconnect_reason == 'no_connection':
            return 'NOT_ANSWERED'
        else:
            return 'FAILED'
    
    # Call was connected - determine how it ended
    if connected:
        # Very short calls (< 3 seconds) after connection are likely immediate hangups
        if call_duration < 3:
            return 'IMMEDIATE_HANGUP'
        
        # Check SIP status for hangup detection
        if sip_status == 'hangup' or disconnect_reason == 'user_hangup':
            return 'HANGUP'
        
        # Normal call completion scenarios
        if disconnect_reason in ['room_empty', 'participant_left', 'agent_alone']:
            return 'ANSWERED'  # These are normal call completions
        
        if disconnect_reason == 'normal_completion':
            return 'ANSWERED'
        
        if disconnect_reason == 'quick_hangup':
            return 'HANGUP'
        
        # Default for connected calls
        return 'ANSWERED'
    
    return 'UNKNOWN'


def export_results_to_csv_in_batch(results, batch_folder, csv_filename):
    """
    Export call results to CSV file in specific batch folder
    
    Args:
        results (dict): Results from batch processing
        batch_folder (Path): Batch folder path
        csv_filename (str): Output CSV filename
    """
    try:
        csv_output_path = batch_folder / csv_filename
        
        with open(csv_output_path, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'row', 'name', 'phone', 'call_status', 'room_name',
                'connected', 'monitoring_duration', 'call_duration', 
                'disconnect_reason', 'sip_status', 'error_reason', 'timestamp'
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            # Write successful calls
            for call in results.get('successful_calls', []):
                call_details = call['call_details']
                status_info = call_details.get('status_info', {})

                # Use business status directly from CallMonitor
                business_status = status_info.get('business_call_status', 'UNKNOWN')
                
                writer.writerow({
                    'row': call['row'],
                    'name': call['name'],
                    'phone': call_details['phone_number'],
                    'call_status': business_status,
                    'room_name': call_details.get('room_name', ''),
                    'connected': status_info.get('connected', False),
                    'monitoring_duration': status_info.get('monitoring_duration', 0),
                    'call_duration': status_info.get('call_duration', 0),
                    'disconnect_reason': status_info.get('disconnect_reason', ''),
                    'sip_status': status_info.get('sip_status', ''),
                    'error_reason': '',
                    'timestamp': call_details.get('timestamp', '')
                })

            # Write failed calls
            for call in results.get('failed_calls', []):
                writer.writerow({
                    'row': call['row'],
                    'name': call['name'],
                    'phone': call['phone'],
                    'call_status': 'failed',
                    'room_name': '',
                    'connected': False,
                    'monitoring_duration': 0,
                    'call_duration': 0,
                    'disconnect_reason': 'initiation_failed',
                    'sip_status': '',
                    'error_reason': call['error'],
                    'timestamp': datetime.now().isoformat()
                })

        print(f"📊 Results exported to CSV: {csv_output_path}")

    except Exception as e:
        print(f"⚠️  Failed to export CSV: {e}")


async def process_csv_calls_async(csv_filename, max_rows=None, delay_seconds=5, monitor_calls=True):
    """
    Process calls from CSV file one by one with monitoring

    Args:
        csv_filename (str): Path to CSV file with format: Name;Phone;Email;Country;
        max_rows (int): Maximum number of rows to process (None for all)
        delay_seconds (int): Delay between calls in seconds
        monitor_calls (bool): Whether to monitor call status after initiation

    Returns:
        dict: Results including successful and failed calls
    """
    successful_calls = []
    failed_calls = []
    
    # Create unique batch folder for this run
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    base_filename = Path(csv_filename).stem
    batch_folder_name = f"batch_{timestamp}_{base_filename}"
    batch_results_path = Path(RESULTS_DIR) / batch_folder_name
    batch_results_path.mkdir(parents=True, exist_ok=True)
    
    print(f"📁 Created batch folder: {batch_results_path}")
    
    # Store batch path for later use
    batch_info = {
        'batch_folder': batch_results_path,
        'batch_name': batch_folder_name,
        'timestamp': timestamp
    }

    try:
        with open(csv_filename, 'r', encoding='utf-8') as csvfile:
            reader = csv.reader(csvfile, delimiter=';')

            for row_num, row in enumerate(reader, 1):
                if max_rows and row_num > max_rows:
                    break

                if len(row) < 2:
                    print(f"⚠️  Row {row_num}: Invalid format (not enough columns)")
                    continue

                name = row[0].strip()
                phone = row[1].strip()

                if not phone:
                    print(f"⚠️  Row {row_num}: Empty phone number for {name}")
                    continue

                # Convert phone to international format if needed
                if not phone.startswith('+'):
                    phone = '+' + phone

                try:
                    print(f"\n🚀 [{row_num}] Making call to {name} at {phone}...")
                    
                    # Prepare metadata from CSV row
                    metadata = {
                        'name': name,
                        'phone': phone,
                        'csv_row': row_num,
                        'call_timestamp': datetime.now().isoformat()
                    }
                    
                    # Add additional CSV columns if they exist
                    if len(row) > 2 and row[2].strip():  # Email
                        metadata['email'] = row[2].strip()
                    if len(row) > 3 and row[3].strip():  # Country
                        metadata['country'] = row[3].strip()
                    
                    call_details = await make_outbound_call(
                        phone_number=phone,
                        participant_name=name,
                        metadata=metadata
                    )
                    print(f"✅ [{row_num}] Call to {name} initiated successfully!")
                    print(f"   📞 Phone: {call_details['phone_number']}")
                    print(f"   🆔 Call ID: {call_details['call_id']}")
                    
                    # Save individual call details to batch folder using room name
                    room_name = call_details['room_name']
                    call_file = batch_info['batch_folder'] / f"{room_name}.json"
                    with open(call_file, 'w') as f:
                        json.dump(call_details, f, indent=2)
                    print(f"   📄 Call details saved to: {call_file}")

                    # Monitor call status if enabled
                    if monitor_calls and call_details.get('participant_id') and call_details.get('room_name'):
                        print(f"   🔍 Monitoring call status...")
                        status_info = await monitor_call_status(
                            call_details['room_name'],
                            call_details['participant_id'],
                            timeout_seconds=1800
                        )
                        call_details['status_info'] = status_info

                        if status_info['connected']:
                            monitoring_duration = status_info.get('monitoring_duration', 0)
                            call_duration = status_info.get('call_duration', 0)
                            print(f"   ✅ Call connected! Call duration: {call_duration:.1f}s, Monitoring: {monitoring_duration:.1f}s")
                            
                            # Download recording after successful call
                            recording_path = await download_call_recording(
                                phone, call_details['room_name'], batch_info['batch_folder'], timeout_seconds=120
                            )
                            if recording_path:
                                call_details['recording_path'] = recording_path
                                print(f"   🎵 Recording saved with call results")
                        else:
                            monitoring_duration = status_info.get('monitoring_duration', 0)
                            print(f"   ⚠️  Call status: {status_info['status']} - {status_info['reason']} (monitored: {monitoring_duration:.1f}s)")
                        
                        # Update saved call details with status info and recording path
                        with open(call_file, 'w') as f:
                            json.dump(call_details, f, indent=2)

                    successful_calls.append({
                        'row': row_num,
                        'name': name,
                        'call_details': call_details
                    })

                except Exception as e:
                    failed_calls.append({
                        'row': row_num,
                        'name': name,
                        'phone': phone,
                        'error': str(e)
                    })
                    print(f"❌ [{row_num}] Failed to call {name}: {e}")

                # Delay between calls
                if row_num < max_rows if max_rows else True:
                    print(f"⏳ Waiting {delay_seconds} seconds before next call...")
                    await asyncio.sleep(delay_seconds)

    except FileNotFoundError:
        raise FileNotFoundError(f"CSV file not found: {csv_filename}")
    except Exception as e:
        raise RuntimeError(f"Error processing CSV file: {e}")

    return {
        'csv_file': csv_filename,
        'processed_rows': row_num if 'row_num' in locals() else 0,
        'successful_calls': successful_calls,
        'failed_calls': failed_calls,
        'timestamp': datetime.now().isoformat(),
        'batch_info': batch_info
    }


async def main():
    """Main function for command line usage"""
    if len(sys.argv) < 2:
        print("Usage:")
        print("  Single call: python make_dynamic_call.py <phone_number> [room_name] [participant_name]")
        print("  CSV batch:   python make_dynamic_call.py --csv <csv_file> [max_rows] [delay_seconds]")
        print()
        print("Examples:")
        print("  python make_dynamic_call.py +380961717124")
        print("  python make_dynamic_call.py +1234567890 'my-call-room' 'Agent Smith'")
        print("  python make_dynamic_call.py --csv 'NZ 300.csv' 5 10")
        print("  python make_dynamic_call.py --csv 'NZ 300.csv'")
        sys.exit(1)

    # Check if CSV mode
    if sys.argv[1] == '--csv':
        if len(sys.argv) < 3:
            print("❌ Error: CSV filename required")
            print("Usage: python make_dynamic_call.py --csv <csv_file> [max_rows] [delay_seconds]")
            sys.exit(1)

        csv_filename = sys.argv[2]
        max_rows = int(sys.argv[3]) if len(sys.argv) > 3 and sys.argv[3].isdigit() else None
        delay_seconds = int(sys.argv[4]) if len(sys.argv) > 4 and sys.argv[4].isdigit() else 5

        try:
            print(f"🚀 Starting batch calls from {csv_filename}")
            if max_rows:
                print(f"📊 Processing first {max_rows} rows")
            print(f"⏱️  Delay between calls: {delay_seconds} seconds")
            print()

            results = await process_csv_calls_async(csv_filename, max_rows, delay_seconds)

            print(f"\n🎉 Batch processing completed!")
            print(f"✅ Successfully initiated {len(results.get('successful_calls', []))} calls")
            
            # Export results to CSV in batch folder
            batch_info = results.get('batch_info', {})
            output_csv_filename = "results_summary.csv"
            
            # Update export function to use batch folder
            if batch_info.get('batch_folder'):
                export_results_to_csv_in_batch(results, batch_info['batch_folder'], output_csv_filename)
                print(f"📄 Detailed results saved to {batch_info['batch_folder']}/{output_csv_filename}")
            else:
                # Fallback to old method
                export_results_to_csv(results, output_csv_filename)
                print(f"📄 Detailed results saved to call_results/{output_csv_filename}")

        except Exception as e:
            print(f"❌ Batch processing failed: {e}")
            sys.exit(1)

    else:
        # Single call mode (original functionality)
        phone_number = sys.argv[1]
        room_name = sys.argv[2] if len(sys.argv) > 2 else None
        participant_name = sys.argv[3] if len(sys.argv) > 3 else None

        try:
            print(f"🚀 Making outbound call to {phone_number}...")
            call_details = await make_outbound_call(phone_number, room_name, participant_name)

            print("✅ Call initiated successfully!")
            print(f"📞 Phone Number: {call_details['phone_number']}")
            print(f"🏠 Room Name: {call_details['room_name']}")
            print(f"👤 Participant: {call_details['participant_name']}")
            print(f"🆔 Call ID: {call_details['call_id']}")
            print(f"👥 Participant ID: {call_details['participant_id']}")

            # Create results directory if it doesn't exist
            results_path = Path(RESULTS_DIR)
            results_path.mkdir(exist_ok=True)
            
            # Save call details to file in results directory
            output_file = results_path / f"call_details_{call_details['call_id']}.json"
            with open(output_file, 'w') as f:
                json.dump(call_details, f, indent=2)

            print(f"📄 Call details saved to: {output_file}")
            
            # For single calls, we could also download recording here if needed
            # But typically single calls are for testing, so we'll skip automatic download
            # Users can manually run download_recordings.sh if needed

            print("💡 To connect an agent to this call, run in a separate terminal:")
            print(f"   python join_agent_to_room.py {call_details['room_name']} emma.json")
            print("   OR join the room manually via LiveKit dashboard")

        except Exception as e:
            print(f"❌ Error: {e}")
            sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())