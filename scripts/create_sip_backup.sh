#!/bin/bash
# LiveKit SIP Configuration Backup Creator
# Creates a timestamped backup of all SIP configuration

set -e

TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="backups/livekit-sip-config/$TIMESTAMP"

echo "🚀 Creating LiveKit SIP Configuration Backup"
echo "📅 Timestamp: $TIMESTAMP"
echo "📂 Backup directory: $BACKUP_DIR"

# Create backup directory structure
mkdir -p "$BACKUP_DIR"/{trunks,dispatch-rules,configurations,scripts}

# Export current configurations
echo "📥 Exporting trunk configurations..."
lk sip inbound list > "$BACKUP_DIR/trunks/inbound-trunks-list.txt"
lk sip outbound list > "$BACKUP_DIR/trunks/outbound-trunks-list.txt"
lk sip dispatch list > "$BACKUP_DIR/dispatch-rules/dispatch-rules-list.txt"

# Copy configuration files
echo "📁 Copying configuration files..."
cp -r serve/livekit/conf/* "$BACKUP_DIR/configurations/"
if [ -f "outbound_trunk_config.json" ]; then
    cp outbound_trunk_config.json "$BACKUP_DIR/configurations/"
fi

# Copy utility scripts
echo "📜 Copying utility scripts..."
if [ -f "setup_sip_trunk.py" ]; then
    cp setup_sip_trunk.py "$BACKUP_DIR/scripts/"
fi
if [ -f "make_dynamic_call.py" ]; then
    cp make_dynamic_call.py "$BACKUP_DIR/scripts/"
fi

# Create backup manifest
echo "📋 Creating backup manifest..."
cat > "$BACKUP_DIR/backup-manifest.json" << EOF
{
  "backup_timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "backup_version": "1.0",
  "livekit_project": "next",
  "created_by": "create_sip_backup.sh",
  "backup_contents": {
    "note": "See individual list files for detailed trunk/rule information"
  },
  "configuration_files": [
    "configurations/inbound-trunk.json",
    "configurations/outbound-rule.json", 
    "configurations/participant.json",
    "configurations/dispatch-rule.json",
    "configurations/outbound_trunk_config.json"
  ],
  "scripts": [
    "scripts/setup_sip_trunk.py",
    "scripts/make_dynamic_call.py"
  ],
  "freeswitch_integration": {
    "auth_user": "keyman",
    "auth_password": "keyman123",
    "server_address": "*************:5080",
    "domain": "*************"
  }
}
EOF

# Copy restoration scripts from template
echo "🔧 Creating restoration scripts..."

# Copy the restoration scripts from the latest backup (if exists)
LATEST_BACKUP=$(find backups/livekit-sip-config -name "restore.py" -exec dirname {} \; | head -1)
if [ -n "$LATEST_BACKUP" ]; then
    cp "$LATEST_BACKUP/restore.py" "$BACKUP_DIR/"
    cp "$LATEST_BACKUP/quick_restore.sh" "$BACKUP_DIR/"
    cp "$LATEST_BACKUP/validate_backup.py" "$BACKUP_DIR/"
    cp "$LATEST_BACKUP/README.md" "$BACKUP_DIR/"
    
    # Update README with current timestamp
    sed -i.bak "s/Backup Created.*/Backup Created**: $(date -u +%Y-%m-%dT%H:%M:%SZ)/" "$BACKUP_DIR/README.md"
    rm "$BACKUP_DIR/README.md.bak"
    
    chmod +x "$BACKUP_DIR"/*.py "$BACKUP_DIR"/*.sh
else
    echo "⚠️  No previous backup found - restoration scripts not copied"
    echo "   You may need to create restoration scripts manually"
fi

echo "✅ Backup created successfully!"
echo ""
echo "📂 Backup location: $BACKUP_DIR"
echo "📋 To validate backup: cd $BACKUP_DIR && python validate_backup.py"
echo "🔄 To restore backup: cd $BACKUP_DIR && ./quick_restore.sh"
echo ""
echo "💾 Backup summary:"
ls -la "$BACKUP_DIR"

echo ""
echo "🎯 Next steps:"
echo "   1. Validate backup: cd $BACKUP_DIR && python validate_backup.py"
echo "   2. Test restoration in safe environment first"
echo "   3. Keep backup in version control or secure storage"