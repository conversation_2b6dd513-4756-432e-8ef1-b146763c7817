#!/bin/bash

# Script to download call recordings from remote FreeSWITCH host

REMOTE_HOST="<EMAIL>"
REMOTE_RECORDINGS_DIR="/opt/freeswitch-recordings"
LOCAL_RECORDINGS_DIR="./recordings"

echo "📥 Downloading call recordings from remote host..."

# Create local recordings directory if it doesn't exist
mkdir -p "$LOCAL_RECORDINGS_DIR"

# Download all recordings from remote host
echo "🔄 Syncing recordings from $REMOTE_HOST:$REMOTE_RECORDINGS_DIR to $LOCAL_RECORDINGS_DIR"
rsync -avz --progress "$REMOTE_HOST:$REMOTE_RECORDINGS_DIR/" "$LOCAL_RECORDINGS_DIR/"

if [ $? -eq 0 ]; then
    echo "✅ Recordings downloaded successfully!"
    echo "📁 Local recordings directory: $LOCAL_RECORDINGS_DIR"
    
    # List downloaded files
    echo ""
    echo "📋 Downloaded recordings:"
    ls -la "$LOCAL_RECORDINGS_DIR"
else
    echo "❌ Failed to download recordings"
    exit 1
fi
