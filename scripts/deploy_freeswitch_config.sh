#!/bin/bash

# FreeSWITCH Configuration Deployment Script
# Deploys configuration to remote Docker host

set -e

REMOTE_HOST="<EMAIL>"
LOCAL_CONFIG_DIR="./serve/freeswitch-minimal"
REMOTE_CONFIG_DIR="/opt/freeswitch-config"
REMOTE_LOGS_DIR="/opt/freeswitch-logs"
CONTAINER_NAME="freeswitch-minimal"

echo "🚀 Deploying FreeSWITCH configuration to remote host..."

# Create remote directories
echo "📁 Creating remote directories..."
ssh $REMOTE_HOST "mkdir -p $REMOTE_CONFIG_DIR $REMOTE_LOGS_DIR /opt/freeswitch-recordings"

# Copy configuration files
echo "📋 Copying configuration files..."
scp -r $LOCAL_CONFIG_DIR/* $REMOTE_HOST:$REMOTE_CONFIG_DIR/

# Set proper permissions
echo "🔒 Setting permissions..."
ssh $REMOTE_HOST "chown -R root:root $REMOTE_CONFIG_DIR $REMOTE_LOGS_DIR /opt/freeswitch-recordings"
ssh $REMOTE_HOST "chmod -R 755 $REMOTE_CONFIG_DIR /opt/freeswitch-recordings"
ssh $REMOTE_HOST "chmod 644 $REMOTE_CONFIG_DIR/freeswitch.xml"

# Alternative: Copy directly to running container (if needed)
echo "🐳 Checking if container is running..."
if ssh $REMOTE_HOST "docker ps --format '{{.Names}}' | grep -q $CONTAINER_NAME"; then
    echo "📦 Container is running, copying config directly..."
    ssh $REMOTE_HOST "docker cp $REMOTE_CONFIG_DIR/. $CONTAINER_NAME:/etc/freeswitch/"
    
    echo "🔄 Restarting FreeSWITCH service..."
    ssh $REMOTE_HOST "docker exec $CONTAINER_NAME fs_cli -x 'reloadxml'"
else
    echo "⚠️  Container not running. Configuration will be used on next start."
fi

echo "✅ FreeSWITCH configuration deployed successfully!"
echo ""
echo "Next steps:"
echo "1. Build/rebuild your freeswitch-custom image if needed"
echo "2. Start/restart the container with: docker-compose up -d freeswitch"
echo "3. Check logs with: docker logs freeswitch-minimal"
echo ""
echo "📁 Call recordings will be stored on remote host at: /opt/freeswitch-recordings"
echo "💡 To download recordings to local machine, run:"
echo "   scp -r $REMOTE_HOST:/opt/freeswitch-recordings ./recordings/"