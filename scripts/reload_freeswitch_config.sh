#!/bin/bash

# FreeSWITCH Configuration Reload and Verification Script
set -e

CONTAINER_NAME="freeswitch-minimal"
ESL_PASSWORD="ClueCon"
ESL_PORT="8021"
FS_HOST="*************"

echo "🔄 FreeSWITCH Configuration Reload Script"
echo "=========================================="

# Check if container is running
echo "📋 Checking FreeSWITCH container status..."
if ! docker ps | grep -q "$CONTAINER_NAME"; then
    echo "❌ FreeSWITCH container '$CONTAINER_NAME' is not running!"
    echo "Starting container..."
    docker start "$CONTAINER_NAME" || {
        echo "❌ Failed to start container. Please check Docker setup."
        exit 1
    }
    sleep 5
fi

echo "✅ FreeSWITCH container is running"

# Function to execute FreeSWITCH CLI commands
fs_cli_exec() {
    local command="$1"
    echo "🔧 Executing: $command"
    docker exec "$CONTAINER_NAME" fs_cli -x "$command" 2>/dev/null || {
        echo "⚠️  Command failed or no output: $command"
        return 1
    }
}

# Function to verify configuration via ESL
verify_via_esl() {
    echo "🔍 Verifying configuration via Event Socket..."
    
    # Test ESL connection and get basic info
    timeout 5 bash -c "echo 'auth $ESL_PASSWORD
status
exit' | nc $FS_HOST $ESL_PORT" 2>/dev/null | grep -E "(FreeSWITCH|UP|sessions)" || {
        echo "⚠️  ESL verification failed - FreeSWITCH may be reloading"
        return 1
    }
}

# Reload XML configuration
echo "📁 Reloading XML configuration..."
fs_cli_exec "reloadxml"
sleep 2

# Reload dialplan
echo "📞 Reloading dialplan..."  
fs_cli_exec "reloadxml"
sleep 1

# Check module status
echo "🔌 Checking critical modules..."
fs_cli_exec "module_exists mod_sofia" && echo "✅ mod_sofia loaded" || echo "❌ mod_sofia not loaded"
fs_cli_exec "module_exists mod_dialplan_xml" && echo "✅ mod_dialplan_xml loaded" || echo "❌ mod_dialplan_xml not loaded"

# Show Sofia profiles status
echo "📡 Checking SIP profiles..."
fs_cli_exec "sofia status" | head -10

# Verify call timeout settings
echo "🕐 Verifying call timeout configuration..."
if docker exec "$CONTAINER_NAME" grep -n "call_timeout=300" /opt/freeswitch/conf/dialplan/default.xml; then
    echo "✅ Call timeout updated to 300 seconds"
else
    echo "❌ Call timeout configuration not found!"
fi

# Test ESL connection
verify_via_esl && echo "✅ ESL connection verified" || echo "⚠️  ESL verification incomplete"

# Show current active calls (if any)
echo "📊 Current call status..."
fs_cli_exec "show calls count" || echo "No active calls"

# Final status
echo ""
echo "🎉 Configuration reload completed!"
echo "🔧 Changes applied:"
echo "   - Call timeout: 30s → 300s (5 minutes)"
echo "   - XML configuration reloaded"
echo "   - Dialplan reloaded"
echo ""
echo "💡 Monitor FreeSWITCH logs for any issues:"
echo "   docker logs -f $CONTAINER_NAME"