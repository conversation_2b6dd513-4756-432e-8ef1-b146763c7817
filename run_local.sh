#!/bin/bash

# Run the application in local development mode (no telemetry)
# This script automatically sets the environment to 'local'

echo "🏠 Running in LOCAL development mode"
echo "   - No telemetry/SigNoz integration"
echo "   - Environment automatically set to 'local'"
echo "   - Logs only to console and file"
echo ""

# Clear any telemetry configuration to ensure local mode
unset OTEL_EXPORTER_OTLP_ENDPOINT
unset OTEL_EXPORTER_OTLP_HEADERS
unset SIGNOZ_ENDPOINT
unset SIGNOZ_INGESTION_KEY
unset OTEL_PYTHON_LOGGING_AUTO_INSTRUMENTATION_ENABLED

# Set basic configuration
export LOG_LEVEL=INFO
export OTEL_SDK_DISABLED=true

# Disable LiveKit's duplicate logging
export LIVEKIT_LOG_LEVEL=info

# Suppress Langfuse warnings when disabled
export LANGFUSE_ENABLED=false

# Set the port for the LiveKit worker
export LIVEKIT_HTTP_PORT=8082

# Default command is "start" unless specified otherwise
COMMAND=${1:-start}

echo "Starting application with command: $COMMAND"
echo "Additional args: ${@:2}"
echo ""

# Run the application with PDM
pdm run python src/main.py "$COMMAND" "${@:2}"
