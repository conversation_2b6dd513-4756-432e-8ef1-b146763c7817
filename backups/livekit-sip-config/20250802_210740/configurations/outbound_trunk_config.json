{"outbound_trunk_id": "ST_4C4GJw7zpNBX", "trunk_name": "FreeSWITCH Dynamic Outbound Trunk", "address": "*************:5080", "number_pattern": "\\+\\d{10,15}", "supports_dynamic_numbers": true, "auth_username": "keyman", "auth_configured": true, "updated_at": "2025-08-02T20:57:00.000Z", "test_calls": [{"number": "+380961717124", "call_id": "SCL_eqXrj3E3ohCP", "participant_id": "PA_SJaD2h98myxg"}, {"number": "+380123456789", "call_id": "SCL_EUtoMLsLcYUG", "participant_id": "PA_yHB74NLaPxJd"}], "freeswitch_dialplan": {"extension_777": "Hardcoded to +380961717124", "dynamic_pattern": "^(\\+\\d{10,15})$ routes to Octella with ${destination_number}"}, "usage_examples": ["lk sip participant create serve/livekit/conf/participant.json", "python make_dynamic_call.py +380961717124", "python make_dynamic_call.py +1234567890 'my-room' 'Agent Name'"]}