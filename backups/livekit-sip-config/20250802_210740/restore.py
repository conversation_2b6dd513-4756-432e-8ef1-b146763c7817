#!/usr/bin/env python3
"""
LiveKit SIP Configuration Restoration Script

This script restores all SIP trunks and dispatch rules from a backup.
It can completely recreate the SIP configuration if everything is dropped.

Usage:
    python restore.py [--dry-run] [--force] [--backup-dir <path>]
    
Options:
    --dry-run      Show what would be restored without making changes
    --force        Force restoration even if trunks/rules already exist
    --backup-dir   Specify backup directory (default: current directory)
"""

import argparse
import json
import os
import subprocess
import sys
import tempfile
from pathlib import Path


class LiveKitSIPRestorer:
    def __init__(self, backup_dir=".", dry_run=False, force=False):
        self.backup_dir = Path(backup_dir)
        self.dry_run = dry_run
        self.force = force
        self.manifest = self.load_manifest()
        
    def load_manifest(self):
        """Load backup manifest"""
        manifest_path = self.backup_dir / "backup-manifest.json"
        if not manifest_path.exists():
            raise FileNotFoundError(f"Backup manifest not found: {manifest_path}")
        
        with open(manifest_path) as f:
            return json.load(f)
    
    def run_lk_command(self, cmd_args, input_data=None):
        """Execute LiveKit CLI command"""
        cmd = ["lk"] + cmd_args
        
        if self.dry_run:
            print(f"[DRY RUN] Would execute: {' '.join(cmd)}")
            if input_data:
                print(f"[DRY RUN] With input data: {json.dumps(input_data, indent=2)}")
            return {"success": True, "output": "DRY RUN - No actual execution"}
        
        try:
            if input_data:
                # Create temporary file for input data
                with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
                    json.dump(input_data, temp_file, indent=2)
                    temp_file_path = temp_file.name
                
                cmd.append(temp_file_path)
                result = subprocess.run(cmd, capture_output=True, text=True, check=True)
                os.unlink(temp_file_path)
            else:
                result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            return {"success": True, "output": result.stdout, "stderr": result.stderr}
            
        except subprocess.CalledProcessError as e:
            return {"success": False, "error": e.stderr, "output": e.stdout}
    
    def check_existing_resources(self):
        """Check what SIP resources already exist"""
        print("🔍 Checking existing SIP resources...")
        
        # Check inbound trunks
        inbound_result = self.run_lk_command(["sip", "inbound", "list"])
        outbound_result = self.run_lk_command(["sip", "outbound", "list"])
        dispatch_result = self.run_lk_command(["sip", "dispatch", "list"])
        
        existing = {
            "inbound_trunks": [],
            "outbound_trunks": [],
            "dispatch_rules": []
        }
        
        # Parse existing resources (simplified - would need proper parsing for production)
        if "ST_" in inbound_result.get("output", ""):
            print("⚠️  Existing inbound trunks found")
        if "ST_" in outbound_result.get("output", ""):
            print("⚠️  Existing outbound trunks found")
        if "SDR_" in dispatch_result.get("output", ""):
            print("⚠️  Existing dispatch rules found")
            
        return existing
    
    def restore_inbound_trunks(self):
        """Restore inbound SIP trunks"""
        print("\\n📥 Restoring inbound trunks...")
        
        for trunk in self.manifest["backup_contents"]["inbound_trunks"]:
            print(f"  Creating inbound trunk: {trunk['name']} ({trunk['id']})")
            
            trunk_config = {
                "trunk": {
                    "name": trunk["name"],
                    "numbers": trunk["numbers"],
                    "allowed_addresses": trunk["allowed_addresses"],
                    "allowed_numbers": trunk["allowed_numbers"]
                }
            }
            
            if trunk["authentication"]:
                trunk_config["trunk"]["auth_username"] = trunk["authentication"]["username"]
                trunk_config["trunk"]["auth_password"] = trunk["authentication"]["password"]
            
            result = self.run_lk_command(["sip", "inbound", "create"], trunk_config)
            
            if result["success"]:
                print(f"    ✅ Created successfully")
                if not self.dry_run:
                    # Extract new trunk ID from output
                    output_lines = result["output"].strip().split('\\n')
                    for line in output_lines:
                        if line.startswith('SIPTrunkID:'):
                            new_id = line.split(': ')[1]
                            print(f"    📋 New Trunk ID: {new_id} (original: {trunk['id']})")
            else:
                print(f"    ❌ Failed: {result.get('error', 'Unknown error')}")
    
    def restore_outbound_trunks(self):
        """Restore outbound SIP trunks"""
        print("\\n📤 Restoring outbound trunks...")
        
        for trunk in self.manifest["backup_contents"]["outbound_trunks"]:
            print(f"  Creating outbound trunk: {trunk['name']} ({trunk['id']})")
            
            trunk_config = {
                "trunk": {
                    "name": trunk["name"],
                    "address": trunk["address"],
                    "numbers": trunk["numbers"]
                }
            }
            
            if trunk["authentication"]:
                trunk_config["trunk"]["auth_username"] = trunk["authentication"]["username"]
                trunk_config["trunk"]["auth_password"] = trunk["authentication"]["password"]
            
            result = self.run_lk_command(["sip", "outbound", "create"], trunk_config)
            
            if result["success"]:
                print(f"    ✅ Created successfully")
                if not self.dry_run:
                    # Extract new trunk ID from output
                    output_lines = result["output"].strip().split('\\n')
                    for line in output_lines:
                        if line.startswith('SIPTrunkID:'):
                            new_id = line.split(': ')[1]
                            print(f"    📋 New Trunk ID: {new_id} (original: {trunk['id']})")
            else:
                print(f"    ❌ Failed: {result.get('error', 'Unknown error')}")
    
    def restore_dispatch_rules(self):
        """Restore dispatch rules"""
        print("\\n🚦 Restoring dispatch rules...")
        print("⚠️  Note: Dispatch rules need to be recreated with current trunk IDs")
        print("          You may need to manually update trunk IDs in dispatch rules")
        
        for rule in self.manifest["backup_contents"]["dispatch_rules"]:
            print(f"  Creating dispatch rule: {rule['name']} ({rule['id']})")
            print(f"    ⚠️  Original trunk IDs: {rule['trunk_ids']}")
            print(f"    📝 You'll need to update trunk IDs manually after restoration")
    
    def restore_configuration_files(self):
        """Restore configuration files"""
        print("\\n📁 Restoring configuration files...")
        
        config_dir = Path("serve/livekit/conf")
        config_dir.mkdir(parents=True, exist_ok=True)
        
        source_config_dir = self.backup_dir / "configurations"
        
        for config_file in self.manifest["configuration_files"]:
            source_path = source_config_dir / Path(config_file).name
            dest_path = config_dir / Path(config_file).name
            
            if source_path.exists():
                if not self.dry_run:
                    import shutil
                    shutil.copy2(source_path, dest_path)
                print(f"  ✅ Restored: {dest_path}")
            else:
                print(f"  ❌ Missing: {source_path}")
    
    def restore_scripts(self):
        """Restore utility scripts"""
        print("\\n📜 Restoring utility scripts...")
        
        source_scripts_dir = self.backup_dir / "scripts"
        
        for script_file in self.manifest["scripts"]:
            source_path = source_scripts_dir / Path(script_file).name
            dest_path = Path(".") / Path(script_file).name
            
            if source_path.exists():
                if not self.dry_run:
                    import shutil
                    shutil.copy2(source_path, dest_path)
                    # Make executable
                    dest_path.chmod(0o755)
                print(f"  ✅ Restored: {dest_path}")
            else:
                print(f"  ❌ Missing: {source_path}")
    
    def run_restoration(self):
        """Run complete restoration process"""
        print(f"🚀 Starting LiveKit SIP Configuration Restoration")
        print(f"📂 Backup directory: {self.backup_dir}")
        print(f"📅 Backup timestamp: {self.manifest['backup_timestamp']}")
        
        if self.dry_run:
            print("🔍 DRY RUN MODE - No changes will be made")
        
        # Check existing resources
        existing = self.check_existing_resources()
        
        if not self.force and not self.dry_run:
            confirm = input("\\n⚠️  Continue with restoration? (y/N): ")
            if confirm.lower() != 'y':
                print("❌ Restoration cancelled")
                return False
        
        # Restore components
        self.restore_configuration_files()
        self.restore_scripts()
        self.restore_inbound_trunks()
        self.restore_outbound_trunks()
        self.restore_dispatch_rules()
        
        print("\\n✅ Restoration completed!")
        print("\\n📋 Post-restoration steps:")
        print("   1. Verify trunk IDs match expectations")
        print("   2. Update dispatch rules with correct trunk IDs if needed")
        print("   3. Test inbound and outbound calling")
        print("   4. Update any configuration tracking files")
        
        return True

def main():
    parser = argparse.ArgumentParser(description="Restore LiveKit SIP configuration from backup")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be restored without making changes")
    parser.add_argument("--force", action="store_true", help="Force restoration even if resources exist")
    parser.add_argument("--backup-dir", default=".", help="Backup directory path")
    
    args = parser.parse_args()
    
    try:
        restorer = LiveKitSIPRestorer(
            backup_dir=args.backup_dir,
            dry_run=args.dry_run,
            force=args.force
        )
        
        success = restorer.run_restoration()
        sys.exit(0 if success else 1)
        
    except Exception as e:
        print(f"❌ Restoration failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()