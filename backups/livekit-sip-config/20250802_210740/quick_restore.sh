#!/bin/bash
# Quick LiveKit SIP Configuration Restoration Script
# This script provides a fast way to restore SIP configuration using the backed up files

set -e

BACKUP_DIR=$(dirname "$0")
PROJECT_ROOT=$(cd "$BACKUP_DIR/../../.." && pwd)

echo "🚀 Quick LiveKit SIP Restoration"
echo "📂 Backup directory: $BACKUP_DIR"
echo "📁 Project root: $PROJECT_ROOT"

# Function to check if LiveKit CLI is available
check_livekit_cli() {
    if ! command -v lk &> /dev/null; then
        echo "❌ LiveKit CLI (lk) not found. Please install it first."
        exit 1
    fi
    echo "✅ LiveKit CLI found"
}

# Function to restore configuration files
restore_config_files() {
    echo "📁 Restoring configuration files..."
    
    # Ensure target directory exists
    mkdir -p "$PROJECT_ROOT/serve/livekit/conf"
    
    # Copy configuration files
    cp "$BACKUP_DIR/configurations/"*.json "$PROJECT_ROOT/serve/livekit/conf/"
    cp "$BACKUP_DIR/configurations/outbound_trunk_config.json" "$PROJECT_ROOT/"
    
    echo "✅ Configuration files restored"
}

# Function to restore scripts
restore_scripts() {
    echo "📜 Restoring utility scripts..."
    
    cp "$BACKUP_DIR/scripts/"*.py "$PROJECT_ROOT/"
    chmod +x "$PROJECT_ROOT/"*.py
    
    echo "✅ Scripts restored"
}

# Function to recreate inbound trunk
recreate_inbound_trunk() {
    echo "📥 Recreating inbound trunk..."
    
    cd "$PROJECT_ROOT"
    
    # Create inbound trunk from backed up configuration
    TRUNK_ID=$(lk sip inbound create serve/livekit/conf/inbound-trunk.json | grep "SIPTrunkID:" | cut -d' ' -f2)
    
    if [ -n "$TRUNK_ID" ]; then
        echo "✅ Inbound trunk created: $TRUNK_ID"
        echo "📋 Original ID was: ST_Spg4uedDwEuf"
    else
        echo "❌ Failed to create inbound trunk"
        return 1
    fi
}

# Function to recreate outbound trunk
recreate_outbound_trunk() {
    echo "📤 Recreating outbound trunk..."
    
    cd "$PROJECT_ROOT"
    
    # Create outbound trunk from backed up configuration
    TRUNK_ID=$(lk sip outbound create serve/livekit/conf/outbound-rule.json | grep "SIPTrunkID:" | cut -d' ' -f2)
    
    if [ -n "$TRUNK_ID" ]; then
        echo "✅ Outbound trunk created: $TRUNK_ID"
        echo "📋 Original ID was: ST_4C4GJw7zpNBX"
        
        # Update participant.json with new trunk ID
        if [ -f "serve/livekit/conf/participant.json" ]; then
            sed -i.bak "s/ST_4C4GJw7zpNBX/$TRUNK_ID/g" serve/livekit/conf/participant.json
            echo "✅ Updated participant.json with new trunk ID"
        fi
        
    else
        echo "❌ Failed to create outbound trunk"
        return 1
    fi
}

# Function to recreate dispatch rule
recreate_dispatch_rule() {
    echo "🚦 Recreating dispatch rule..."
    
    cd "$PROJECT_ROOT"
    
    # Get current inbound trunk ID
    INBOUND_TRUNK_ID=$(lk sip inbound list | grep "ST_" | awk '{print $2}' | head -1)
    
    if [ -n "$INBOUND_TRUNK_ID" ]; then
        # Update dispatch rule with current trunk ID
        TEMP_FILE=$(mktemp)
        sed "s/ST_Spg4uedDwEuf/$INBOUND_TRUNK_ID/g" serve/livekit/conf/dispatch-rule.json > "$TEMP_FILE"
        
        RULE_ID=$(lk sip dispatch create "$TEMP_FILE" | grep "SipDispatchRuleID:" | cut -d' ' -f2)
        rm "$TEMP_FILE"
        
        if [ -n "$RULE_ID" ]; then
            echo "✅ Dispatch rule created: $RULE_ID"
            echo "📋 Original ID was: SDR_H4LurPHZQRUQ"
        else
            echo "❌ Failed to create dispatch rule"
            return 1
        fi
    else
        echo "❌ No inbound trunk found for dispatch rule"
        return 1
    fi
}

# Function to update tracking files
update_tracking_files() {
    echo "📝 Updating tracking configuration..."
    
    cd "$PROJECT_ROOT"
    
    # Get current trunk IDs
    INBOUND_TRUNK_ID=$(lk sip inbound list | grep "ST_" | awk '{print $2}' | head -1)
    OUTBOUND_TRUNK_ID=$(lk sip outbound list | grep "ST_" | awk '{print $2}' | head -1)
    DISPATCH_RULE_ID=$(lk sip dispatch list | grep "SDR_" | awk '{print $2}' | head -1)
    
    # Update outbound_trunk_config.json
    if [ -f "outbound_trunk_config.json" ]; then
        TEMP_FILE=$(mktemp)
        jq --arg new_id "$OUTBOUND_TRUNK_ID" '.outbound_trunk_id = $new_id' outbound_trunk_config.json > "$TEMP_FILE"
        mv "$TEMP_FILE" outbound_trunk_config.json
        echo "✅ Updated outbound trunk configuration tracking"
    fi
}

# Function to run tests
run_tests() {
    echo "🧪 Running basic tests..."
    
    cd "$PROJECT_ROOT"
    
    echo "📋 Current SIP configuration:"
    echo "  Inbound trunks:"
    lk sip inbound list
    echo ""
    echo "  Outbound trunks:"
    lk sip outbound list
    echo ""
    echo "  Dispatch rules:"
    lk sip dispatch list
    echo ""
}

# Main execution
main() {
    echo "Starting restoration process..."
    
    check_livekit_cli
    
    # Ask for confirmation unless --force is provided
    if [ "$1" != "--force" ]; then
        echo ""
        read -p "⚠️  This will recreate all SIP trunks and dispatch rules. Continue? (y/N): " -n 1 -r
        echo ""
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "❌ Restoration cancelled"
            exit 1
        fi
    fi
    
    # Run restoration steps
    restore_config_files
    restore_scripts
    recreate_inbound_trunk
    recreate_outbound_trunk
    recreate_dispatch_rule
    update_tracking_files
    run_tests
    
    echo ""
    echo "✅ Quick restoration completed!"
    echo ""
    echo "📋 Next steps:"
    echo "   1. Test inbound calling (dial extension 888)"
    echo "   2. Test outbound calling: python make_dynamic_call.py +380961717124"
    echo "   3. Verify FreeSWITCH integration is working"
    echo "   4. Update any hardcoded trunk IDs in your application code"
    echo ""
}

# Run main function
main "$@"