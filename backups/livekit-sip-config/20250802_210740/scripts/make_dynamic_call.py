#!/usr/bin/env python3
"""
Script to make dynamic outbound calls via LiveKit to any phone number
"""

import json
import subprocess
import sys
import tempfile
from datetime import datetime

def make_outbound_call(phone_number, room_name=None, participant_name=None):
    """
    Make an outbound call to a specific phone number via LiveKit
    
    Args:
        phone_number (str): Phone number in international format (e.g., +380961717124)
        room_name (str): Optional custom room name
        participant_name (str): Optional custom participant name
    
    Returns:
        dict: Call details including call ID, participant ID, etc.
    """
    
    # Validate phone number format
    if not phone_number.startswith('+') or not phone_number[1:].isdigit():
        raise ValueError("Phone number must be in international format starting with + followed by digits")
    
    if len(phone_number) < 11 or len(phone_number) > 16:
        raise ValueError("Phone number must be between 10-15 digits (excluding +)")
    
    # Default values
    if not room_name:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        room_name = f"call-{phone_number.replace('+', '')}-{timestamp}"
    
    if not participant_name:
        participant_name = f"Caller to {phone_number}"
    
    # Create participant configuration
    participant_config = {
        "sip_trunk_id": "ST_4C4GJw7zpNBX",  # Dynamic outbound trunk
        "sip_call_to": phone_number,
        "room_name": room_name,
        "participant_identity": f"caller-{phone_number.replace('+', '').replace(' ', '')}",
        "participant_name": participant_name
    }
    
    # Write config to temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
        json.dump(participant_config, temp_file, indent=2)
        temp_file_path = temp_file.name
    
    try:
        # Execute LiveKit SIP participant create command
        result = subprocess.run([
            'lk', 'sip', 'participant', 'create', temp_file_path
        ], capture_output=True, text=True, check=True)
        
        # Parse the output to extract call details
        output_lines = result.stdout.strip().split('\n')
        call_details = {}
        
        for line in output_lines:
            if line.startswith('SIPCallID:'):
                call_details['call_id'] = line.split(': ')[1]
            elif line.startswith('ParticipantID:'):
                call_details['participant_id'] = line.split(': ')[1]
            elif line.startswith('ParticipantIdentity:'):
                call_details['participant_identity'] = line.split(': ')[1]
            elif line.startswith('RoomName:'):
                call_details['room_name'] = line.split(': ')[1]
        
        call_details.update({
            'phone_number': phone_number,
            'participant_name': participant_name,
            'timestamp': datetime.now().isoformat(),
            'config_used': participant_config
        })
        
        return call_details
        
    except subprocess.CalledProcessError as e:
        raise RuntimeError(f"Failed to create call: {e.stderr}")
    
    finally:
        # Clean up temporary file
        import os
        try:
            os.unlink(temp_file_path)
        except:
            pass

def main():
    """Main function for command line usage"""
    if len(sys.argv) < 2:
        print("Usage: python make_dynamic_call.py <phone_number> [room_name] [participant_name]")
        print("Example: python make_dynamic_call.py +380961717124")
        print("Example: python make_dynamic_call.py +1234567890 'my-call-room' 'Agent Smith'")
        sys.exit(1)
    
    phone_number = sys.argv[1]
    room_name = sys.argv[2] if len(sys.argv) > 2 else None
    participant_name = sys.argv[3] if len(sys.argv) > 3 else None
    
    try:
        print(f"🚀 Making outbound call to {phone_number}...")
        call_details = make_outbound_call(phone_number, room_name, participant_name)
        
        print("✅ Call initiated successfully!")
        print(f"📞 Phone Number: {call_details['phone_number']}")
        print(f"🏠 Room Name: {call_details['room_name']}")
        print(f"👤 Participant: {call_details['participant_name']}")
        print(f"🆔 Call ID: {call_details['call_id']}")
        print(f"👥 Participant ID: {call_details['participant_id']}")
        
        # Save call details to file
        output_file = f"call_details_{call_details['call_id']}.json"
        with open(output_file, 'w') as f:
            json.dump(call_details, f, indent=2)
        
        print(f"📄 Call details saved to: {output_file}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()