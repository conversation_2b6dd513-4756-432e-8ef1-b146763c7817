#!/usr/bin/env python3
"""
<PERSON>ript to create SIP trunk and dispatch rule using LiveKit API directly
"""

import requests
import json
import base64
import hashlib
import hmac
import time
import os
from datetime import datetime, timedelta

# LiveKit API configuration
LIVEKIT_URL = "http://32016-51127.bacloud.info:7880"
LIVEKIT_API_KEY = "lk_2f7c8d9a1b3e4f6g"
LIVEKIT_API_SECRET = "lk_3h9k1m2n4p6q8r0s5t7v9w0x2z4y6a8b"

def generate_access_token(api_key, api_secret, grant=None):
    """Generate LiveKit access token"""
    import jwt
    
    # Create payload with admin permissions
    now = int(time.time())
    payload = {
        "iss": api_key,
        "sub": api_key,
        "iat": now,
        "exp": now + 3600,  # 1 hour expiry
        "nbf": now - 10,    # Not before (10 seconds ago)
        "video": {
            "room": "*",
            "roomCreate": True,
            "roomList": True,
            "roomRecord": True,
            "roomAdmin": True,
            "roomJoin": True,
            "canPublish": True,
            "canSubscribe": True,
            "canPublishData": True,
            "canUpdateOwnMetadata": True
        },
        "sip": {
            "admin": True,
            "call": True
        }
    }
    
    if grant:
        payload.update(grant)
    
    # Generate token
    token = jwt.encode(payload, api_secret, algorithm="HS256")
    return token

def make_api_request(method, endpoint, data=None):
    """Make authenticated API request to LiveKit"""
    try:
        import jwt
    except ImportError:
        print("PyJWT library required. Install with: pip install PyJWT")
        return None
    
    # Generate access token
    token = generate_access_token(LIVEKIT_API_KEY, LIVEKIT_API_SECRET)
    
    # Prepare headers
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Make request
    url = f"{LIVEKIT_URL.replace('ws://', 'http://').replace('wss://', 'https://')}/twirp/livekit.SIP/{endpoint}"
    
    print(f"Making {method} request to: {url}")
    
    if method.upper() == "POST":
        response = requests.post(url, headers=headers, json=data)
    elif method.upper() == "GET":
        response = requests.get(url, headers=headers)
    else:
        raise ValueError(f"Unsupported method: {method}")
    
    print(f"Response status: {response.status_code}")
    print(f"Response headers: {dict(response.headers)}")
    
    if response.status_code == 200:
        try:
            return response.json()
        except:
            return response.text
    else:
        print(f"Error response: {response.text}")
        return None

def create_sip_trunk():
    """Create SIP inbound trunk"""
    trunk_data = {
        "trunk": {
            "name": "freeswitch-trunk",
            "metadata": "Extension 888 trunk for LiveKit agent",
            "numbers": ["888"],
            "allowed_addresses": ["*************"],
            "allowed_numbers": [],
            "auth_username": "",
            "auth_password": ""
        }
    }
    
    print("Creating SIP inbound trunk...")
    result = make_api_request("POST", "CreateSIPInboundTrunk", trunk_data)
    
    if result:
        print(f"Trunk created successfully: {json.dumps(result, indent=2)}")
        return result.get("sip_trunk_id")
    else:
        print("Failed to create trunk")
        return None

def delete_dispatch_rule(rule_id):
    """Delete SIP dispatch rule"""
    delete_data = {
        "sip_dispatch_rule_id": rule_id
    }
    
    print(f"Deleting dispatch rule {rule_id}...")
    result = make_api_request("POST", "DeleteSIPDispatchRule", delete_data)
    
    if result:
        print(f"Dispatch rule deleted successfully")
        return True
    else:
        print("Failed to delete dispatch rule")
        return False

def create_dispatch_rule(trunk_id):
    """Create SIP dispatch rule"""
    dispatch_data = {
        "trunk_ids": [trunk_id],
        "name": "extension-888-rule",
        "metadata": "Route extension 888 to agent room",
        "rule": {
            "dispatchRuleIndividual": {
                "roomPrefix": "inbound-call-"
            }
        }
    }
    
    print(f"Creating dispatch rule for trunk {trunk_id}...")
    result = make_api_request("POST", "CreateSIPDispatchRule", dispatch_data)
    
    if result:
        print(f"Dispatch rule created successfully: {json.dumps(result, indent=2)}")
        return result.get("sip_dispatch_rule_id")
    else:
        print("Failed to create dispatch rule")
        return None

def list_trunks():
    """List existing SIP trunks"""
    print("Listing existing SIP trunks...")
    result = make_api_request("POST", "ListSIPInboundTrunk", {})
    
    if result:
        print(f"Existing trunks: {json.dumps(result, indent=2)}")
        return result
    else:
        print("Failed to list trunks")
        return None

def list_dispatch_rules():
    """List existing dispatch rules"""
    print("Listing existing dispatch rules...")
    result = make_api_request("POST", "ListSIPDispatchRule", {})
    
    if result:
        print(f"Existing dispatch rules: {json.dumps(result, indent=2)}")
        return result
    else:
        print("Failed to list dispatch rules")
        return None

def main():
    """Main function"""
    print("=== LiveKit SIP Trunk and Dispatch Rule Setup ===\n")
    
    # Check if PyJWT is available
    try:
        import jwt
    except ImportError:
        print("ERROR: PyJWT library is required.")
        print("Install with: pip install PyJWT")
        return
    
    # List existing trunks and rules
    print("1. Checking existing configuration...")
    trunks = list_trunks()
    print()
    dispatch_rules = list_dispatch_rules()
    print()
    
    # Check if trunk already exists
    trunk_id = None
    if trunks and "items" in trunks:
        for trunk in trunks["items"]:
            if "888" in trunk.get("numbers", []):
                trunk_id = trunk["sip_trunk_id"]
                print(f"✅ Found existing trunk for extension 888: {trunk_id}")
                break
    
    # Create trunk if it doesn't exist
    if not trunk_id:
        print("2. Creating SIP trunk...")
        trunk_id = create_sip_trunk()
        
        if not trunk_id:
            print("Failed to create trunk. Exiting.")
            return
        
        print(f"✅ Created trunk with ID: {trunk_id}")
    else:
        print("2. Using existing SIP trunk...")
    
    print()
    
    # Create dispatch rule
    print("3. Creating dispatch rule...")
    rule_id = create_dispatch_rule(trunk_id)
    
    if not rule_id:
        print("Failed to create dispatch rule. Exiting.")
        return
    
    print(f"✅ Created dispatch rule with ID: {rule_id}")
    print()
    
    # Save configuration for persistence
    config = {
        "trunk_id": trunk_id,
        "dispatch_rule_id": rule_id,
        "created_at": datetime.now().isoformat()
    }
    
    with open("sip_trunk_config.json", "w") as f:
        json.dump(config, f, indent=2)
    
    print("✅ Configuration saved to sip_trunk_config.json")
    print("\n=== Setup Complete ===")
    print(f"Trunk ID: {trunk_id}")
    print(f"Dispatch Rule ID: {rule_id}")
    print("Extension 888 is now configured to route to LiveKit agent rooms!")

if __name__ == "__main__":
    main()