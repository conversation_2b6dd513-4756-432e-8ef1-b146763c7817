{"backup_timestamp": "2025-08-02T21:07:40Z", "backup_version": "1.0", "livekit_project": "next", "backup_contents": {"inbound_trunks": [{"id": "ST_Spg4uedDwEuf", "name": "freeswitch-trunk", "numbers": ["888"], "allowed_addresses": [], "allowed_numbers": [], "authentication": null, "encryption": "DISABLE", "headers": {}, "metadata": ""}], "outbound_trunks": [{"id": "ST_4C4GJw7zpNBX", "name": "FreeSWITCH Dynamic Outbound Trunk", "address": "*************:5080", "transport": "AUTO", "numbers": ["\\+\\d{10,15}"], "authentication": {"username": "keyman", "password": "keyman123"}, "encryption": "DISABLE", "headers": {}, "metadata": ""}], "dispatch_rules": [{"id": "SDR_H4LurPHZQRUQ", "name": "extension-888-rule", "trunk_ids": ["ST_Spg4uedDwEuf"], "type": "Individual (Caller)", "room_name_pattern": "inbound-call-_<caller>_<random>", "pin": null, "attributes": {}, "agents": []}]}, "configuration_files": ["configurations/inbound-trunk.json", "configurations/outbound-rule.json", "configurations/participant.json", "configurations/dispatch-rule.json", "configurations/outbound_trunk_config.json"], "scripts": ["scripts/setup_sip_trunk.py", "scripts/make_dynamic_call.py"], "freeswitch_integration": {"auth_user": "keyman", "auth_password": "keyman123", "server_address": "*************:5080", "domain": "*************"}}