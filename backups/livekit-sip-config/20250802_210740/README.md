# LiveKit SIP Configuration Backup

**Backup Created**: 2025-08-02T21:07:40Z  
**Project**: next  
**Version**: 1.0  

## Overview

This backup contains a complete snapshot of your LiveKit SIP configuration, including all trunks, dispatch rules, configuration files, and utility scripts. It can be used to fully restore your SIP setup if everything is dropped or needs to be recreated.

## Backup Contents

### 📞 SIP Trunks
- **1 Inbound Trunk**: `ST_Spg4uedDwEuf` (freeswitch-trunk, extension 888)
- **1 Outbound Trunk**: `ST_4C4GJw7zpNBX` (FreeSWITCH Dynamic Outbound Trunk, pattern: `\+\d{10,15}`)

### 🚦 Dispatch Rules
- **1 Dispatch Rule**: `SDR_H4LurPHZQRUQ` (extension-888-rule, routes to inbound-call rooms)

### 📁 Configuration Files
- `inbound-trunk.json` - Inbound trunk configuration
- `outbound-rule.json` - Dynamic outbound trunk configuration  
- `participant.json` - Participant template for outbound calls
- `dispatch-rule.json` - Dispatch rule configuration
- `outbound_trunk_config.json` - Configuration tracking and metadata

### 📜 Utility Scripts
- `make_dynamic_call.py` - Python script for making dynamic outbound calls
- `setup_sip_trunk.py` - Script for creating/managing SIP trunks via API

## Directory Structure

```
backups/livekit-sip-config/20250802_210740/
├── backup-manifest.json          # Complete backup metadata
├── configurations/               # Configuration files
│   ├── inbound-trunk.json
│   ├── outbound-rule.json
│   ├── participant.json
│   ├── dispatch-rule.json
│   └── outbound_trunk_config.json
├── scripts/                      # Utility scripts
│   ├── make_dynamic_call.py
│   └── setup_sip_trunk.py
├── trunks/                      # Trunk exports
│   ├── inbound-trunks-list.txt
│   └── outbound-trunks-list.txt
├── dispatch-rules/              # Dispatch rule exports
│   └── dispatch-rules-list.txt
├── restore.py                   # Full restoration script
├── quick_restore.sh            # Quick restoration script
├── validate_backup.py          # Backup validation script
└── README.md                   # This file
```

## Restoration Options

### Option 1: Quick Restoration (Recommended)
```bash
cd backups/livekit-sip-config/20250802_210740
./quick_restore.sh
```

The quick restore script will:
- ✅ Restore all configuration files
- ✅ Restore utility scripts
- ✅ Recreate inbound trunk
- ✅ Recreate outbound trunk  
- ✅ Recreate dispatch rule with updated trunk IDs
- ✅ Update tracking files with new IDs
- ✅ Run basic validation tests

### Option 2: Full Python Restoration
```bash
cd backups/livekit-sip-config/20250802_210740

# Dry run (see what would be restored)
python restore.py --dry-run

# Full restoration
python restore.py

# Force restoration (ignore existing resources)
python restore.py --force
```

### Option 3: Manual Restoration
1. Copy configuration files to target directories
2. Use LiveKit CLI to recreate trunks:
   ```bash
   lk sip inbound create configurations/inbound-trunk.json
   lk sip outbound create configurations/outbound-rule.json
   lk sip dispatch create configurations/dispatch-rule.json
   ```
3. Update any configuration files with new trunk IDs

## Validation

Before and after restoration, you can validate the backup:

```bash
cd backups/livekit-sip-config/20250802_210740
python validate_backup.py
```

## Post-Restoration Steps

After restoration, you should:

1. **Verify Trunk IDs**: Check that new trunk IDs are properly updated in all configuration files
2. **Test Inbound Calling**: Call extension 888 to test inbound routing
3. **Test Outbound Calling**: 
   ```bash
   python make_dynamic_call.py +************
   ```
4. **Update Application Code**: If your application has hardcoded trunk IDs, update them
5. **Verify FreeSWITCH Integration**: Ensure FreeSWITCH can authenticate with the new trunks

## Important Notes

### Trunk ID Changes
When trunks are recreated, they get new IDs. The restoration scripts automatically update:
- `participant.json` with new outbound trunk ID
- `dispatch-rule.json` with new inbound trunk ID  
- `outbound_trunk_config.json` with new trunk ID

### Authentication
The backup includes authentication credentials:
- **FreeSWITCH User**: `keyman`
- **FreeSWITCH Password**: `keyman123`
- **FreeSWITCH Domain**: `*************`

### Dependencies
- LiveKit CLI (`lk`) must be installed and configured
- FreeSWITCH must be running and accessible at `*************:5080`
- Octella trunk must be configured in FreeSWITCH

## Original Configuration Summary

### Inbound Trunk (`ST_Spg4uedDwEuf`)
- **Name**: freeswitch-trunk
- **Numbers**: 888
- **Purpose**: Receives calls from FreeSWITCH extension 888
- **Authentication**: None required

### Outbound Trunk (`ST_4C4GJw7zpNBX`)  
- **Name**: FreeSWITCH Dynamic Outbound Trunk
- **Address**: `*************:5080`
- **Pattern**: `\+\d{10,15}` (accepts international phone numbers)
- **Authentication**: `keyman/keyman123`
- **Purpose**: Routes outbound calls to any international number via FreeSWITCH

### Dispatch Rule (`SDR_H4LurPHZQRUQ`)
- **Name**: extension-888-rule  
- **Trunk**: Links to inbound trunk
- **Type**: Individual (Caller)
- **Room Pattern**: `inbound-call-_<caller>_<random>`
- **Purpose**: Routes inbound calls to individual agent rooms

## Support

If restoration fails:
1. Check LiveKit CLI connectivity: `lk --help`
2. Verify project access: `lk room list`
3. Check FreeSWITCH connectivity: Test extension 777 or 888
4. Review backup validation: `python validate_backup.py`
5. Check logs and error messages from restoration scripts

For additional help, refer to the main project documentation at `docs/OUTBOUND_CALLING_SETUP.md`.