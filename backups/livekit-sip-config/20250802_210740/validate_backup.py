#!/usr/bin/env python3
"""
LiveKit SIP Backup Validation Script

This script validates that a backup contains all necessary components
for a complete restoration of LiveKit SIP configuration.
"""

import json
from pathlib import Path

def validate_backup(backup_dir="."):
    """Validate backup completeness"""
    backup_path = Path(backup_dir)
    
    print(f"🔍 Validating backup in: {backup_path}")
    
    # Check for manifest
    manifest_path = backup_path / "backup-manifest.json"
    if not manifest_path.exists():
        print("❌ Missing backup-manifest.json")
        return False
    
    with open(manifest_path) as f:
        manifest = json.load(f)
    
    print(f"✅ Manifest found - Backup from {manifest['backup_timestamp']}")
    
    # Validate directory structure
    required_dirs = ["trunks", "dispatch-rules", "configurations", "scripts"]
    for dir_name in required_dirs:
        if not (backup_path / dir_name).exists():
            print(f"❌ Missing directory: {dir_name}")
            return False
        else:
            print(f"✅ Directory exists: {dir_name}")
    
    # Validate configuration files
    config_dir = backup_path / "configurations"
    required_configs = [
        "inbound-trunk.json",
        "outbound-rule.json", 
        "participant.json",
        "dispatch-rule.json",
        "outbound_trunk_config.json"
    ]
    
    for config_file in required_configs:
        if not (config_dir / config_file).exists():
            print(f"❌ Missing configuration: {config_file}")
            return False
        else:
            print(f"✅ Configuration exists: {config_file}")
    
    # Validate scripts
    scripts_dir = backup_path / "scripts"
    required_scripts = ["make_dynamic_call.py", "setup_sip_trunk.py"]
    
    for script_file in required_scripts:
        if not (scripts_dir / script_file).exists():
            print(f"❌ Missing script: {script_file}")
            return False
        else:
            print(f"✅ Script exists: {script_file}")
    
    # Validate restoration scripts
    if not (backup_path / "restore.py").exists():
        print("❌ Missing restore.py")
        return False
    
    if not (backup_path / "quick_restore.sh").exists():
        print("❌ Missing quick_restore.sh")
        return False
    
    print("✅ Restoration scripts exist")
    
    # Validate manifest content
    backup_contents = manifest.get("backup_contents", {})
    
    if not backup_contents.get("inbound_trunks"):
        print("⚠️  No inbound trunks in manifest")
    else:
        print(f"✅ {len(backup_contents['inbound_trunks'])} inbound trunk(s) in manifest")
    
    if not backup_contents.get("outbound_trunks"):
        print("⚠️  No outbound trunks in manifest")
    else:
        print(f"✅ {len(backup_contents['outbound_trunks'])} outbound trunk(s) in manifest")
    
    if not backup_contents.get("dispatch_rules"):
        print("⚠️  No dispatch rules in manifest")
    else:
        print(f"✅ {len(backup_contents['dispatch_rules'])} dispatch rule(s) in manifest")
    
    # Validate trunk list exports
    trunk_exports = [
        "trunks/inbound-trunks-list.txt",
        "trunks/outbound-trunks-list.txt",
        "dispatch-rules/dispatch-rules-list.txt"
    ]
    
    for export_file in trunk_exports:
        if not (backup_path / export_file).exists():
            print(f"❌ Missing export: {export_file}")
            return False
        else:
            # Check if file has content
            with open(backup_path / export_file) as f:
                content = f.read().strip()
                if "ST_" in content or "SDR_" in content or "Using default project" in content:
                    print(f"✅ Export has content: {export_file}")
                else:
                    print(f"⚠️  Export appears empty: {export_file}")
    
    print("\\n🎉 Backup validation completed successfully!")
    print("\\n📋 Backup summary:")
    print(f"   📅 Timestamp: {manifest['backup_timestamp']}")
    print(f"   🏗️  Project: {manifest.get('livekit_project', 'Unknown')}")
    print(f"   📥 Inbound trunks: {len(backup_contents.get('inbound_trunks', []))}")
    print(f"   📤 Outbound trunks: {len(backup_contents.get('outbound_trunks', []))}")
    print(f"   🚦 Dispatch rules: {len(backup_contents.get('dispatch_rules', []))}")
    print(f"   📁 Configuration files: {len(manifest.get('configuration_files', []))}")
    print(f"   📜 Scripts: {len(manifest.get('scripts', []))}")
    
    return True

if __name__ == "__main__":
    import sys
    backup_dir = sys.argv[1] if len(sys.argv) > 1 else "."
    
    if validate_backup(backup_dir):
        print("\\n✅ Backup is valid and ready for restoration")
        sys.exit(0)
    else:
        print("\\n❌ Backup validation failed")
        sys.exit(1)