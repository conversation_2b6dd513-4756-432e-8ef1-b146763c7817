"""
LiveKit Agents Metrics Collector for SigNoz

This module provides comprehensive metrics collection for LiveKit Agents including:
- Agent performance metrics (response time, errors, etc.)
- Conversation metrics (duration, messages, handoffs)
- Resource metrics (CPU, memory, API usage)
- LiveKit specific metrics (room events, participant events)
"""

import asyncio
import logging
import os
import psutil
import time
from contextlib import asynccontextmanager
from typing import Dict, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime

from opentelemetry.metrics import Counter, Histogram, UpDownCounter
# Gauge is not directly importable, it's created via Meter.create_gauge()
# We'll use a generic type for Gauge metrics
from typing import Any
from telemetry_config import get_meter, is_telemetry_enabled

_logger = logging.getLogger(__name__)

@dataclass
class ConversationMetrics:
    """Track metrics for a conversation session"""
    start_time: float = field(default_factory=time.time)
    message_count: int = 0
    error_count: int = 0
    handoff_count: int = 0
    participant_count: int = 0
    last_activity: float = field(default_factory=time.time)


class AgentMetricsCollector:
    """Collects and reports metrics for LiveKit Agents"""
    
    def __init__(self, service_name: str = "advagency-conversation-manager"):
        self.service_name = service_name
        self._conversations: Dict[str, ConversationMetrics] = {}
        self._meter = None
        self._metrics_initialized = False
        
        # Performance metrics
        self.agent_requests_total: Optional[Counter] = None
        self.agent_request_duration: Optional[Histogram] = None
        self.agent_errors_total: Optional[Counter] = None
        self.agent_active_conversations: Optional[UpDownCounter] = None
        
        # Conversation metrics
        self.conversation_duration: Optional[Histogram] = None
        self.conversation_messages_total: Optional[Counter] = None
        self.conversation_handoffs_total: Optional[Counter] = None
        self.conversation_participants: Optional[Histogram] = None
        
        # Resource metrics
        self.process_cpu_usage: Optional[Any] = None
        self.process_memory_usage: Optional[Any] = None
        self.process_memory_rss: Optional[Any] = None
        self.process_memory_vms: Optional[Any] = None
        
        # LiveKit specific metrics
        self.room_events_total: Optional[Counter] = None
        self.participant_events_total: Optional[Counter] = None
        self.audio_tracks_total: Optional[Counter] = None
        self.video_tracks_total: Optional[Counter] = None
        
        # API usage metrics
        self.api_calls_total: Optional[Counter] = None
        self.api_call_duration: Optional[Histogram] = None
        self.api_errors_total: Optional[Counter] = None
        
        # Agent state metrics
        self.agent_state_changes: Optional[Counter] = None
        self.agent_active_sessions: Optional[UpDownCounter] = None
        
        # Initialize metrics if telemetry is enabled
        if is_telemetry_enabled():
            self._initialize_metrics()
        
        # Start background resource monitoring
        self._monitoring_task = None
        # Only start monitoring if we have an event loop
        if self._metrics_initialized:
            try:
                # Check if there's a running event loop
                asyncio.get_running_loop()
                self._monitoring_task = asyncio.create_task(self._monitor_resources())
            except RuntimeError:
                # No event loop running, we'll start monitoring later when needed
                pass
    
    def _initialize_metrics(self):
        """Initialize all OpenTelemetry metrics"""
        try:
            self._meter = get_meter(self.service_name)
            
            # Performance metrics
            self.agent_requests_total = self._meter.create_counter(
                name="agent_requests_total",
                description="Total number of agent requests",
                unit="1"
            )
            
            self.agent_request_duration = self._meter.create_histogram(
                name="agent_request_duration_seconds",
                description="Agent request duration in seconds",
                unit="s"
            )
            
            self.agent_errors_total = self._meter.create_counter(
                name="agent_errors_total",
                description="Total number of agent errors",
                unit="1"
            )
            
            self.agent_active_conversations = self._meter.create_up_down_counter(
                name="agent_active_conversations",
                description="Number of active conversations",
                unit="1"
            )
            
            # Conversation metrics
            self.conversation_duration = self._meter.create_histogram(
                name="conversation_duration_seconds",
                description="Conversation duration in seconds",
                unit="s"
            )
            
            self.conversation_messages_total = self._meter.create_counter(
                name="conversation_messages_total",
                description="Total number of messages in conversations",
                unit="1"
            )
            
            self.conversation_handoffs_total = self._meter.create_counter(
                name="conversation_handoffs_total",
                description="Total number of conversation handoffs",
                unit="1"
            )
            
            self.conversation_participants = self._meter.create_histogram(
                name="conversation_participants",
                description="Number of participants in conversations",
                unit="1"
            )
            
            # Resource metrics
            self.process_cpu_usage = self._meter.create_gauge(
                name="process_cpu_usage_percent",
                description="Process CPU usage percentage",
                unit="%"
            )
            
            self.process_memory_usage = self._meter.create_gauge(
                name="process_memory_usage_percent",
                description="Process memory usage percentage",
                unit="%"
            )
            
            self.process_memory_rss = self._meter.create_gauge(
                name="process_memory_rss_bytes",
                description="Process resident set size memory in bytes",
                unit="By"
            )
            
            self.process_memory_vms = self._meter.create_gauge(
                name="process_memory_vms_bytes",
                description="Process virtual memory size in bytes",
                unit="By"
            )
            
            # LiveKit specific metrics
            self.room_events_total = self._meter.create_counter(
                name="livekit_room_events_total",
                description="Total number of LiveKit room events",
                unit="1"
            )
            
            self.participant_events_total = self._meter.create_counter(
                name="livekit_participant_events_total",
                description="Total number of LiveKit participant events",
                unit="1"
            )
            
            self.audio_tracks_total = self._meter.create_counter(
                name="livekit_audio_tracks_total",
                description="Total number of audio tracks",
                unit="1"
            )
            
            self.video_tracks_total = self._meter.create_counter(
                name="livekit_video_tracks_total",
                description="Total number of video tracks",
                unit="1"
            )
            
            # API usage metrics
            self.api_calls_total = self._meter.create_counter(
                name="api_calls_total",
                description="Total number of API calls",
                unit="1"
            )
            
            self.api_call_duration = self._meter.create_histogram(
                name="api_call_duration_seconds",
                description="API call duration in seconds",
                unit="s"
            )
            
            self.api_errors_total = self._meter.create_counter(
                name="api_errors_total",
                description="Total number of API errors",
                unit="1"
            )
            
            # Agent state metrics
            self.agent_state_changes = self._meter.create_counter(
                name="agent_state_changes_total",
                description="Total number of agent state changes",
                unit="1"
            )
            
            self.agent_active_sessions = self._meter.create_up_down_counter(
                name="agent_active_sessions",
                description="Number of active agent sessions",
                unit="1"
            )
            
            self._metrics_initialized = True
            _logger.info("Metrics collector initialized successfully")
            
        except Exception as e:
            _logger.error(f"Failed to initialize metrics: {e}", exc_info=True)
    
    async def _monitor_resources(self):
        """Background task to monitor system resources"""
        process = psutil.Process()
        
        while True:
            try:
                if self._metrics_initialized:
                    # CPU usage
                    cpu_percent = process.cpu_percent()
                    if self.process_cpu_usage:
                        self.process_cpu_usage.set(cpu_percent)
                    
                    # Memory usage
                    memory_info = process.memory_info()
                    memory_percent = process.memory_percent()
                    
                    if self.process_memory_usage:
                        self.process_memory_usage.set(memory_percent)
                    if self.process_memory_rss:
                        self.process_memory_rss.set(memory_info.rss)
                    if self.process_memory_vms:
                        self.process_memory_vms.set(memory_info.vms)
                
                await asyncio.sleep(30)  # Update every 30 seconds
                
            except Exception as e:
                _logger.error(f"Error monitoring resources: {e}")
                await asyncio.sleep(60)  # Wait longer on error
    
    def start_conversation(self, conversation_id: str, participant_count: int = 1) -> None:
        """Start tracking a new conversation"""
        if not self._metrics_initialized:
            return
            
        # Start monitoring if not already started
        self.start_monitoring()
        
        self._conversations[conversation_id] = ConversationMetrics(
            participant_count=participant_count
        )
        
        if self.agent_active_conversations:
            self.agent_active_conversations.add(1)
        
        if self.conversation_participants:
            self.conversation_participants.record(participant_count)
        
        _logger.debug(f"Started tracking conversation: {conversation_id}")
    
    def end_conversation(self, conversation_id: str) -> None:
        """End tracking a conversation and record final metrics"""
        if not self._metrics_initialized or conversation_id not in self._conversations:
            return
        
        conv_metrics = self._conversations.pop(conversation_id)
        duration = time.time() - conv_metrics.start_time
        
        if self.agent_active_conversations:
            self.agent_active_conversations.add(-1)
        
        if self.conversation_duration:
            self.conversation_duration.record(duration)
        
        _logger.debug(f"Ended tracking conversation: {conversation_id}, duration: {duration:.2f}s")
    
    def record_message(self, conversation_id: str, message_type: str = "user") -> None:
        """Record a message in a conversation"""
        if not self._metrics_initialized:
            return
        
        if conversation_id in self._conversations:
            self._conversations[conversation_id].message_count += 1
            self._conversations[conversation_id].last_activity = time.time()
        
        if self.conversation_messages_total:
            self.conversation_messages_total.add(1, {"message_type": message_type})
    
    def record_handoff(self, conversation_id: str, from_agent: str, to_agent: str) -> None:
        """Record an agent handoff"""
        if not self._metrics_initialized:
            return
        
        if conversation_id in self._conversations:
            self._conversations[conversation_id].handoff_count += 1
        
        if self.conversation_handoffs_total:
            self.conversation_handoffs_total.add(1, {
                "from_agent": from_agent,
                "to_agent": to_agent
            })
        
        if self.agent_state_changes:
            self.agent_state_changes.add(1, {"event": "handoff"})
    
    def record_error(self, conversation_id: str, error_type: str, error_message: str = "") -> None:
        """Record an error"""
        if not self._metrics_initialized:
            return
        
        if conversation_id in self._conversations:
            self._conversations[conversation_id].error_count += 1
        
        if self.agent_errors_total:
            self.agent_errors_total.add(1, {"error_type": error_type})
    
    @asynccontextmanager
    async def track_request(self, request_type: str = "general"):
        """Context manager to track request duration"""
        start_time = time.time()
        
        if self._metrics_initialized and self.agent_requests_total:
            self.agent_requests_total.add(1, {"request_type": request_type})
        
        try:
            yield
        except Exception as e:
            if self._metrics_initialized and self.agent_errors_total:
                self.agent_errors_total.add(1, {"request_type": request_type})
            raise
        finally:
            duration = time.time() - start_time
            if self._metrics_initialized and self.agent_request_duration:
                self.agent_request_duration.record(duration, {"request_type": request_type})
    
    @asynccontextmanager
    async def track_api_call(self, api_provider: str, api_method: str):
        """Context manager to track API call duration"""
        start_time = time.time()
        
        if self._metrics_initialized and self.api_calls_total:
            self.api_calls_total.add(1, {
                "provider": api_provider,
                "method": api_method
            })
        
        try:
            yield
        except Exception as e:
            if self._metrics_initialized and self.api_errors_total:
                self.api_errors_total.add(1, {
                    "provider": api_provider,
                    "method": api_method,
                    "error": str(type(e).__name__)
                })
            raise
        finally:
            duration = time.time() - start_time
            if self._metrics_initialized and self.api_call_duration:
                self.api_call_duration.record(duration, {
                    "provider": api_provider,
                    "method": api_method
                })
    
    def record_room_event(self, event_type: str, room_id: str) -> None:
        """Record a LiveKit room event"""
        if not self._metrics_initialized or not self.room_events_total:
            return
        
        self.room_events_total.add(1, {
            "event_type": event_type,
            "room_id": room_id
        })
    
    def record_participant_event(self, event_type: str, participant_id: str, room_id: str) -> None:
        """Record a LiveKit participant event"""
        if not self._metrics_initialized or not self.participant_events_total:
            return
        
        self.participant_events_total.add(1, {
            "event_type": event_type,
            "participant_id": participant_id,
            "room_id": room_id
        })
    
    def record_track_event(self, track_type: str, event_type: str, participant_id: str) -> None:
        """Record a track event (audio/video)"""
        if not self._metrics_initialized:
            return
        
        if track_type == "audio" and self.audio_tracks_total:
            self.audio_tracks_total.add(1, {
                "event_type": event_type,
                "participant_id": participant_id
            })
        elif track_type == "video" and self.video_tracks_total:
            self.video_tracks_total.add(1, {
                "event_type": event_type,
                "participant_id": participant_id
            })
    
    def record_session_start(self) -> None:
        """Record a new agent session start"""
        if self._metrics_initialized and self.agent_active_sessions:
            self.agent_active_sessions.add(1)
        
        if self._metrics_initialized and self.agent_state_changes:
            self.agent_state_changes.add(1, {"event": "session_start"})
    
    def record_session_end(self) -> None:
        """Record an agent session end"""
        if self._metrics_initialized and self.agent_active_sessions:
            self.agent_active_sessions.add(-1)
        
        if self._metrics_initialized and self.agent_state_changes:
            self.agent_state_changes.add(1, {"event": "session_end"})
    
    async def shutdown(self):
        """Shutdown the metrics collector"""
        if self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
        
        _logger.info("Metrics collector shutdown complete")

    def start_monitoring(self):
        """Start the background monitoring task if not already started"""
        if self._metrics_initialized and self._monitoring_task is None:
            try:
                # Check if there's a running event loop
                loop = asyncio.get_running_loop()
                self._monitoring_task = loop.create_task(self._monitor_resources())
                _logger.info("Started background resource monitoring")
            except RuntimeError:
                # No event loop running
                _logger.warning("Cannot start monitoring: no event loop running")


# Global metrics collector instance
_metrics_collector: Optional[AgentMetricsCollector] = None

def get_metrics_collector() -> AgentMetricsCollector:
    """Get the global metrics collector instance"""
    global _metrics_collector
    if _metrics_collector is None:
        _metrics_collector = AgentMetricsCollector()
    return _metrics_collector

def init_metrics_collector(service_name: str = "advagency-conversation-manager") -> AgentMetricsCollector:
    """Initialize the global metrics collector"""
    global _metrics_collector
    _metrics_collector = AgentMetricsCollector(service_name)
    return _metrics_collector