import argparse
import async<PERSON>
import json
import logging
import os
import sys
from contextvars import ContextVar

from livekit.agents import AutoSubscribe, JobContext, WorkerOptions, cli, JobProcess, RoomInputOptions, metrics, \
    MetricsCollectedEvent, JobExecutorType
from livekit.plugins import groq

from app.config import get_config
from app.sentry_config import initialize_sentry, capture_errors
from call.feature.participant_awaiter import ParticipantAwaiter
from conv.conv_mgr import ConvManager
from log.sentry_decorators import sentry_transaction, sentry_span
from log.advagency_logger import AdvAgencyLogger, get_main_logger
from telemetry_config import setup_telemetry, get_tracer

# Suppress numba debug logs
logging.getLogger('numba').setLevel(logging.INFO)
logging.getLogger('numba.core.byteflow').setLevel(logging.INFO)



conversation_id_var = ContextVar('conversation_id', default="")
usage_collector = metrics.UsageCollector()

# Setup telemetry FIRST before any logging configuration
setup_telemetry()

# Initialize configuration and log
config = get_config()

# Configure AdvAgency logging system
# Let LiveKit handle console output, we only add file logging
AdvAgencyLogger.configure(
    level=config.app.log_level,
    enable_console=False,  # LiveKit handles console with colored output
    enable_file=True       # We handle file logging
)

# Get our application logger
logger = get_main_logger()

initialize_sentry(config)

# Global variable to store agent_file
BASE_DIR = os.path.dirname(__file__)
_AGENT_FILE = "amber.json"

async def entrypoint_with_agent(ctx: JobContext) -> None:
    global _AGENT_FILE
    await entrypoint(ctx, _AGENT_FILE)

def prewarm(job_process: JobProcess) -> None:
    """Prewarm function called after LiveKit initialization"""
    pass


async def get_room_metadata(ctx: JobContext, timeout: float = 10.0) -> dict:
    """Wait briefly for room metadata and return it as a dict."""
    future = asyncio.get_event_loop().create_future()

    if ctx.room.metadata:
        logger.info("Room metadata already available, using it.")
        try:
            return json.loads(ctx.room.metadata)
        except Exception:
            logger.warning("Room metadata is not valid JSON; using raw string under 'room_metadata'.")
            return {"room_metadata": ctx.room.metadata}

    @ctx.room.on("room_metadata_changed")
    def room_metadata_changed(old_metadata, metadata):
        logger.debug(f"Room metadata changed: {metadata}")
        if metadata and not future.done():
            future.set_result(metadata)

    try:
        await asyncio.wait_for(future, timeout=timeout)
        logger.info("Received room metadata within timeout.")
        try:
            return json.loads(future.result())
        except Exception:
            logger.warning("Room metadata is not valid JSON; using raw string under 'room_metadata'.")
            return {"room_metadata": future.result()}
    except asyncio.TimeoutError:
        logger.warning("Timed out waiting for metadata to be updated; proceeding with empty metadata.")
        return {}

def load_agent_settings(agent_file: str) -> dict:
    """Load agent settings JSON from disk."""
    default_settings_path = os.path.join(BASE_DIR, "meta_test", agent_file)
    logger.info(f"Loading agent settings from {default_settings_path}")
    if not os.path.exists(default_settings_path):
        err = FileNotFoundError(f"Agent settings file not found: {default_settings_path}")
        logger.error(str(err))
        raise err
    try:
        with open(default_settings_path, "r") as f:
            settings = json.load(f)
            logger.info("Agent settings loaded successfully.")
            return settings
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON in agent settings file {default_settings_path}: {str(e)}")
        raise

def _deep_merge(base: dict, override: dict) -> dict:
    """Deep-merge two dicts (override wins)."""
    out = dict(base)
    for k, v in override.items():
        if k in out and isinstance(out[k], dict) and isinstance(v, dict):
            out[k] = _deep_merge(out[k], v)
        else:
            out[k] = v
    return out

def build_effective_settings(agent_settings: dict, room_metadata: dict) -> dict:
    """Combine agent settings with room metadata."""
    return _deep_merge(agent_settings, room_metadata)

@capture_errors
@sentry_transaction("conversation", "entry point")
async def entrypoint(ctx: JobContext, agent_file: str = "amber.json") -> None:
    tracer = get_tracer(__name__)
    
    with tracer.start_as_current_span("main.entrypoint") as span:
        span.set_attribute("room.name", ctx.room.name)
        span.set_attribute("agent.file", agent_file)
        logger.info(f"Room Name: {ctx.room.name}")

        async def shutdown_callback(obj=None):
            try:
                if conversation_manager is not None:
                    if conversation_manager.conversation_ended.is_set():
                        logger.info("Conversation already ended. Skipping shutdown.")
                        return
                    conversation_manager.set_end_conversation()
                    # Wait a moment for cleanup tasks to complete
                    await asyncio.sleep(0.5)
                    await ctx.room.disconnect()
            except Exception as e:
                logger.error(f"Error shutting down conversation: {str(e)}")

            logger.info(f"Shutdown callback execution: {obj}")

        ctx.add_shutdown_callback(shutdown_callback)

        with tracer.start_as_current_span("main.connect_to_room"):
            await ctx.connect(auto_subscribe=AutoSubscribe.AUDIO_ONLY)

        try:
            with tracer.start_as_current_span("main.initialize_conversation_manager"):
                conversation_manager = ConvManager(ctx)
                agent_settings = load_agent_settings(agent_file)
                room_metadata = await get_room_metadata(ctx, timeout=10.0)
                effective_settings = build_effective_settings(agent_settings, room_metadata)
                conversation_manager.update_metadata(json.dumps(effective_settings))
                span.set_attribute("conversation.metadata_loaded", True)
        except Exception as e:
            logger.error(f"Failed to initialize ConvManager: {str(e)}")
            logger.error(f"Current room metadata: {ctx.room.metadata!r}")
            span.record_exception(e)
            span.set_attribute("conversation.initialization_failed", True)
            raise

        participant_awaiter = ParticipantAwaiter(ctx, shutdown_callback)
        await run_conversation(ctx, conversation_manager, participant_awaiter.wait_for_participant)

@sentry_span(op="conversation", description="run conversation")
async def run_conversation(ctx: JobContext, conversation_manager: ConvManager, wait_for_participant_fn):
    jana = await conversation_manager.start_conversation(usage_collector=usage_collector)
    participant = await wait_for_participant_fn()
    await conversation_manager.session.start(room=ctx.room, agent=jana, room_input_options=RoomInputOptions())

    await conversation_manager.say_welcome()  # Moved here to ensure welcome after speech detection
    # Start collecting 3 digits at the beginning


    @conversation_manager.session.on("metrics_collected")
    def _on_metrics_collected(ev: MetricsCollectedEvent):
        usage_collector.collect(ev.metrics)

    async def log_usage():
        summary = usage_collector.get_summary()
        logger.warning(f"Usage: {summary}")

    ctx.add_shutdown_callback(log_usage)

def main() -> None:
    logger.info(f"Raw sys.argv: {sys.argv}")
    parser = argparse.ArgumentParser(
        description="Run the application with optional agent configuration",
        add_help=False
    )
    subparsers = parser.add_subparsers(dest="command", required=True, help="Command to run (dev or start)")
    dev_parser = subparsers.add_parser("dev", help="Run in development mode", add_help=False)
    dev_parser.add_argument(
        "--agent",
        default="amber.json",
        choices=["emma.json", "noah.json", "richard.json", "amber.json"],
        help="Agent configuration JSON file (default: amber.json)"
    )
    start_parser = subparsers.add_parser("start", help="Run in start mode", add_help=False)
    start_parser.add_argument(
        "--agent",
        default="amber.json",
        choices=["emma.json", "noah.json", "richard.json", "amber.json"],
        help="Agent configuration JSON file (default: amber.json)"
    )
    args, unknown_args = parser.parse_known_args()
    logger.info(f"Parsed args: command={args.command}, agent={args.agent}")
    logger.info(f"Unknown args: {unknown_args}")
    global _AGENT_FILE
    _AGENT_FILE = args.agent
    sys.argv = [sys.argv[0], args.command] + unknown_args
    logger.info(f"Modified sys.argv for cli.run_app: {sys.argv}")
    worker_options = WorkerOptions(
        entrypoint_fnc=entrypoint_with_agent,
        job_executor_type=JobExecutorType.THREAD,
        prewarm_fnc=prewarm,
        initialize_process_timeout=360,
    )
    try:
        logger.info(f"Starting application with command: {args.command}, agent: {args.agent}")
        cli.run_app(worker_options)
    except asyncio.CancelledError:
        logger.info("Long-running task was cancelled")
    except Exception as e:
        logger.error(f"Error in main application: {str(e)}", exc_info=True)
    finally:
        logger.info("Shutting down the application.")

if __name__ == "__main__":
    main()
