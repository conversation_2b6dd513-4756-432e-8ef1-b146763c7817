from typing import Optional

from conv.analysers.analyser import CallAnalys<PERSON>, AnalyserConfig
from conv.conv_svc import ConvSvc


def create_analyser(core_api_client: ConvSvc,
                    company_name: Optional[str] = None, usage_collector = None, config: AnalyserConfig = None) -> CallAnalyser:
    return CallAnalyser(config=config, core_api_client=core_api_client, usage_collector=usage_collector)
