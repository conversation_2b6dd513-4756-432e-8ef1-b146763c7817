from .turn_feature_detector import TurnFeatureDetector


class ContextualAnswerDetector(TurnFeatureDetector):
    def __init__(self, ack_set=None):
        self.ack_set = ack_set or {"ok", "okay", "yes", "uh-huh", "ask", "yeah", "yep", "yup", "right", "gotcha", "mhm",
                                   "mm-hmm", "hmm", "I see", "sure", "roger"}

    def check(self, chat_ctx):
        msgs = chat_ctx.items
        last_agent = next((m.text_content.strip() for m in reversed(msgs) if m.role == "assistant"), "")
        last_user = next((m.text_content.lower().strip() for m in reversed(msgs) if m.role == "user"), "")
        if last_agent.strip().endswith("?") and last_user in self.ack_set:
            return True
        return False
