import asyncio

from .turn_feature_detector import TurnFeatureDetector


class ProsodicBackchannelDetector(TurnFeatureDetector):
    def __init__(self, ack_set=None, max_ack_words=2, energy_thresh=0.01, vad=None):
        self.ack_set = ack_set or {"ok", "okay", "yes", "uh-huh", "ask", "yeah", "yep", "yup", "right", "gotcha", "mhm",
                                   "mm-hmm", "hmm", "I see", "sure", "roger"}
        self.max_ack_words = max_ack_words
        self.energy_thresh = energy_thresh
        self.vad = vad

    async def check(self, chat_ctx):
        texts = [m.text_content.lower().strip() for m in chat_ctx.items if
                 hasattr(m, "role") and m.role == "user" and m.text_content]
        if not texts:
            return False
        last = texts[-1]
        if len(last.split()) <= self.max_ack_words and last in self.ack_set:
            attr = getattr(chat_ctx, "last_audio_chunk", None)
            if attr is None:
                return False
            audio_chunk = attr() if callable(attr) else attr
            if audio_chunk is None:
                return False
            energy_var = await asyncio.to_thread(self.vad.compute_energy_variance, audio_chunk)
            if energy_var < self.energy_thresh:
                return True
        return False
