"""
Simple Voicemail Detection for LiveKit Agents

This module provides a lightweight voicemail detection function that can be easily
integrated with LiveKit agents using LLM function calling. It analyzes conversation
patterns to identify likely voicemail scenarios.
"""

import asyncio
import logging
from dataclasses import dataclass
from typing import Optional, Callable, Any
from livekit.agents import function_tool

logger = logging.getLogger(__name__)


@dataclass
class VoicemailDetectionConfig:
    """Configuration for voicemail detection"""
    enabled: bool = True
    timeout_seconds: float = 30.0  # How long to wait for response before considering voicemail
    confidence_threshold: float = 0.8  # Confidence level needed to trigger voicemail detection
    max_detection_attempts: int = 3  # Maximum attempts to detect voicemail
    

class VoicemailDetector:
    """
    Simple voicemail detector that uses LLM function calling to identify voicemail scenarios.
    
    This detector looks for common voicemail patterns like:
    - Long automated messages without user interaction
    - Phrases like "leave a message", "after the beep", "not available"
    - One-way conversation patterns typical of voicemail greetings
    
    When voicemail is detected, it will immediately disconnect from the room.
    """
    
    def __init__(self, config: VoicemailDetectionConfig = None, on_voicemail_detected: Optional[Callable] = None, room=None):
        self.config = config or VoicemailDetectionConfig()
        self.on_voicemail_detected = on_voicemail_detected
        self.detection_attempts = 0
        self.is_voicemail_detected = False
        self.conversation_start_time = None
        self.last_user_speech_time = None
        self.room = room  # LiveKit room instance for immediate disconnect
        
    def reset(self):
        """Reset detection state for new call"""
        self.detection_attempts = 0
        self.is_voicemail_detected = False
        self.conversation_start_time = None
        self.last_user_speech_time = None
        
    @function_tool
    async def detect_voicemail_pattern(
        self,
        transcribed_text: str,
        speaker_type: str = "unknown",  # "user" or "assistant" or "unknown"
        confidence: float = 0.0
    ) -> dict[str, Any]:
        """
        Analyze transcribed text for voicemail patterns.
        
        This function should be called by the LLM when it detects potential voicemail content.
        
        Args:
            transcribed_text: The text that was transcribed from audio
            speaker_type: Who is speaking ("user", "assistant", or "unknown")
            confidence: Confidence level (0.0 to 1.0) that this is voicemail
            
        Returns:
            dict with detection results and recommended actions
        """
        if not self.config.enabled or self.is_voicemail_detected:
            return {"action": "continue", "voicemail_detected": False}
            
        logger.info(f"Analyzing potential voicemail pattern: '{transcribed_text[:100]}...'")
        
        # Common voicemail indicators
        voicemail_phrases = [
            "leave a message",
            "after the beep",
            "not available",
            "please record your message",
            "at the tone",
            "you have reached",
            "cannot take your call",
            "please try again later",
            "mailbox",
            "voicemail",
            "answering machine",
            "please hold",
            "busy signal"
        ]
        
        # Check for voicemail phrases
        text_lower = transcribed_text.lower()
        phrase_matches = sum(1 for phrase in voicemail_phrases if phrase in text_lower)
        pattern_confidence = min(phrase_matches * 0.3, 1.0)
        
        # Boost confidence if this is clearly automated speech
        if speaker_type == "unknown" and len(transcribed_text) > 50:
            pattern_confidence += 0.2
            
        # Boost confidence if it's a long monologue without user interaction
        if len(transcribed_text) > 100 and speaker_type != "user":
            pattern_confidence += 0.2
            
        total_confidence = min(confidence + pattern_confidence, 1.0)
        
        self.detection_attempts += 1
        
        logger.info(f"Voicemail detection attempt {self.detection_attempts}: confidence={total_confidence:.2f}")
        
        if total_confidence >= self.config.confidence_threshold:
            self.is_voicemail_detected = True
            logger.info("🎯 Voicemail detected! Taking appropriate action.")
            
            # Trigger callback if provided
            if self.on_voicemail_detected:
                try:
                    await self.on_voicemail_detected(transcribed_text, total_confidence)
                except Exception as e:
                    logger.error(f"Error in voicemail callback: {e}")
            
            return {
                "action": "voicemail_detected",
                "voicemail_detected": True,
                "confidence": total_confidence,
                "matched_phrases": phrase_matches,
                "recommendation": "leave_message_and_hangup"
            }
        
        # If we've tried too many times without clear detection, assume it's not voicemail
        if self.detection_attempts >= self.config.max_detection_attempts:
            logger.info("Maximum detection attempts reached. Assuming live person.")
            return {
                "action": "continue_conversation", 
                "voicemail_detected": False,
                "confidence": total_confidence
            }
            
        return {
            "action": "continue_monitoring",
            "voicemail_detected": False,
            "confidence": total_confidence,
            "attempts": self.detection_attempts
        }

    def get_voicemail_function_tool(self):
        """
        Get the function tool that can be added to your agent's tools list.
        
        Usage:
            detector = VoicemailDetector()
            agent = Agent(
                tools=[detector.get_voicemail_function_tool(), other_tools...]
            )
        """
        return self.detect_voicemail_pattern


# Convenience function for easy integration
def create_voicemail_detector(
    enabled: bool = True,
    timeout_seconds: float = 30.0,
    confidence_threshold: float = 0.8,
    on_voicemail_detected: Optional[Callable] = None
) -> VoicemailDetector:
    """
    Create a voicemail detector with simple configuration.
    
    Args:
        enabled: Whether voicemail detection is enabled
        timeout_seconds: Timeout for voicemail detection
        confidence_threshold: Minimum confidence to trigger detection
        on_voicemail_detected: Callback function when voicemail is detected
        
    Returns:
        Configured VoicemailDetector instance
    """
    config = VoicemailDetectionConfig(
        enabled=enabled,
        timeout_seconds=timeout_seconds,
        confidence_threshold=confidence_threshold
    )
    
    return VoicemailDetector(config=config, on_voicemail_detected=on_voicemail_detected)


# Example usage and integration helper
async def example_voicemail_callback(transcribed_text: str, confidence: float):
    """
    Example callback function that gets called when voicemail is detected.
    
    This is where you would implement your voicemail handling logic,
    such as playing a message and hanging up.
    """
    logger.info(f"Voicemail detected with {confidence:.1%} confidence!")
    logger.info(f"Detected text: {transcribed_text[:200]}...")
    
    # Example actions you might take:
    # 1. Play a pre-recorded message
    # 2. Record the voicemail greeting for analysis
    # 3. Hang up gracefully
    # 4. Log the interaction for review
    
    print("🎯 VOICEMAIL DETECTED - Implement your handling logic here!")


if __name__ == "__main__":
    # Example of how to test the detector
    async def test_detector():
        detector = create_voicemail_detector(
            confidence_threshold=0.6,
            on_voicemail_detected=example_voicemail_callback
        )
        
        # Test with some sample voicemail text
        test_cases = [
            "Hi, you have reached John's voicemail. Please leave a message after the beep.",
            "Sorry, I cannot take your call right now. Please try again later.",
            "Hello, this is a live person speaking. How can I help you today?",
            "You have reached the automated system. Please record your message at the tone."
        ]
        
        for text in test_cases:
            result = await detector.detect_voicemail_pattern(
                transcribed_text=text,
                speaker_type="unknown",
                confidence=0.5
            )
            print(f"Text: {text[:50]}...")
            print(f"Result: {result}")
            print("-" * 50)
            
    # Run the test
    asyncio.run(test_detector())