import asyncio
import collections
import logging
from contextlib import suppress
from dataclasses import dataclass
from typing import AsyncIterable, AsyncIterator, Optional

import numpy as np
from livekit.agents import APIConnectOptions
from livekit.agents import stt
from livekit.rtc import AudioFrame

logger = logging.getLogger(__name__)


# --------- low-level helpers

def _to_mono_float32(frame: AudioFrame) -> np.ndarray:
    """PCM16 -> mono float32 in [-1, 1]."""
    if not frame or not frame.data or frame.samples_per_channel == 0:
        return np.array([], dtype=np.float32)
    pcm = np.frombuffer(frame.data, dtype=np.int16).astype(np.float32) / 32768.0
    if frame.num_channels > 1:
        pcm = pcm.reshape(-1, frame.num_channels).mean(axis=1)
    return pcm


def _resample(x: np.ndarray, src_hz: int, dst_hz: int) -> np.ndarray:
    if src_hz == dst_hz or x.size == 0:
        return x
    # lightweight rational resample
    import math
    from numpy import gcd as _gcd  # numpy>=2; for numpy<2 use math.gcd on ints
    g = int(_gcd(src_hz, dst_hz)) if hasattr(np, 'gcd') else math.gcd(src_hz, dst_hz)
    up, down = dst_hz // g, src_hz // g
    # polyphase via spsample if available; otherwise simple decimate/interp
    try:
        from scipy.signal import resample_poly
        return resample_poly(x, up, down).astype(np.float32, copy=False)
    except Exception:
        # naive fallback
        idx = np.round(np.arange(0, len(x) * dst_hz / src_hz)).astype(int)
        idx = np.clip(idx, 0, len(x) - 1)
        return x[idx].astype(np.float32, copy=False)


# --------- VAD + tone/beep guard

@dataclass
class GateConfig:
    sample_rate: int = 16000
    window_ms: int = 20  # 10/20/30ms windows for VAD
    min_voice_ms: int = 300  # require this much continuous voice
    use_webrtcvad: bool = True  # auto-falls back if not installed
    anti_tone: bool = True  # reject narrow-band tones (ring/busy/beep)
    anti_music: bool = True  # reject likely music
    ring_cadence_guard: bool = True  # reject PSTN ring cadence (on/off pattern)


class VoiceActivityGate:
    """
    Streams AudioFrame -> fires once we confirm 'human speech' (not tone).
    Two signals:
      - .first_speech: asyncio.Event() set when speech detected
      - .triggered: bool
    """

    def __init__(self, cfg: GateConfig = GateConfig()):
        self.cfg = cfg
        self.first_speech = asyncio.Event()
        self.triggered = False
        self._buf = np.array([], dtype=np.float32)
        self._voice_ms = 0
        self._hist_peaks = collections.deque(maxlen=8)  # [(f1,f2,...)] dominant peaks per segment
        self._hist_energy = collections.deque(maxlen=40)  # segment energies (0.5s window → ~20s history)
        self._prev_spec = None  # previous magnitude spectrum (for spectral flux)
        self._onoff = collections.deque(maxlen=8)  # recent (on/off) durations in seconds

        self._vad = None
        if cfg.use_webrtcvad:
            try:
                import webrtcvad  # pip install webrtcvad
                self._vad = webrtcvad.Vad(2)  # 0-3 aggressiveness
            except Exception:
                self._vad = None

    # --- replace _is_tone with this ---
    def _is_tone(self, x: np.ndarray) -> bool:
        """Reject pure/narrow multi-tone (ring/busy/beep)."""
        if x.size == 0:
            return False
        spec = np.abs(np.fft.rfft(x)) ** 2
        total = np.sum(spec)
        if total <= 0:
            return True

        # save peaks & compute stability
        peaks_hz = self._dominant_peaks(spec, top_k=3)
        if peaks_hz.size > 0:
            self._hist_peaks.append(peaks_hz)

        # info measures
        p = spec / total
        entropy = -np.sum(p * np.log2(p + 1e-12))
        peakiness = float((spec.max() + 1e-9) / (spec.mean() + 1e-9))

        # narrowband → few peaks, very stable across time, high peakiness, low entropy
        freq_stab = self._freq_stability(list(self._hist_peaks), tol_hz=8.0)

        # additionally require low spectral flux across frames to avoid misclassifying singing/sustained music
        flux = self._spectral_flux(self._prev_spec, spec)
        self._prev_spec = spec

        narrow_peaks = (peaks_hz.size <= 3)
        very_stable = (freq_stab < 6.0)  # Hz std
        low_entropy = (entropy < 4.2)
        very_peaky = (peakiness > 22.0)
        low_flux = (flux < 0.05)

        return narrow_peaks and very_stable and low_entropy and very_peaky and low_flux

    def _is_vad_voice(self, x16k: np.ndarray) -> bool:
        """Use webrtcvad if available; else energy+ZCR heuristics."""
        win = int(self.cfg.window_ms * self.cfg.sample_rate / 1000)
        if x16k.size < win:
            return False
        if self._vad:
            start = 0
            voiced = 0
            needed = max(1, int(200 / self.cfg.window_ms))  # ~200ms total voiced chunks
            while start + win <= x16k.size:
                chunk = x16k[start:start + win]
                pcm16 = np.clip(chunk * 32768, -32768, 32767).astype(np.int16).tobytes()
                if self._vad.is_speech(pcm16, self.cfg.sample_rate):
                    voiced += 1
                start += win
            return voiced >= needed
        # heuristics
        x = x16k
        energy = float(np.sum(x * x) / (len(x) + 1e-9))
        zcr = float(np.mean(np.abs(np.diff(np.sign(x)))))
        return energy > 1e-4 and 0.05 < zcr < 0.25

    # --- add inside class VoiceActivityGate ---

    def _dominant_peaks(self, spec: np.ndarray, top_k: int = 4) -> np.ndarray:
        """Return frequencies (Hz) of top_k narrow peaks in 100–4000 Hz band."""
        if np.sum(spec) <= 0:
            return np.array([], dtype=np.float32)
        # restrict to 100–4000 Hz
        n = spec.size
        freqs = np.linspace(0, self.cfg.sample_rate / 2, n, dtype=np.float32)
        band = (freqs >= 100) & (freqs <= 4000)
        s = spec[band]
        f = freqs[band]
        if s.size < 8:
            return np.array([], dtype=np.float32)

        # try SciPy for robust peak pick; fall back to argpartition
        try:
            from scipy.signal import find_peaks
            peaks, _ = find_peaks(s, height=np.percentile(s, 90), distance=5)
            if peaks.size == 0:
                return np.array([], dtype=np.float32)
            idx = peaks[np.argsort(s[peaks])[-top_k:]]
        except Exception:
            k = min(top_k, s.size)
            idx = np.argpartition(s, -k)[-k:]
        # sort by freq
        return np.sort(f[idx])

    def _freq_stability(self, peak_hist: list[np.ndarray], tol_hz: float = 8.0) -> float:
        """Return mean std-dev (Hz) across tracked peaks; small → very stable tone(s)."""
        if len(peak_hist) < 3:
            return 1e6
        # align by count
        m = min(len(p) for p in peak_hist)
        if m == 0:
            return 1e6
        stack = np.stack([p[:m] for p in peak_hist], axis=0)  # [T, m]
        return float(np.mean(np.std(stack, axis=0)))

    def _spectral_flux(self, prev: np.ndarray, cur: np.ndarray) -> float:
        """Normalized spectral flux between consecutive magnitude spectra."""
        if prev is None or prev.size != cur.size:
            return 0.0
        prev_n = prev / (np.sum(prev) + 1e-9)
        cur_n = cur / (np.sum(cur) + 1e-9)
        return float(np.sqrt(np.sum((cur_n - prev_n) ** 2)))

    def _ring_cadence_like(self) -> bool:
        """
        PSTN ring is cadencey: ~0.8–1.5s ON, ~2–5s OFF repeating.
        Using 0.5s analysis windows, check for ON/OFF pattern.
        """
        if not self.cfg.ring_cadence_guard or len(self._onoff) < 4:
            return False
        # We only need to see ~2 cycles of (on, off) in plausible ranges.
        ons = [d for (state, d) in self._onoff if state == "on"]
        offs = [d for (state, d) in self._onoff if state == "off"]
        if len(ons) >= 2 and len(offs) >= 2:
            on_ok = all(0.6 <= d <= 1.6 for d in ons[-2:])
            off_ok = all(1.5 <= d <= 5.5 for d in offs[-2:])
            return on_ok and off_ok
        return False

    def _is_music(self, spec: np.ndarray, freqs: np.ndarray) -> bool:
        """
        Lightweight music heuristic:
          - wide bandwidth (rolloff high),
          - high spectral entropy,
          - not dominated by 1–2 sharp peaks (low peakiness),
          - subband flatness relatively high (broad spectrum).
        """
        if not self.cfg.anti_music:
            return False
        total = np.sum(spec)
        if total <= 0:
            return False

        # entropy
        p = spec / total
        entropy = -np.sum(p * np.log2(p + 1e-12))

        # rolloff ~ 90% energy
        cumsum = np.cumsum(spec)
        idx = np.searchsorted(cumsum, 0.9 * cumsum[-1])
        rolloff_hz = float(freqs[min(idx, len(freqs) - 1)])

        # peakiness
        peakiness = float((spec.max() + 1e-9) / (spec.mean() + 1e-9))

        # subband flatness (5 bands)
        bands = np.array_split(spec, 5)
        flatness_bands = []
        for b in bands:
            gm = np.exp(np.mean(np.log(b + 1e-12)))
            am = np.mean(b + 1e-12)
            flatness_bands.append(float(gm / am))
        med_flat = float(np.median(flatness_bands))

        # thresholds tuned conservatively
        return (entropy > 6.5 and rolloff_hz > 4500 and peakiness < 10.0 and med_flat > 0.25)

    def push_frame(self, frame: AudioFrame) -> None:
        if self.triggered:
            return
        mono = _to_mono_float32(frame)
        if mono.size == 0:
            return
        mono = _resample(mono, frame.sample_rate, self.cfg.sample_rate)
        self._buf = np.append(self._buf, mono)

        # analyze ~0.5s windows
        W = int(0.5 * self.cfg.sample_rate)
        while self._buf.size >= W and not self.triggered:
            segment = self._buf[:W]
            self._buf = self._buf[W:]

            if self.cfg.anti_tone and self._is_tone(segment):
                # ignore tone completely; do not accumulate voice_ms
                continue

            if self._is_vad_voice(segment):
                self._voice_ms += 500
            else:
                self._voice_ms = max(0, self._voice_ms - 250)  # decay

            if self._voice_ms >= self.cfg.min_voice_ms:
                self.triggered = True
                self.first_speech.set()

    async def wait_first_speech(self, timeout: float = 30.0) -> bool:
        try:
            await asyncio.wait_for(self.first_speech.wait(), timeout=timeout)
            return True
        except asyncio.TimeoutError:
            return False


# --------- Gated STT wrapper (for JanaAgent)

class GatedSpeechStream(stt.SpeechStream):
    """Forwards frames to delegate only after gate fires."""

    def __init__(self, delegate_stream: stt.SpeechStream, gate: VoiceActivityGate, sample_rate: int, stt_obj: stt.STT,
                 conn_options: Optional[dict] = None):
        super().__init__(stt=stt_obj, conn_options=conn_options or APIConnectOptions(), sample_rate=sample_rate, )
        self.delegate_stream = delegate_stream
        self.gate = gate
        self.sample_rate = sample_rate
        self._closed = False

    def push_frame(self, frame: AudioFrame) -> None:
        if self._closed:
            return
        # always feed the gate
        self.gate.push_frame(frame)
        # only forward after speech confirmed
        if self.gate.triggered:
            try:
                self.delegate_stream.push_frame(frame)
            except Exception as e:
                logger.error(f"delegate push_frame error: {e}")

    async def __aiter__(self) -> AsyncIterator[stt.SpeechEvent]:
        try:
            async for ev in self.delegate_stream:
                yield ev
        except Exception as e:
            logger.error(f"iterate delegate error: {e}")
            raise

    async def _run(self) -> None:
        # pass-through
        async for ev in self.delegate_stream:
            self._push_event(ev)

    def close(self):
        self._closed = True
        with suppress(Exception):
            self.delegate_stream.close()


class SpeechFilterSTT(stt.STT):
    """
    Wraps a real STT (e.g., Deepgram STT) and gates audio until human speech is detected.
    Use this in JanaAgent.stt_node.
    """

    def __init__(self, delegate: stt.STT, gate_cfg: GateConfig = GateConfig()):
        super().__init__(capabilities=delegate.capabilities)
        self.delegate = delegate
        self.gate_cfg = gate_cfg

    def new_gate(self) -> VoiceActivityGate:
        return VoiceActivityGate(cfg=self.gate_cfg)

    def stream(self, *args, **kwargs) -> stt.SpeechStream:
        sample_rate = kwargs.pop('sample_rate', 16000)
        conn_options = kwargs.get('conn_options')
        if isinstance(conn_options, dict) or conn_options is None:
            conn_options = APIConnectOptions()

        delegate_stream = self.delegate.stream(*args, **kwargs)
        gate = self.new_gate()
        return GatedSpeechStream(delegate_stream=delegate_stream, gate=gate, sample_rate=sample_rate,
                                 stt_obj=self.delegate, conn_options=conn_options)

    async def _recognize_impl(self, audio: AsyncIterable[AudioFrame], *args, **kwargs) -> stt.SpeechEvent:
        # one-shot recognition, gated
        sample_rate = kwargs.pop('sample_rate', 16000)
        conn_options = kwargs.get('conn_options')
        if isinstance(conn_options, dict) or conn_options is None:
            conn_options = APIConnectOptions()
        stream = self.stream(sample_rate=sample_rate)
        feeder_task = asyncio.create_task(_feed_frames(stream, audio))
        try:
            async for ev in stream:
                return ev
        finally:
            feeder_task.cancel()
            with suppress(Exception):
                await feeder_task
            stream.close()


# helper to push and read concurrently
async def _feed_frames(stream: stt.SpeechStream, audio: AsyncIterable[AudioFrame]) -> None:
    async for f in audio:
        stream.push_frame(f)
