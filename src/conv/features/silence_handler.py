import asyncio
import threading
from concurrent.futures import Thread<PERSON>ool<PERSON>xecutor
from typing import Callable


class SilenceHandler:
    def __init__(
            self,
            say_callback: Callable[[], None],
            timer_duration=10,
            loop: asyncio.AbstractEventLoop = None,
    ):
        self.timer_duration = timer_duration  # Duration in seconds
        self.timer_future = None
        self.cancel_event = threading.Event()
        self.say_callback = say_callback
        self.lock = threading.Lock()
        self.executor = ThreadPoolExecutor(max_workers=1)
        self.cancel_event = threading.Event()
        self.loop = loop or asyncio.get_event_loop()

    def cancel_timer(self):
        with self.lock:
            self._cancel_timer()

    def start_timer(self):
        with self.lock:
            self._cancel_timer()
            self.cancel_event.clear()  # Reset the cancel event
            self.timer_future = self.executor.submit(self._wait_and_trigger_timer)
            print("Timer started: Waiting for user to start speaking.")

    def _wait_and_trigger_timer(self):
        # Wait for the timer duration or until the cancel event is set
        is_canceled = self.cancel_event.wait(timeout=self.timer_duration)
        with self.lock:
            self.timer_future = None  # Reset the future
        if not is_canceled:
            # Schedule the coroutine to run in the event loop
            self.say_callback()

    def _cancel_timer(self):
        if self.timer_future is not None:
            self.cancel_event.set()  # Signal cancellation to the timer thread
            self.timer_future.cancel()
            self.timer_future = None

    def shutdown(self):
        with self.lock:
            self._cancel_timer()
        self.executor.shutdown(wait=True)
