import logging
from livekit import rtc
from livekit.agents import BackgroundAudioPlayer, AgentSession, AudioConfig, BuiltinAudioClip
_logger = logging.getLogger(__name__)


def create_default_background_audio_player() -> BackgroundAudioPlayer:
    return BackgroundAudioPlayer(
        ambient_sound=AudioConfig(BuiltinAudioClip.OFFICE_AMBIENCE, volume=0.5),
        thinking_sound=[
            AudioConfig(BuiltinAudioClip.KEYBOARD_TYPING, volume=0.6),
            AudioConfig(BuiltinAudioClip.KEYBOARD_TYPING2, volume=0.5),
        ]
    )
class BackgroundAudioManager:
    def __init__(self, background_audio_player: BackgroundAudioPlayer = None):
        self.background_audio_player  = background_audio_player or create_default_background_audio_player()
        self._background_audio_started = False
        self._background_audio_closed = False

    async def start(self, room: rtc.Room, agent_session: AgentSession):
        """Start background audio if provided"""
        if self.background_audio_player and not self._background_audio_started:
            try:
                await self.background_audio_player.start(room=room, agent_session=agent_session)
                self._background_audio_started = True
                _logger.info("Background audio started")
            except Exception as e:
                _logger.error(f"Failed to start background audio: {e}")
                # Continue without background audio if it fails to start
                self.background_audio_player = None
                raise

    async def cleanup(self):
        """Properly cleanup background audio player"""
        if self.background_audio_player and self._background_audio_started and not self._background_audio_closed:
            self._background_audio_closed = True
            try:
                await self.background_audio_player.aclose()
                _logger.info("Background audio closed successfully")
            except Exception as e:
                _logger.warning(f"Error closing background audio: {e}")
            finally:
                self.background_audio_player = None

    def is_started(self) -> bool:
        """Check if background audio is started"""
        return self._background_audio_started

    def is_closed(self) -> bool:
        """Check if background audio is closed"""
        return self._background_audio_closed

    def has_background_audio(self) -> bool:
        """Check if background audio player is available"""
        return self.background_audio_player is not None