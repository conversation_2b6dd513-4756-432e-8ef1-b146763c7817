import asyncio
import logging
from typing import Dict, Callable, Optional, Any
from livekit import rtc
from livekit.agents import AgentSession

_logger = logging.getLogger(__name__)


class DTMFReceiver:
    """
    Handles DTMF tone reception and processing during conversations.
    Can be registered with ConvManager to handle keypad inputs.
    """
    
    def __init__(self, room: rtc.Room, session: AgentSession):
        self.room = room
        self.session = session
        self._dtmf_handlers: Dict[str, Callable[[str], Any]] = {}
        self._is_active = False
        self._dtmf_timeout = 5.0  # seconds to wait for DTMF input
        self._collected_digits = ""
        self._max_digits = 10
        room.on("sip_dtmf_received", self.handle_dtmf)

    def handle_dtmf(dtmf_event: rtc.SipDTMF):
        """
        Synchronous handler for DTMF signals that schedules the async logic.

        Args:
            dtmf_event (rtc.SipDTMF): The DTMF event data.
        """
        _logger.info("DTMF receiver: " + dtmf_event.digit)

    async def start(self):
        """Start listening for DTMF tones"""
        if self._is_active:
            _logger.warning("DTMF receiver already active")
            return
            
        self._is_active = True
        _logger.info("DTMF receiver started")


        # Register for DTMF events on all participants
        # for participant in self.room.remote_participants.values():
        #     await self._register_participant_dtmf(participant)
        #


        # Listen for new participants joining
        self.room.on("participant_connected", self._on_participant_connected_sync)
        
    async def stop(self):
        """Stop listening for DTMF tones"""
        if not self._is_active:
            return
            
        self._is_active = False
        self._collected_digits = ""
        _logger.info("DTMF receiver stopped")
        
    def register_dtmf_handler(self, pattern: str, handler: Callable[[str], Any]):
        """
        Register a handler for a specific DTMF pattern
        
        Args:
            pattern: DTMF pattern to match (e.g., "*", "#", "1", "123", etc.)
            handler: Function to call when pattern is matched
        """
        self._dtmf_handlers[pattern] = handler
        _logger.debug(f"Registered DTMF handler for pattern: {pattern}")
        
    def unregister_dtmf_handler(self, pattern: str):
        """Remove a DTMF handler"""
        if pattern in self._dtmf_handlers:
            del self._dtmf_handlers[pattern]
            _logger.debug(f"Unregistered DTMF handler for pattern: {pattern}")
            
    async def collect_digits(self, max_digits: int = 10, timeout: float = 5.0, 
                           terminator: str = "#") -> Optional[str]:
        """
        Collect DTMF digits from user input
        
        Args:
            max_digits: Maximum number of digits to collect
            timeout: Timeout in seconds
            terminator: Digit that terminates collection
            
        Returns:
            Collected digits string or None if timeout/cancelled
        """
        if not self._is_active:
            _logger.warning("DTMF receiver not active")
            return None
            
        self._collected_digits = ""
        self._max_digits = max_digits
        
        try:
            # Wait for digits or timeout
            return await asyncio.wait_for(
                self._wait_for_collection(terminator), 
                timeout=timeout
            )
        except asyncio.TimeoutError:
            _logger.debug(f"DTMF collection timeout after {timeout}s")
            return None
            
    async def _wait_for_collection(self, terminator: str) -> str:
        """Wait for digit collection to complete"""
        while len(self._collected_digits) < self._max_digits:
            await asyncio.sleep(0.1)
            if terminator and self._collected_digits.endswith(terminator):
                # Remove terminator and return
                return self._collected_digits[:-1]
        return self._collected_digits
        
    async def _register_participant_dtmf(self, participant: rtc.RemoteParticipant):
        """Register DTMF event handler for a participant"""
        try:
            # In a real implementation, you would listen for DTMF events
            # This is a placeholder for the actual DTMF event handling
            _logger.debug(f"Registered DTMF listener for participant: {participant.identity}")
        except Exception as e:
            _logger.error(f"Failed to register DTMF for participant {participant.identity}: {e}")
            
    def _on_participant_connected_sync(self, participant: rtc.RemoteParticipant):
        """Handle new participant connection (sync wrapper)"""
        if self._is_active:
            asyncio.create_task(self._register_participant_dtmf(participant))
            
    async def _on_participant_connected(self, participant: rtc.RemoteParticipant):
        """Handle new participant connection"""
        if self._is_active:
            await self._register_participant_dtmf(participant)
            
    async def _handle_dtmf_tone(self, digit: str, participant: rtc.RemoteParticipant):
        """
        Handle received DTMF tone
        
        Args:
            digit: The DTMF digit received
            participant: The participant who sent the tone
        """
        if not self._is_active:
            return
            
        _logger.debug(f"Received DTMF digit '{digit}' from {participant.identity}")
        
        # Add to collected digits
        self._collected_digits += digit
        
        # Check for immediate pattern matches
        await self._check_pattern_matches(digit)
        
    async def _check_pattern_matches(self, digit: str):
        """Check if any registered patterns match the current input"""
        for pattern, handler in self._dtmf_handlers.items():
            try:
                # Check single digit patterns
                if pattern == digit:
                    await self._call_handler(handler, digit)
                    
                # Check if collected digits end with pattern
                elif self._collected_digits.endswith(pattern):
                    await self._call_handler(handler, pattern)
                    
            except Exception as e:
                _logger.error(f"Error in DTMF handler for pattern '{pattern}': {e}")
                
    async def _call_handler(self, handler: Callable, matched_pattern: str):
        """Safely call a DTMF handler"""
        try:
            if asyncio.iscoroutinefunction(handler):
                await handler(matched_pattern)
            else:
                handler(matched_pattern)
        except Exception as e:
            _logger.error(f"Error executing DTMF handler: {e}")
            
    def get_collected_digits(self) -> str:
        """Get currently collected digits"""
        return self._collected_digits
        
    def clear_collected_digits(self):
        """Clear collected digits buffer"""
        self._collected_digits = ""
        
    @property
    def is_active(self) -> bool:
        """Check if DTMF receiver is active"""
        return self._is_active