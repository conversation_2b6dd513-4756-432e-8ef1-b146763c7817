import json
from enum import Enum
from typing import Optional, List

from pydantic import BaseModel, Field
from typing_extensions import Literal


class MetadataType(str, Enum):
    TEST_OUTBOUND_CAMPAIGN_CALL = "test-outbound-campaign-call"
    TEST_CALL = "test-call"
    OUTBOUND_CAMPAIGN_CALL = "outbound-campaign-call"
    INBOUND_CAMPAIGN_CALL = "inbound-campaign-call"
    INBOUND_TEST_CALL = "inbound-test-call"


class AnalysisSettings(BaseModel):
    prompt: Optional[str] = Field(
        default=None,
        description="Custom prompt for conversation analysis"
    )


class VoiceSettings(BaseModel):
    provider: Optional[str] = Field(default="11labs", description="TTS provider name")
    name: Optional[str] = Field(default="")
    voiceId: Optional[str] = Field(default="56AoDkrOh6qfVPDXZ7Pt", description="Voice ID used by TTS")
    model: Optional[str] = Field(default="")
    language: Optional[str] = Field(default="eng", description="Language used by the TTS")
    vad_profile: Optional[str] = Field(default="strict")

    @staticmethod
    def factory(data: dict) -> "VoiceSettings":
        provider = data.get("provider", "11labs").lower()
        if provider == "11labs":
            return VoiceSettings11Labs(**data)
        elif provider == "playai":
            return VoiceSettingsPlayai(**data)
        else:
            return VoiceSettings(**data)


class VoiceSettings11Labs(VoiceSettings):
    provider: Optional[str] = Field(default="11labs", description="TTS provider name")
    voiceId: Optional[str] = Field(default="56AoDkrOh6qfVPDXZ7Pt", description="Voice ID used by TTS")
    model: Optional[str] = Field(default="")
    similarity_boost: Optional[float] = Field(
        default=0.6, description="Boost for similarity"
    )
    stability: Optional[float] = Field(default=0.4, description="Stability factor")
    style: Optional[float] = Field(default=0.0, description="Style parameter")
    use_speaker_boost: Optional[bool] = Field(
        default=True, description="Flag to use speaker boost"
    )
    vad_profile: Optional[str] = Field(default="phone_call", description="VAD profile name")
    streaming_latency: Optional[int] = Field(default=2, description="Steaming latency in seconds")


class VoiceSettingsPlayai(VoiceSettings):
    provider: Optional[str] = Field(default="playai", description="TTS provider name")
    voiceId: Optional[str] = Field(default="56AoDkrOh6qfVPDXZ7Pt", description="Voice ID used by TTS")
    model: Optional[str] = Field(default="PlayDialog")
    sample_rate: Optional[int] = 22050


class LLMSettings(BaseModel):
    name: Optional[str] = Field(default="Camilla", description="Name of the LLM")
    model: Optional[str] = Field(default="gpt-4o", description="Model identifier for the LLM")
    profile: Optional[str] = Field(
        default="salesrepresentative", description="Profile used by the LLM"
    )
    language: Optional[str] = Field(default="eng", description="Language used by the LLM")
    prompt: Optional[str] = Field(default="", description="Prompt for the LLM")
    profilePrompt: Optional[str] = Field(default="You are helpful assistant", description="Profile prompt for the LLM")


class ConversationContext(BaseModel):
    companyId: Optional[str] = Field(default=None, description="UUID of the company")
    companyName: Optional[str] = Field(default="OneTop Company", description="Name of the company")
    conversationId: Optional[str] = Field(default=None, description="UUID of the conversation")
    campaignId: Optional[str] = Field(default=None, description="UUID of the campaign")
    timezone: Optional[str] = Field(default="UTC", description="Timezone for the context")
    participant: Optional[dict] = Field(default=None,
                                        description="Participant information dictionary containing phone and details")
    callSchedule: Optional[dict] = Field(default=None, description="Call schedule information")


FunctionType = Literal["send_follow_up_message", "schedule_callback", "donot_call"]


class RoomConfig(BaseModel):
    type: str = Field(
        default=str(MetadataType.TEST_OUTBOUND_CAMPAIGN_CALL),
        description="Type of metadata",
    )
    llm: LLMSettings = Field(default_factory=LLMSettings, description="LLM configuration")
    voice: VoiceSettings = Field(default_factory=VoiceSettings, description="TTS configuration")
    context: ConversationContext = Field(default_factory=ConversationContext, description="Contextual information")
    analysis: Optional[AnalysisSettings] = Field(
        default_factory=AnalysisSettings,
        description="Analysis configuration"
    )
    functions: List[FunctionType] = Field(
        default_factory=lambda: ['schedule_callback', 'end_conversation', 'donot_call'],
        description="List of enabled functions for the conversation"
    )
    is_closed: Optional[bool] = Field(default=False, description="Is session closed")
    video_enabled: Optional[bool] = Field(default=False, description="Enable video avatar")
    avatar_config: Optional[dict] = Field(default=None, description="Avatar configuration (provider, settings, etc.)")

    def model_post_init(self, *args, **kwargs):
        # Ensure mandatory functions are always present
        mandatory_functions = {'schedule_callback', 'end_conversation',
                               'donot_call'}  # {"schedule_callback", "donot_call"}
        functions_set = set(self.functions)
        functions_set.update(mandatory_functions)
        self.functions = list(functions_set)

    @classmethod
    def close_session(cls):
        cls.is_closed = True

    def is_session_closed(self):
        return self.is_closed

    @classmethod
    def load_json(cls, data: str) -> 'RoomConfig':
        parsed_data = json.loads(data)
        return cls.load_dict(parsed_data)

    @classmethod
    def load_dict(cls, data: dict) -> 'RoomConfig':
        # Use factory for voice settings
        if "voice" in data:
            data["voice"] = VoiceSettings.factory(data["voice"])
        return cls.model_validate(data)
