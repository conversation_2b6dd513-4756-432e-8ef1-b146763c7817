import asyncio
import json
import logging
import uuid
from multiprocessing import Event as ProcessEvent
from threading import Event as ThreadEvent
from typing import Any

import langfuse
import sentry_sdk
from livekit import rtc
from livekit.agents import JobContext, AgentSession, ConversationItemAddedEvent, AgentStateChangedEvent
from livekit.agents import UserStateChangedEvent
from livekit.plugins import groq, silero
from livekit.plugins.turn_detector.english import EnglishModel

from agents.agent_conf import get_agent_info_from_meta
from agents.jana_agent import JanaAgent
from agents.settings import AgentSettings
from app.config import get_config
from conv.analysers.analyser import AnalyserConfig
from conv.analysers.analysers import create_analyser
from conv.conv_meta import RoomConfig
from conv.conv_svc import ConvSvc
from conv.features.background_audio_manager import BackgroundAudioManager
from conv.features.dtmf_receiver import DTMFReceiver
from factory.avatar_factory import create_avatar_session
from factory.stt_factory import create_stt
from factory.tts_factory import create_tts
from factory.vad_profiles import get_vad_profile
from log import conv_log_svc
from log.conv_log_svc import ConversationLogger
from log.advagency_logger import get_conversation_logger
from log.sentry_decorators import sentry_span
from telemetry_config import setup_telemetry, get_tracer
from metrics_collector import get_metrics_collector


def create_event():
    try:
        return ProcessEvent()
    except ImportError:
        return ThreadEvent()

_config = get_config()
_logger = get_conversation_logger()

# Get tracer instance
_tracer = get_tracer(__name__)

# Get metrics collector instance
_metrics_collector = get_metrics_collector()

@sentry_span(op="room.build_ses_opts", description="build session options")
def build_conv_meta(room: rtc.Room, metadata: str = ''):
    _metadata = metadata if metadata else (room.metadata if len(room.metadata) > 0 else "")
    _logger.debug(f"Using metadata: {_metadata}")

    room_meta = RoomConfig()
    if _metadata:
        try:
            room_meta = RoomConfig().load_json(_metadata)
        except json.JSONDecodeError as e:
            _logger.warning(f"Failed to parse metadata as JSON: {e}")
        except Exception as e:
            _logger.warning(f"Error processing metadata: {e}")

    return room_meta

class ConvManager:
    _tasks = set[asyncio.Task[Any]]()
    _lock = asyncio.Lock()
    meta_updated = create_event()
    conversation_ended = create_event()
    conversation_finalizing = create_event()
    _conversation_logger: ConversationLogger = ConversationLogger()
    _api_client: conv_log_svc = None
    conv_meta: RoomConfig = None
    _raw_metadata: str = None  # Store the raw metadata string

    ctx: JobContext = None
    room: rtc.Room = None

    agent: JanaAgent = None
    avatar_session = None  # Avatar session (Anam/Tavus/etc)
    avatar_provider = None  # Avatar provider instance
    user_away_timeout = _config.app.silence_threshold  # Duration in seconds
    is_user_active = False
    langfuse_client = langfuse.Langfuse(
        public_key=_config.langfuse.public_key,
        secret_key=_config.langfuse.secret_key,
        host=_config.langfuse.host,
    )

    def update_metadata(self, metadata: str):
        self._raw_metadata = metadata  # Store the raw metadata string
        self.conv_meta = build_conv_meta(self.room, metadata)
        self._api_client = ConvSvc(conversation_type=self.conv_meta.type)
        conversation_id = self.conv_meta.context.conversationId
        _logger.debug(f"Successfully created ConvManager with conversation_id: {conversation_id}")
        self.meta_updated.set()

    def get_metadata(self) -> str:
        return self._raw_metadata

    @sentry_span(op="manager", description="wake up")
    def __init__(self, ctx: JobContext,  session_id: str = str(uuid.uuid4())):
        with _tracer.start_as_current_span("conv_manager.init") as span:
            span.set_attribute("room_id", ctx.room.name)
            span.set_attribute("session_id", session_id)
            
            self.callee_is_idle = create_event()
            self.sentiment_analyser = None
            self.agent_settings = None
            self.session: AgentSession = None
            self.analyser = None
            self.last_activity = None
            self._is_welcome_played = False
            self.ctx = ctx
            self.room = ctx.room
            # Create and use background audio player
           
            self.background_audio_manager = BackgroundAudioManager()
            self.dtmf_receiver: DTMFReceiver = None
            self.collected_initial_digits: str = None
            self.conversation_finalizing = create_event()
            self.conversation_ended = create_event()
            self.participant_is_joined = create_event()
            self.idle_counter = 0
            self._shutting_down = False
            
            # Record session start for metrics
            _metrics_collector.record_session_start()

    def send_metrics_to_sentry(self, usageSummary: any):
        metric_name = "voice services summary"
        _logger.info(f"metrics:{metric_name} {usageSummary}")

        span = sentry_sdk.get_current_span()
        if span:
            span.set_data(metric_name, usageSummary)

    async def say_welcome(self):
        if self._is_welcome_played:
            return
        self._is_welcome_played = True
        await self.session.generate_reply(
            instructions='''One short sentence.
                firstly make sure it's [Client Name] to the user using common friendly but formal phrase
                like Hello do i speak to [Client Name]?
                and wait answer
            ''',
            allow_interruptions=False
        )
        self.add_task(self._collect_initial_digits())

    @sentry_span(op="agent.setup", description="setup features")
    def start_conversation_workflow(self, usage_collector):
        log_conversation = self._conversation_logger.log_conversation
        config = get_config()

        company_name = self.conv_meta.context.companyName
        analyser_config = AnalyserConfig(prompt=self.conv_meta.analysis.prompt)
        self.analyser = create_analyser(
            core_api_client=self._api_client,
            company_name=company_name,
            config=analyser_config,
            usage_collector=usage_collector
        )

        @self.session.on("agent_state_changed")
        def on_agent_state_changed(event: AgentStateChangedEvent):
            if event.old_state == "speaking" and event.new_state == "listening":
                if self.callee_is_idle.is_set():
                    self.session._set_user_away_timer()
                    self.session._user_state = 'listening'

        @self.session.on("user_state_changed")
        def on_user_state_changed(event: UserStateChangedEvent):
            if event.new_state == "speaking":
                self.idle_counter = 0
                self.callee_is_idle.clear()
                if not self._is_welcome_played:
                    # greet ONLY after we have real user speech
                    self.add_task(self.say_welcome())
            elif event.new_state == "away":
                self.idle_counter += 1
                _logger.info(f"user_state_changed: User away for {self.user_away_timeout}s - ending conversation immediately.")
                self.set_end_conversation()


        @self.session.on("conversation_item_added")
        def on_conversation_item_added(event: ConversationItemAddedEvent):
            for content in event.item.content:
                if isinstance(content, str):
                    print(f" - text: {content}")
                    # Record message metrics
                    _metrics_collector.record_message(self.ctx.room.name, event.item.role)
                    
                    self.add_task(log_conversation(
                        agent=self.conv_meta.llm.name,
                        participant=self.conv_meta.context.participant,
                        message=content,
                        conversation_id=self.ctx.room.name,
                        role=event.item.role
                    ))
                    if event.item.role == "user" and not self._is_welcome_played:
                    # Trigger welcome message on first user speech
                        self.add_task(self.say_welcome())

        conversation_id = self.conv_meta.context.conversationId
        conversation_type = self.conv_meta.type
        company_id = self.conv_meta.context.companyId

    def _create_tts(self, agent_settings: AgentSettings):
        provider = agent_settings.voiceSettings.provider
        return create_tts(provider=provider, agent_settings=agent_settings)

    @sentry_span("agent.init", "init agent")
    async def start_conversation(self, usage_collector):
        async with _metrics_collector.track_request("start_conversation"):
            with _tracer.start_as_current_span("conv_manager.start_conversation") as span:
                span.set_attribute("room_id", self.ctx.room.name)
                span.set_attribute("participant", self.conv_meta.context.participant)
                span.set_attribute("agent_name", self.conv_meta.llm.name)
                
                # Start conversation metrics tracking
                participant_count = len(self.room.remote_participants) + 1  # +1 for local participant
                _metrics_collector.start_conversation(self.ctx.room.name, participant_count)
                _metrics_collector.record_room_event("conversation_started", self.ctx.room.name)
                
                response = await self._conversation_logger.create_conversation(room_id=self.ctx.room.name,
                                                                               participant=self.conv_meta.context.participant, agent_name=self.conv_meta.llm.name)
                if response is None:
                    _logger.error(f"Failed to create conversation for room_id: {self.ctx.room.name}")
                    span.set_attribute("conversation_creation_failed", True)
                    _metrics_collector.record_error(self.ctx.room.name, "conversation_creation_failed", "Failed to create conversation")
                    # Optionally handle failure (e.g., raise exception or retry)

            agent_config = get_agent_info_from_meta(self.conv_meta)
            self.agent_settings = AgentSettings(
                agent_info=agent_config,
                profile_prompt=self.conv_meta.llm.profilePrompt,
                languages=self.conv_meta.llm.language
            )

            vad_profile = get_vad_profile(self.agent_settings.voiceSettings.vad_profile)
            vad = silero.VAD.load(**vad_profile.to_dict())
            #llm_model =  self.conv_meta.llm.model
            llm_model = 'meta-llama/llama-4-maverick-17b-128e-instruct'
            span.set_attribute("llm_model", llm_model)
            span.set_attribute("language", self.conv_meta.llm.language)
            
            self.session = AgentSession(
                stt=create_stt(language=self.conv_meta.llm.language),
                llm=groq.LLM(model=llm_model, temperature=_config.openai.temperature),
                tts=self._create_tts(self.agent_settings),
                vad=vad,
                turn_detection=EnglishModel(),
                user_away_timeout=self.user_away_timeout,
                preemptive_generation=True,
                min_interruption_duration=1
            )
            self.start_conversation_workflow(usage_collector=usage_collector)
            
            self.agent = JanaAgent(
                opts=self.agent_settings,
                conv_id=self.conv_meta.context.conversationId,
                company_id=self.conv_meta.context.companyId,
                timezone=self.conv_meta.context.timezone,
                analyser=self.analyser,
                api_client=self._api_client,
                room=self.room,
            )
            await self.agent.initialize()

            # Start background audio if provided
            if self.background_audio_manager.has_background_audio():
                try:
                    with _tracer.start_as_current_span("conv_manager.start_background_audio"):
                        await self.background_audio_manager.start(room=self.room, agent_session=self.session)
                        span.set_attribute("background_audio_started", True)
                except Exception as e:
                    _logger.error(f"Failed to start background audio: {e}")
                    span.set_attribute("background_audio_failed", True)
                    # Continue without background audio if it fails to start

            # Initialize DTMF receiver
            with _tracer.start_as_current_span("conv_manager.init_dtmf"):
                self.dtmf_receiver = DTMFReceiver(room=self.room, session=self.session)
                #await self.dtmf_receiver.start()
            

            # Initialize video avatar if enabled
            if self.conv_meta.video_enabled:
                span.set_attribute("video_avatar_enabled", True)
                await self._initialize_avatar()

            _logger.debug(f"start_conversation: agent initialized")
            await self.agent.update_agent_attributes({"agent.status": "ready"})
            _logger.info(
                f"agent {self.agent.agent_settings.config.name}: {self.agent.agent_settings.config.id} started listening in room"
            )
            span.set_attribute("conversation_started", True)
            return self.agent

    async def register_last_message(self):
        if self.conversation_ended.is_set() or self.conversation_finalizing.is_set():
            return
        await asyncio.sleep(0.1)

        @self.agent.on("agent_speech_committed")
        def on_agent_stopped_speaking(event):
            _logger.info('last message played, ending conversation.')
            self.set_end_conversation()

    async def say_last_message(self, message=None):
        await self.register_last_message()
        await self.session.say(message or self.agent.agent_settings.get_ending_message())

    def set_end_conversation(self):
        with _tracer.start_as_current_span("conv_manager.end_conversation") as span:
            span.set_attribute("room_id", self.ctx.room.name)
            
            self.conversation_ended.set()
            self.conversation_finalizing.set()
            
            # Properly close background audio
            if self.background_audio_manager.has_background_audio() and self.background_audio_manager.is_started() and not self.background_audio_manager.is_closed():
                self.add_task(self.background_audio_manager.cleanup())
                _logger.info("Background audio cleanup initiated")
                span.set_attribute("background_audio_cleanup", True)
            # Cleanup avatar session if it exists
            if self.avatar_session:
                self.add_task(self._cleanup_avatar())
                _logger.info("Avatar cleanup initiated")
                span.set_attribute("avatar_cleanup", True)
            # Cleanup DTMF receiver if it exists
            if self.dtmf_receiver and self.dtmf_receiver.is_active:
                self.add_task(self.dtmf_receiver.stop())
                _logger.info("DTMF receiver cleanup initiated")
                span.set_attribute("dtmf_cleanup", True)
            self.add_task(self.agent.finish_call(self.session))
            span.set_attribute("conversation_ended", True)

    async def _initialize_avatar(self):
        """Initialize avatar session using factory"""
        try:
            # Get avatar configuration from metadata or use default
            avatar_config = self.conv_meta.avatar_config or {}
            
            # Set default values if not specified
            if "name" not in avatar_config:
                avatar_config["name"] = self.agent_settings.config.name
            
            # Create avatar session using factory
            session_result = await create_avatar_session(avatar_config)
            if session_result[0] is None:
                _logger.warning("No available avatar providers found, continuing without video avatar")
                return
            
            self.avatar_session, self.avatar_provider = session_result
            
            # Start the avatar session
            await self.avatar_provider.start(self.avatar_session, self.session, self.room)
            provider_name = avatar_config.get("provider", "anam")
            _logger.info(f"Avatar initialized using {provider_name} provider")
            
        except Exception as e:
            _logger.error(f"Failed to initialize avatar: {e}")
            self.avatar_session = None
            self.avatar_provider = None
            # Continue without avatar rather than failing the entire conversation

    async def _cleanup_avatar(self):
        """Properly cleanup avatar session"""
        if self.avatar_session and self.avatar_provider:
            try:
                await self.avatar_provider.close(self.avatar_session)
                _logger.info("Avatar session closed successfully")
            except Exception as e:
                _logger.warning(f"Error closing avatar session: {e}")
            finally:
                self.avatar_session = None
                self.avatar_provider = None

    async def _collect_initial_digits(self):
        """Collect 3 digits at the beginning of the conversation"""
        with _tracer.start_as_current_span("conv_manager.collect_digits") as span:
            span.set_attribute("room_id", self.ctx.room.name)
            span.set_attribute("max_digits", 3)
            span.set_attribute("timeout", 30.0)
            
            try:
                _logger.info("Starting to collect 3 initial digits")
                
                # Prompt user for digits
                await self.session.say("Please enter your 3-digit code using your keypad.")
                
                # Collect 3 digits with 30 second timeout, no terminator needed
                collected_digits = await self.dtmf_receiver.collect_digits(
                    max_digits=3, 
                    timeout=30.0, 
                    terminator=""  # No terminator needed, stop at 3 digits
                )
                
                if collected_digits and len(collected_digits) == 3:
                    _logger.info(f"Successfully collected 3 digits: {collected_digits}")
                    span.set_attribute("digits_collected", True)
                    span.set_attribute("digits_count", len(collected_digits))
                    await self._handle_collected_digits(collected_digits)
                else:
                    _logger.warning(f"Failed to collect 3 digits. Got: {collected_digits}")
                    span.set_attribute("digits_collected", False)
                    span.set_attribute("digits_count", len(collected_digits) if collected_digits else 0)
                    await self.session.say("I didn't receive your 3-digit code. Let's continue with the conversation.")
                    
            except Exception as e:
                _logger.error(f"Error collecting initial digits: {e}")
                span.set_attribute("error", str(e))
                await self.session.say("There was an issue with digit collection. Let's continue with the conversation.")

    async def _handle_collected_digits(self, digits: str):
        """Handle the collected 3 digits"""
        try:
            _logger.info(f"Processing collected digits: {digits}")
            
            # You can add your logic here to handle the digits
            # For example, validate against a database, use for routing, etc.
            
            # For now, just acknowledge receipt
            await self.session.say(f"Thank you. I received your code {digits}. Let's continue with our conversation.")
            
            # Store digits for later use if needed
            self.collected_initial_digits = digits
            
        except Exception as e:
            _logger.error(f"Error handling collected digits: {e}")
            await self.session.say("Code received. Let's continue with our conversation.")


    def add_task(self, task):
        asyncio.create_task(self._add_task_async(task))

    async def _add_task_async(self, task):
        if not asyncio.iscoroutine(task):
            raise ValueError("Task must be an awaitable coroutine")
        async_task = asyncio.create_task(task)
        async with self._lock:
            self._tasks.add(async_task)
        return async_task