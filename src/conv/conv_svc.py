import asyncio
import base64
import json
import logging
from dataclasses import dataclass
from typing import Dict, List, Any, Optional

import aiohttp
import sentry_sdk
import yaml
from livekit.agents import AgentSession

from app.base_svcs import get_now
from app.config import get_config
from app.sentry_config import capture_errors
from log.sentry_decorators import sentry_span

_logger = logging.getLogger(__name__)
_logger.propagate = False


@dataclass
class Voice:
    id: Optional[str] = None
    voiceId: Optional[str] = None
    name: Optional[str] = None
    previewUrl: Optional[str] = None
    gender: Optional[str] = None
    accent: Optional[str] = None
    voiceSettings: Optional[str] = None
    createdAt: Optional[str] = None
    updatedAt: Optional[str] = None


@dataclass
class Mission:
    id: Optional[str] = None
    humanName: Optional[str] = None
    intro: Optional[str] = None
    goal: Optional[str] = None
    offerDetails: Optional[str] = None
    farewell: Optional[str] = None
    createdAt: Optional[str] = None
    updatedAt: Optional[str] = None

    def get_prompt(self) -> str:
        intro = f"{self.intro} Your name is {self.humanName}" if self.intro else f"Your name is {self.humanName}"
        goal = ""  # "{human_name} want to peace in the world"
        parts = [intro, goal, self.offerDetails, self.farewell]
        return f"\n .".join(filter(None, parts)).replace("{human_name}", self.humanName)


@dataclass
class AgentInfo:
    id: Optional[str] = None
    name: Optional[str] = None
    model: Optional[str] = None
    voiceId: Optional[str] = None
    companyId: Optional[str] = None
    missionId: Optional[str] = None
    langfusePromptId: Optional[str] = None
    createdAt: Optional[str] = None
    updatedAt: Optional[str] = None
    voice: Optional[Voice] = None
    mission: Optional[Mission] = None


class ConvSvc:
    def __init__(self, conversation_type: str = 'outbound-campaign-call'):
        api_conf = get_config().core_api
        self.base_url = api_conf.url
        auth = base64.b64encode(
            f"{api_conf.login}:{api_conf.password}".encode()
        ).decode()
        self.headers = {
            "Authorization": f"token {api_conf.login}:{api_conf.password}",
            "Content-Type": "application/json",
            "conversation-type": conversation_type,
        }

    @capture_errors
    @sentry_span(op="conv.update", description="update conversation data")
    async def update_recipient_conversation(
            self, conversation_id: str,
            data: Dict[str, Any]
    ) -> Dict[str, Any]:
        current_span = sentry_sdk.Hub.current.scope.span
        try:
            _logger.debug(f"no dev server")
            # async with aiohttp.ClientSession(headers=self.headers) as session:
            #     url = f"{self.base_url}/v2/system/recipient-conversation/{conversation_id}"
            #     # url = f"{self.base_url}/v1/recipient-conversation/{conversation_id}"
            #     _logger.debug(f"PATCH Request to URL: {url} with Data: {data}")
            #
            #     async with session.patch(url, json=data) as response:
            #         current_span = sentry_sdk.Hub.current.scope.span
            #         if current_span:
            #             current_span.set_data("http.status_code", response.status)
            #
            #         response.raise_for_status()
            #         result = await response.json()
            #
            #         # Optionally sanitize the response before logging
            #         sanitized_result = self._sanitize_response(result)
            #         if current_span:
            #             current_span.set_data("response_body", sanitized_result)
            #
            #         _logger.info(f"PATCH {url} - {data} = {result}")
            #         return result
        except aiohttp.ClientError as http_err:
            _logger.error(f"HTTP error occurred while updating conversation: {http_err}")
            sentry_sdk.capture_exception(http_err)
            if current_span:
                current_span.set_status("error")
            raise
        except Exception as e:
            _logger.exception(f"Unexpected error occurred while updating conversation: {e}")
            sentry_sdk.capture_exception(e)
            if current_span:
                current_span.set_status("error")

    @capture_errors
    @sentry_span(op="conv.sentiment.add", description="add sentiment marks")
    async def add_sentiment(
            self, conversation_id: str,
            sentiment_scores: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        current_span = sentry_sdk.Hub.current.scope.span
        data = {
            "sentimentScores": [
                {
                    "label": score["sentiment"],
                    "score": score["sentimentScore"]
                }
                for score in sentiment_scores
            ],
            "createdAt": get_now(),
        }
        try:
            _logger.debug(f"no dev server")
            # async with aiohttp.ClientSession(headers=self.headers) as session:
            #     # url = f"{self.base_url}/v1/system/recipient-conversation/{conversation_id}/add-sentiment"
            #     url = f"{self.base_url}/v2/system/conversation/{conversation_id}/sentiment"
            #     _logger.debug(f"POST Request to URL: {url} with Data: {data}")
            #
            #     async with session.post(url=url, json=data) as response:
            #         # Access the current span from Sentry
            #         current_span = sentry_sdk.Hub.current.scope.span
            #         if current_span:
            #             current_span.set_data("http.status_code", response.status)
            #
            #         response.raise_for_status()
            #         result = await response.json()
            #
            #         # Optionally sanitize the response before logging
            #         sanitized_result = self._sanitize_response(result)
            #         if current_span:
            #             current_span.set_data("response_body", sanitized_result)
            #
            #         _logger.info(f"POST {url} - {data} = {result}")
            #         return result
        except aiohttp.ClientError as http_err:
            _logger.error(f"HTTP error occurred while adding sentiment: {http_err}")
            sentry_sdk.capture_exception(http_err)
            if current_span:
                current_span.set_status("error")
            # raise
        except Exception as e:
            _logger.exception(f"Unexpected error occurred while adding sentiment: {e}")
            sentry_sdk.capture_exception(e)
            if current_span:
                current_span.set_status("error")

    @capture_errors
    @sentry_span(op="conv.schedule_callback", description="schedule callback")
    async def request_callback(
            self,
            conversation_id: str,
            callback_time: str,
    ) -> Dict[str, Any]:
        data = {
            "interactionResult": "callback_requested",
            "callbackAt": callback_time
        }
        try:
            result = await self.update_recipient_conversation(conversation_id=conversation_id, data=data)
            _logger.info(f"Callback requested for conversation_id: {conversation_id} at {callback_time}")
            return {'success': True}
        except Exception as e:
            _logger.exception(f"Error requesting callback: {e}")
            sentry_sdk.capture_exception(e)
            return None

    @capture_errors
    @sentry_span(op="conv.set_donotcall", description="set do-not-call")
    async def set_do_not_call(self, conversation_id: str) -> None:
        data = {
            "interactionResult": "do_not_call"
        }
        try:
            await self.update_recipient_conversation(conversation_id=conversation_id, data=data)
            _logger.info(f"Set do_not_call for conversation_id: {conversation_id}")
        except Exception as e:
            _logger.exception(f"Error setting do_not_call: {e}")
            sentry_sdk.capture_exception(e)

    @capture_errors
    @sentry_span(op="conv.send_followup", description="request follow-up")
    async def request_follow_up(
            self,
            conversation_id: str,
    ) -> Dict[str, Any]:
        data = {
            "interactionResult": "follow_up_required"
        }
        try:
            result = await self.update_recipient_conversation(conversation_id=conversation_id, data=data)
            _logger.info(f"Follow-up requested for conversation_id: {conversation_id}")
            return {"success": True}
        except Exception as e:
            _logger.exception(f"Error requesting follow-up: {e}")
            sentry_sdk.capture_exception(e)

    async def create_usage_summary(self, room_id: str, metric, session: AgentSession):
        """
        Create a Usage Summary linked to Room=room_id exactly once.
        Requires autoname = 'field:room' (so docname == room_id) and `room` unique in the DocType.
        Never updates; if it exists, returns the existing doc.
        """
        payload = {
            "room_id": room_id,
            "table_usage_summary": [
                {
                    "provider": f"{session.llm.label.split('.')[2]}.prompt",
                    "type": session.llm.label.split(".")[4],
                    "token_quantity": float(getattr(metric, "llm_prompt_tokens", 0) or 0),
                },
                {
                    "provider": f"{session.llm.label.split('.')[2]}.prompt_cached",
                    "type": session.llm.label.split(".")[4],
                    "token_quantity": float(getattr(metric, "llm_prompt_cached_tokens", 0) or 0),
                },
                {
                    "provider": f"{session.llm.label.split('.')[2]}.completion",
                    "type": session.llm.label.split(".")[4],
                    "token_quantity": float(getattr(metric, "llm_completion_tokens", 0) or 0),
                },
                {
                    "provider": session.stt.label.split(".")[2],
                    "type": session.stt.label.split(".")[4],
                    "token_quantity": float(getattr(metric, "stt_audio_duration", 0) or 0),
                },
                {
                    "provider": session.tts.label.split(".")[2],
                    "type": session.tts.label.split(".")[4],
                    "token_quantity": float(getattr(metric, "tts_audio_duration", 0) or 0),
                }
            ]
            # "llm_prompt_tokens": float(getattr(metric, "llm_prompt_tokens", 0) or 0),
            # "llm_prompt_cached_tokens": float(getattr(metric, "llm_prompt_cached_tokens", 0) or 0),
            # "llm_completion_tokens": float(getattr(metric, "llm_completion_tokens", 0) or 0),
            # "tts_characters_count": float(getattr(metric, "tts_characters_count", 0) or 0),
            # "stt_audio_duration": float(getattr(metric, "stt_audio_duration", 0) or 0),
        }

        get_url = f"{self.base_url}/api/resource/Usage%20Summary/{room_id}"
        post_url = f"{self.base_url}/api/resource/Usage%20Summary"

        max_retries = 3
        for attempt in range(1, max_retries + 1):
            try:
                async with aiohttp.ClientSession(headers=self.headers) as session:
                    # 1) Already created?
                    async with session.get(get_url) as resp:
                        if resp.status == 200:
                            data = await resp.json()
                            _logger.info(f"Usage Summary already exists for room {room_id}: {room_id}")
                            return data
                        elif resp.status not in (404, 400):
                            # Unexpected GET error -> raise
                            text = await resp.text()
                            raise aiohttp.ClientResponseError(
                                request_info=resp.request_info,
                                history=resp.history,
                                status=resp.status,
                                message=text,
                            )

                    # 2) Create it
                    async with session.post(post_url, json=payload) as resp:
                        if resp.status in (409, 417):
                            # Frappe may return 417 “Duplicate Entry” (or 409) if another process just created it.
                            # Treat as success; fetch and return the existing doc.
                            text = await resp.text()
                            _logger.warning(
                                f"Usage Summary POST conflict for room {room_id}: {text}. Treating as created.")
                            async with session.get(get_url) as r2:
                                r2.raise_for_status()
                                data = await r2.json()
                                return data

                        resp.raise_for_status()
                        data = await resp.json()
                        _logger.info(f"Created Usage Summary for room {room_id}")
                        return data

            except aiohttp.ClientError as http_err:
                if attempt == max_retries:
                    _logger.error(f"Failed creating Usage Summary after {attempt} attempts: {http_err}")
                    sentry_sdk.capture_exception(http_err)
                    break
                _logger.warning(f"Retry {attempt}/{max_retries} creating Usage Summary: {http_err}")
                await asyncio.sleep(1)
            except Exception as e:
                _logger.exception(f"Unexpected error creating Usage Summary: {e}")
                sentry_sdk.capture_exception(e)
                break

        return None

    @capture_errors
    @sentry_span(op="conv.save_result", description="save conversation result")
    async def save_conversation_result(
            self,
            conversation_id: str,
            result,
            room_id,
            usage_collector,
            session: AgentSession,
            is_final: bool = False
    ):
        metric = usage_collector.get_summary()

        # --- make subfields proper objects for nicer YAML ---
        def _maybe_parse_json(v):
            if isinstance(v, str):
                try:
                    return json.loads(v)  # parse JSON string to object
                except Exception:
                    return v
            return v

        # normalize metrics to plain data (handles pydantic/dataclasses)
        def _json_default(o):
            if hasattr(o, "model_dump"):
                return o.model_dump()
            if hasattr(o, "__dict__"):
                return o.__dict__
            return str(o)

        metrics_plain = json.loads(json.dumps(metric, default=_json_default))

        # build a clean copy for YAML with outcome first
        result_for_yaml = {}
        if 'outcome' in result:
            result_for_yaml['outcome'] = _maybe_parse_json(result['outcome'])
        
        for k, v in result.items():
            if k != 'outcome':
                result_for_yaml[k] = _maybe_parse_json(v)
        #result_for_yaml["metrics"] = metrics_plain

        summary_yaml = yaml.safe_dump(
            result_for_yaml, sort_keys=False, default_flow_style=False, allow_unicode=True
        )

        data = {
            "summary": summary_yaml,  # <- YAML blob
            "duration": str(getattr(metric, "stt_audio_duration", "")),
            # "isFinal": is_final
        }
        await self.create_usage_summary(
            room_id=room_id,
            metric=metric,
            session=session
        )

        current_span = sentry_sdk.Hub.current.scope.span
        max_retries = 3
        retry_count = 0

        while retry_count <= max_retries:
            try:
                retry_count = max_retries + 1
                _logger.debug(f"no dev server")
                async with aiohttp.ClientSession(headers=self.headers) as session:
                    url = f"{self.base_url}/api/resource/Room/{room_id}"
                    _logger.debug(f"POST Request to URL: {url} with Data: {data}")

                    async with session.put(url, json=data) as response:
                        if current_span:
                            current_span.set_data("http.status_code", response.status)

                        response.raise_for_status()
                        result = await response.json()

                        sanitized_result = self._sanitize_response(result)
                        if current_span:
                            current_span.set_data("response_body", sanitized_result)

                        _logger.info(f"POST {url} - {data} = {sanitized_result}")
                return ""

            except aiohttp.ClientError as http_err:
                retry_count += 1
                if retry_count > max_retries:
                    _logger.error(
                        f"HTTP error occurred while saving conversation result after {max_retries} retries: {http_err}")
                    sentry_sdk.capture_exception(http_err)
                    if current_span:
                        current_span.set_status("error")
                    # raise
                else:
                    _logger.warning(
                        f"HTTP error occurred while saving conversation result. Retry attempt {retry_count}/{max_retries}: {http_err}")
                    await asyncio.sleep(1)  # Wait 1 second before retrying
            except Exception as e:
                _logger.exception(f"Unexpected error occurred while saving conversation result: {e}")
                sentry_sdk.capture_exception(e)
                if current_span:
                    current_span.set_status("error")
                # raise
                break  # Don't retry on non-HTTP errors

    def _sanitize_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """
        Sanitizes the response by masking or removing sensitive information.

        Args:
            response (Dict[str, Any]): The original response data.

        Returns:
            Dict[str, Any]: The sanitized response data.
        """
        # Implement sanitization logic as needed
        sensitive_fields = ["auth_token"]
        sanitized = response.copy()
        for field in sensitive_fields:
            if field in sanitized:
                sanitized[field] = "MASKED"
        return sanitized
