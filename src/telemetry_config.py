import os
import logging
import threading
from opentelemetry import trace, metrics, _logs
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk.metrics import MeterProvider
from opentelemetry.sdk.metrics.export import PeriodicExportingMetricReader
from opentelemetry.exporter.otlp.proto.grpc.metric_exporter import OTLPMetricExporter
from opentelemetry.sdk._logs import LoggerProvider, LoggingHandler
from opentelemetry.sdk._logs.export import BatchLogRecordProcessor
from opentelemetry.exporter.otlp.proto.grpc._log_exporter import OTLPLogExporter
from opentelemetry.sdk.resources import Resource




from log.advagency_logger import get_telemetry_logger

_logger = get_telemetry_logger()
_initialized = False
_setup_attempted = False
_setup_lock = threading.RLock()

# Global flag to track if logging handlers have been set up
_logging_handlers_setup = False

def _setup_logging_export():
    """Set up OpenTelemetry logging export to SigNoz (backend only, no console interference)"""
    global _logging_handlers_setup
    with _setup_lock:
        # Check if logging handlers have already been set up globally
        if _logging_handlers_setup:
            _logger.debug("Logging handlers already set up globally, skipping")
            return

        try:
            # Try multiple environment variable names for endpoint
            signoz_endpoint = (
                os.getenv("SIGNOZ_ENDPOINT") or
                os.getenv("OTEL_EXPORTER_OTLP_ENDPOINT") or
                "https://ingest.us.signoz.cloud:443"
            )

            # Try multiple environment variable names for auth token
            auth_token = (
                os.getenv("SIGNOZ_INGESTION_KEY") or
                os.getenv("SIGNOZ_ACCESS_TOKEN") or
                "c80552d9-8531-4668-ac8a-eeb2622fe5f1"
            )

            # Parse headers from OTEL_EXPORTER_OTLP_HEADERS if available
            headers = {}
            otlp_headers = os.getenv("OTEL_EXPORTER_OTLP_HEADERS")
            if otlp_headers:
                for header in otlp_headers.split(","):
                    if "=" in header:
                        key, value = header.split("=", 1)
                        headers[key.strip()] = value.strip()

            # Ensure we have the signoz access token
            if "signoz-access-token" not in headers:
                headers["signoz-access-token"] = auth_token

            # Check if we're in instrumented mode (have valid SigNoz config)
            # For now, always consider it instrumented if we have endpoint and token
            is_instrumented = bool(signoz_endpoint and auth_token)

            # Only set environment to 'local' if explicitly disabled or no config at all
            if not signoz_endpoint or not auth_token:
                _logger.info("No SigNoz configuration found for logging - setting environment to 'local'")
                os.environ["OTEL_DEPLOYMENT_ENVIRONMENT"] = "local"
                os.environ["ENV"] = "local"
            else:
                _logger.info(f"SigNoz logging configuration found - using endpoint: {signoz_endpoint}")
                # Keep the environment as specified in the configuration
                if not os.getenv("OTEL_DEPLOYMENT_ENVIRONMENT"):
                    os.environ["OTEL_DEPLOYMENT_ENVIRONMENT"] = "production"

            # Check if LoggerProvider is already set (by auto-instrumentation)
            current_logger_provider = _logs.get_logger_provider()
            if hasattr(current_logger_provider, 'resource'):
                _logger.info("LoggerProvider already set by auto-instrumentation, using existing one")

                # Ensure we don't add duplicate log processors
                existing_processors = list(getattr(current_logger_provider, "_processors", []))
                has_batch_log_processor = any(isinstance(p, BatchLogRecordProcessor) for p in existing_processors)
                if not has_batch_log_processor:
                    try:
                        otlp_log_exporter = OTLPLogExporter(
                            endpoint=signoz_endpoint,
                            headers=headers
                        )
                        current_logger_provider.add_log_record_processor(
                            BatchLogRecordProcessor(otlp_log_exporter)
                        )
                        _logger.info("Attached BatchLogRecordProcessor to existing LoggerProvider")
                    except Exception as e:
                        _logger.warning(f"Could not attach BatchLogRecordProcessor to existing provider: {e}")

                # Check if we already have an OpenTelemetry handler to avoid duplicates
                root_logger = logging.getLogger()
                has_otel_handler = any(isinstance(h, LoggingHandler) for h in root_logger.handlers)

                if not has_otel_handler:
                    handler = LoggingHandler(level=logging.NOTSET, logger_provider=current_logger_provider)
                    root_logger.addHandler(handler)
                    _logger.info("Added LoggingHandler to existing LoggerProvider")
                    _logger.info(f"Logging will be exported to: {signoz_endpoint}")
                    _logging_handlers_setup = True
                else:
                    _logger.debug("OpenTelemetry handler already exists, skipping duplicate")
                    _logging_handlers_setup = True
                return

            # Create resource
            resource = Resource.create({
                "service.name": os.getenv("OTEL_SERVICE_NAME", "advagency-conversation-manager"),
                "service.version": os.getenv("OTEL_SERVICE_VERSION", "1.0.0"),
                "deployment.environment": os.getenv("OTEL_DEPLOYMENT_ENVIRONMENT", os.getenv("ENV", "development"))
            })

            # Set up LoggerProvider manually
            logger_provider = LoggerProvider(resource=resource)
            _logs.set_logger_provider(logger_provider)

            # Create OTLP log exporter
            otlp_log_exporter = OTLPLogExporter(
                endpoint=signoz_endpoint,
                headers=headers
            )

            # Add log processor only once
            existing_processors = list(getattr(logger_provider, "_processors", []))
            has_batch_log_processor = any(
                isinstance(p, BatchLogRecordProcessor) for p in existing_processors
            )
            if not has_batch_log_processor:
                logger_provider.add_log_record_processor(
                    BatchLogRecordProcessor(otlp_log_exporter)
                )
            else:
                _logger.info("BatchLogRecordProcessor already attached, skipping duplicate")

            # Attach OTEL handler to Python's root logger
            handler = LoggingHandler(level=logging.NOTSET, logger_provider=logger_provider)

            # Check if we already have an OpenTelemetry handler to avoid duplicates
            root_logger = logging.getLogger()
            has_otel_handler = any(isinstance(h, LoggingHandler) for h in root_logger.handlers)

            if not has_otel_handler:
                root_logger.addHandler(handler)
                _logger.info("Logging export to SigNoz configured manually")
                _logger.info(f"Logging will be exported to: {signoz_endpoint}")
                _logging_handlers_setup = True
            else:
                _logger.debug("OpenTelemetry handler already exists, skipping duplicate")
                _logging_handlers_setup = True
        except Exception as e:
            _logger.warning(f"Could not set up logging export: {e}")

def setup_telemetry():
    """
    Configure OpenTelemetry to work with auto-instrumentation or manual setup.
    Automatically sets environment to 'local' when not in instrumented mode.
    """
    global _initialized, _setup_attempted
    with _setup_lock:
        if _setup_attempted:
            _logger.debug("Telemetry setup already attempted, skipping")
            return trace.get_tracer(__name__) if _initialized else None

        _setup_attempted = True

        try:
            # Check if we're running in a local/development environment without proper telemetry config
            signoz_endpoint = (
                os.getenv("SIGNOZ_ENDPOINT") or
                os.getenv("OTEL_EXPORTER_OTLP_ENDPOINT")
            )
            auth_token = (
                os.getenv("SIGNOZ_INGESTION_KEY") or
                os.getenv("SIGNOZ_ACCESS_TOKEN")
            )

            # If no telemetry config is provided at all, assume local development
            if not signoz_endpoint and not auth_token:
                _logger.info("No telemetry configuration found - setting environment to 'local'")
                os.environ["OTEL_DEPLOYMENT_ENVIRONMENT"] = "local"
                os.environ["ENV"] = "local"
                os.environ["OTEL_SDK_DISABLED"] = "true"  # Disable telemetry for local dev
                _initialized = True
                return trace.get_tracer(__name__)
            # Check if telemetry is disabled
            if os.getenv("OTEL_SDK_DISABLED", "false").lower() == "true":
                _logger.info("OpenTelemetry is disabled via OTEL_SDK_DISABLED - setting environment to 'local'")
                # Set environment to 'local' when telemetry is disabled
                os.environ["OTEL_DEPLOYMENT_ENVIRONMENT"] = "local"
                os.environ["ENV"] = "local"
                _initialized = True
                return trace.get_tracer(__name__)

            # Check if we're running under auto-instrumentation
            auto_instrumentation = os.getenv("OTEL_PYTHON_LOGGING_AUTO_INSTRUMENTATION_ENABLED", "false").lower() == "true"

            # For now, let's always use manual setup since auto-instrumentation is complex
            if False:  # auto_instrumentation:
                _logger.info("Auto-instrumentation detected, using existing providers")
                # When using auto-instrumentation, providers are already set up
                # Just verify they exist and log the configuration
                current_tracer_provider = trace.get_tracer_provider()
                current_meter_provider = metrics.get_meter_provider()

                if hasattr(current_tracer_provider, 'resource'):
                    _logger.info("TracerProvider configured by auto-instrumentation")
                if hasattr(current_meter_provider, 'resource'):
                    _logger.info("MeterProvider configured by auto-instrumentation")

                # Set up logging manually even with auto-instrumentation
                _setup_logging_export()

                _initialized = True
                _logger.info("Telemetry working with auto-instrumentation")
                return trace.get_tracer(__name__)

            # Manual setup (when not using auto-instrumentation)
            _logger.info("Setting up telemetry manually")

            # Get SigNoz endpoint from environment or use default
            signoz_endpoint = (
                os.getenv("SIGNOZ_ENDPOINT") or
                os.getenv("OTEL_EXPORTER_OTLP_ENDPOINT") or
                "https://ingest.us.signoz.cloud:443"
            )

            # Try multiple environment variable names for auth token
            auth_token = (
                os.getenv("SIGNOZ_INGESTION_KEY") or
                os.getenv("SIGNOZ_ACCESS_TOKEN") or
                "c80552d9-8531-4668-ac8a-eeb2622fe5f1"
            )

            # Parse headers from OTEL_EXPORTER_OTLP_HEADERS if available
            headers = {}
            otlp_headers = os.getenv("OTEL_EXPORTER_OTLP_HEADERS")
            if otlp_headers:
                for header in otlp_headers.split(","):
                    if "=" in header:
                        key, value = header.split("=", 1)
                        headers[key.strip()] = value.strip()

            # Ensure we have the signoz access token
            if "signoz-access-token" not in headers:
                headers["signoz-access-token"] = auth_token

            # Check if we're in instrumented mode (have valid SigNoz config)
            # For now, always consider it instrumented if we have endpoint and token
            is_instrumented = bool(signoz_endpoint and auth_token)

            # Only set environment to 'local' if explicitly disabled or no config at all
            if not signoz_endpoint or not auth_token:
                _logger.info("No SigNoz configuration found - setting environment to 'local'")
                os.environ["OTEL_DEPLOYMENT_ENVIRONMENT"] = "local"
                os.environ["ENV"] = "local"
            else:
                _logger.info(f"SigNoz configuration found - using endpoint: {signoz_endpoint}")
                # Keep the environment as specified in the configuration
                if not os.getenv("OTEL_DEPLOYMENT_ENVIRONMENT"):
                    os.environ["OTEL_DEPLOYMENT_ENVIRONMENT"] = "production"

            # Create resource with service information
            resource = Resource.create({
                "service.name": os.getenv("OTEL_SERVICE_NAME", "advagency-conversation-manager"),
                "service.version": os.getenv("OTEL_SERVICE_VERSION", "1.0.0"),
                "deployment.environment": os.getenv("OTEL_DEPLOYMENT_ENVIRONMENT", os.getenv("ENV", "development"))
            })

            # Only set tracer provider if not already set
            try:
                current_tracer_provider = trace.get_tracer_provider()
                if hasattr(current_tracer_provider, 'resource'):
                    _logger.info("TracerProvider already set, skipping initialization")
                else:
                    # Create tracer provider
                    tracer_provider = TracerProvider(resource=resource)

                    # Create SigNoz OTLP trace exporter
                    otlp_trace_exporter = OTLPSpanExporter(
                        endpoint=signoz_endpoint,
                        headers=headers
                    )

                    # Add span processor with optimized configuration (avoid duplicates)
                    existing_span_processors = []
                    active_proc = getattr(tracer_provider, "_active_span_processor", None)
                    if active_proc is not None:
                        existing_span_processors = list(getattr(active_proc, "_span_processors", []))
                    has_batch_span_processor = any(isinstance(p, BatchSpanProcessor) for p in existing_span_processors)
                    if not has_batch_span_processor:
                        span_processor = BatchSpanProcessor(
                            otlp_trace_exporter,
                            max_queue_size=2048,
                            max_export_batch_size=512,
                            export_timeout_millis=30000,
                            schedule_delay_millis=5000
                        )
                        tracer_provider.add_span_processor(span_processor)
                    else:
                        _logger.info("BatchSpanProcessor already attached, skipping duplicate")

                    # Set the tracer provider globally
                    trace.set_tracer_provider(tracer_provider)
                    _logger.info("TracerProvider set manually")
            except Exception as e:
                _logger.warning(f"Could not set TracerProvider: {e}")

            # Only set meter provider if not already set
            try:
                current_meter_provider = metrics.get_meter_provider()
                if hasattr(current_meter_provider, 'resource'):
                    _logger.info("MeterProvider already set, skipping initialization")
                else:
                    # Create SigNoz OTLP metrics exporter
                    otlp_metric_exporter = OTLPMetricExporter(
                        endpoint=signoz_endpoint,
                        headers=headers
                    )

                    # Reuse existing MeterProvider if present, otherwise create one.
                    existing_readers = []
                    try:
                        # try to peek at existing readers to avoid duplicates
                        existing_readers = list(getattr(current_meter_provider, "_metric_readers", []))
                    except Exception:
                        existing_readers = []

                    has_periodic_reader = any(isinstance(r, PeriodicExportingMetricReader) for r in existing_readers)
                    if not has_periodic_reader:
                        metric_reader = PeriodicExportingMetricReader(
                            exporter=otlp_metric_exporter,
                            export_interval_millis=30000  # Export every 30 seconds
                        )
                        meter_provider = MeterProvider(
                            resource=resource,
                            metric_readers=[metric_reader]
                        )
                        metrics.set_meter_provider(meter_provider)
                        _logger.info("MeterProvider set manually")
                    else:
                        _logger.info("PeriodicExportingMetricReader already attached, skipping duplicate")
            except Exception as e:
                _logger.warning(f"Could not set MeterProvider: {e}")

            # Set up logging export
            _setup_logging_export()

            _initialized = True
            _logger.info(f"Telemetry configured successfully. Endpoint: {signoz_endpoint}")

            return trace.get_tracer(__name__)

        except Exception as e:
            _logger.error(f"Failed to setup telemetry: {e}", exc_info=True)
            _initialized = True  # Mark as initialized to prevent retry loops
            # Return a no-op tracer if setup fails
            return trace.get_tracer(__name__)

def get_tracer(name: str = __name__):
    """Get a tracer instance"""
    if not _initialized:
        _logger.warning("Telemetry not initialized, calling setup_telemetry()")
        setup_telemetry()
    return trace.get_tracer(name)

def get_meter(name: str = __name__):
    """Get a meter instance for metrics"""
    if not _initialized:
        _logger.warning("Telemetry not initialized, calling setup_telemetry()")
        setup_telemetry()
    return metrics.get_meter(name)

def is_telemetry_enabled():
    """Check if telemetry is enabled"""
    return _initialized and os.getenv("OTEL_SDK_DISABLED", "false").lower() != "true"