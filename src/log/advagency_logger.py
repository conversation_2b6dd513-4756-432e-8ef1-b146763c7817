"""
AdvAgency Voice Agent Logging Configuration

This module provides unified logging configuration for the AdvAgency voice agent application.
All application logs use the 'advagency.voiceagent' namespace with LiveKit-compatible formatting.
"""

import logging
import sys
from typing import Dict, Optional


class AdvAgencyFormatter(logging.Formatter):
    """
    Custom formatter that matches LiveKit's colored output format exactly.
    
    Format: timestamp - [COLOR]LEVEL[RESET] logger.name - message [DIM]extra_data[RESET]
    """
    
    def __init__(self):
        super().__init__()
        # ANSI color codes matching LiveKit's style
        self._colors = {
            'DEBUG': '\033[36m',      # cyan
            'INFO': '\033[32m',       # green
            'WARNING': '\033[33m',    # yellow
            'ERROR': '\033[31m',      # red
            'CRITICAL': '\033[1;31m', # bold red
        }
        self._reset = '\033[0m'       # reset
        self._dim = '\033[90m'        # dim/gray for extra data
        
    def format(self, record):
        """Format log record to match LiveKit's style exactly"""
        # Get color for log level
        color = self._colors.get(record.levelname, '')
        
        # Format timestamp (matches LiveKit's format)
        timestamp = self.formatTime(record, '%Y-%m-%d %H:%M:%S,%f')[:-3]  # Remove last 3 digits from microseconds
        
        # Format the main message
        formatted_msg = f"{timestamp} - {color}{record.levelname}{self._reset} {record.name} - {record.getMessage()}"
        
        # Add any extra data in dim style (like LiveKit does with JSON data)
        if hasattr(record, 'extra_data') and record.extra_data:
            formatted_msg += f" {self._dim}{record.extra_data}{self._reset}"
        
        return formatted_msg


class AdvAgencyLogger:
    """
    Centralized logger factory for AdvAgency voice agent application.
    
    All loggers use the 'advagency.voiceagent' namespace hierarchy.
    """
    
    BASE_NAMESPACE = "advagency.voiceagent"
    _configured = False
    _loggers: Dict[str, logging.Logger] = {}
    
    @classmethod
    def configure(cls, level: str = "INFO", enable_console: bool = True, enable_file: bool = True):
        """
        Configure the AdvAgency logging system.
        
        Args:
            level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            enable_console: Whether to enable console output (usually False when LiveKit handles console)
            enable_file: Whether to enable file logging
        """
        if cls._configured:
            return
            
        # Convert string level to logging constant
        log_level = getattr(logging, level.upper(), logging.INFO)
        
        # Configure the base logger
        base_logger = logging.getLogger(cls.BASE_NAMESPACE)
        base_logger.setLevel(log_level)
        
        # Remove any existing handlers to avoid duplicates
        for handler in base_logger.handlers[:]:
            base_logger.removeHandler(handler)
        
        # Add console handler if requested (usually disabled when LiveKit handles console)
        if enable_console:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(AdvAgencyFormatter())
            console_handler.setLevel(log_level)
            base_logger.addHandler(console_handler)
        
        # Add file handler if requested
        if enable_file:
            file_handler = logging.FileHandler('app.log')
            file_handler.setFormatter(logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            ))
            file_handler.setLevel(log_level)
            base_logger.addHandler(file_handler)
        
        # Allow propagation to root logger so OpenTelemetry can capture logs for SigNoz
        # This ensures AdvAgency logs reach the OpenTelemetry LoggingHandler
        base_logger.propagate = True
        
        cls._configured = True
    
    @classmethod
    def get_logger(cls, name: str) -> logging.Logger:
        """
        Get a logger with the AdvAgency namespace.
        
        Args:
            name: Logger name (will be prefixed with advagency.voiceagent)
            
        Returns:
            Logger instance with proper namespace
        """
        # Ensure configuration is done
        if not cls._configured:
            cls.configure()
        
        # Create full logger name
        if name.startswith(cls.BASE_NAMESPACE):
            full_name = name
        else:
            # Remove common prefixes and create clean hierarchy
            clean_name = name.replace('src.', '').replace('__main__', 'main')
            full_name = f"{cls.BASE_NAMESPACE}.{clean_name}"
        
        # Return cached logger or create new one
        if full_name not in cls._loggers:
            logger = logging.getLogger(full_name)
            # Inherit configuration from base logger
            logger.propagate = True  # Propagate to base AdvAgency logger
            cls._loggers[full_name] = logger
        
        return cls._loggers[full_name]
    
    @classmethod
    def get_module_logger(cls, module_name: str) -> logging.Logger:
        """
        Get a logger for a specific module.
        
        Args:
            module_name: Usually __name__ from the calling module
            
        Returns:
            Logger instance with proper namespace
        """
        return cls.get_logger(module_name)


# Convenience function for easy import
def get_logger(name: str = None) -> logging.Logger:
    """
    Get an AdvAgency logger instance.
    
    Args:
        name: Logger name (optional, uses calling module if not provided)
        
    Returns:
        Logger instance with advagency.voiceagent namespace
    """
    if name is None:
        # Try to get the calling module name
        import inspect
        frame = inspect.currentframe().f_back
        name = frame.f_globals.get('__name__', 'unknown')
    
    return AdvAgencyLogger.get_module_logger(name)


# Pre-configured loggers for common components
def get_main_logger() -> logging.Logger:
    """Get logger for main application"""
    return AdvAgencyLogger.get_logger("main")


def get_agent_logger() -> logging.Logger:
    """Get logger for agent components"""
    return AdvAgencyLogger.get_logger("agent")


def get_conversation_logger() -> logging.Logger:
    """Get logger for conversation management"""
    return AdvAgencyLogger.get_logger("conversation")


def get_telemetry_logger() -> logging.Logger:
    """Get logger for telemetry components"""
    return AdvAgencyLogger.get_logger("telemetry")


def get_config_logger() -> logging.Logger:
    """Get logger for configuration components"""
    return AdvAgencyLogger.get_logger("config")
