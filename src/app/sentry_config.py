import functools
import logging

import sentry_sdk
from sentry_sdk.integrations.asyncio import AsyncioIntegration
from sentry_sdk.integrations.logging import LoggingIntegration


def initialize_sentry(config):
    sentry_environment = config.app.sentry_environment if config.app.sentry_environment else config.env
    sentry_sdk.init(
        dsn=config.app.sentry_dsn,
        environment=sentry_environment,
        integrations=[
            LoggingIntegration(
                level=logging.ERROR,
                event_level=logging.ERROR
            ),
            AsyncioIntegration(),
        ],
        traces_sample_rate=1.0,  # if sentry_environment != "production" else 0.1,
        profiles_sample_rate=1.0  # if sentry_environment != "production" else 0.1,
    )


def capture_errors(f):
    @functools.wraps(f)
    async def wrapper(*args, **kwargs):
        try:
            return await f(*args, **kwargs)
        except Exception as e:
            logger = logging.getLogger(f.__module__)
            logger.error(f"Exception in {f.__name__}: {e}", exc_info=True)
            sentry_sdk.capture_exception(e)
            raise

    return wrapper
