import logging
from typing import Optional, Dict, Any
from abc import ABC, abstractmethod

from app.config import get_config

_logger = logging.getLogger(__name__)
_config = get_config()

class AvatarProvider(ABC):
    """Abstract base class for avatar providers"""
    
    @abstractmethod
    async def create_session(self, config: Dict[str, Any]) -> Any:
        """Create and return an avatar session"""
        pass
    
    @abstractmethod
    async def start(self, session: Any, agent_session: Any, room: Any) -> None:
        """Start the avatar session"""
        pass
    
    @abstractmethod
    async def close(self, session: Any) -> None:
        """Close the avatar session"""
        pass
    
    @property
    @abstractmethod
    def is_available(self) -> bool:
        """Check if the provider is available"""
        pass


class AnamProvider(AvatarProvider):
    """Anam avatar provider implementation"""
    
    def __init__(self):
        self._anam = None
        self._available = False
        try:
            from livekit.plugins import anam
            self._anam = anam
            self._available = True
            _logger.debug("Anam provider initialized successfully")
        except ImportError:
            _logger.warning("Anam plugin not available. Install with: pip install 'livekit-agents[anam]'")
    
    @property
    def is_available(self) -> bool:
        return self._available
    
    async def create_session(self, config: Dict[str, Any]) -> Any:
        """Create Anam avatar session"""
        if not self.is_available:
            raise RuntimeError("Anam provider is not available")
        
        persona_name = config.get("name", "Assistant")
        avatar_id = config.get("avatarId", "default")
        
        return self._anam.AvatarSession(
            persona_config=self._anam.PersonaConfig(
                name=persona_name,
                avatarId=avatar_id
            )
        )
    
    async def start(self, session: Any, agent_session: Any, room: Any) -> None:
        """Start Anam avatar session"""
        await session.start(agent_session, room=room)
        _logger.info(f"Anam avatar session started")
    
    async def close(self, session: Any) -> None:
        """Close Anam avatar session"""
        if session:
            await session.aclose()
            _logger.info("Anam avatar session closed")


class TavusProvider(AvatarProvider):
    """Tavus avatar provider implementation"""
    
    def __init__(self):
        self._tavus = None
        self._available = False
        try:
            from livekit.plugins import tavus
            self._tavus = tavus
            self._available = True
            _logger.debug("Tavus provider initialized successfully")
        except ImportError:
            _logger.warning("Tavus plugin not available. Install with: pip install 'livekit-agents[tavus]'")
    
    @property
    def is_available(self) -> bool:
        return self._available
    
    async def create_session(self, config: Dict[str, Any]) -> Any:
        """Create Tavus avatar session"""
        if not self.is_available:
            raise RuntimeError("Tavus provider is not available")
        
        replica_id = config.get("replica_id")
        persona_id = config.get("persona_id")
        avatar_participant_name = config.get("avatar_participant_name", "Tavus-avatar-agent")
        
        if not replica_id or not persona_id:
            raise ValueError("Tavus requires both 'replica_id' and 'persona_id' in avatar config")
        
        return self._tavus.AvatarSession(
            replica_id=replica_id,
            persona_id=persona_id,
            avatar_participant_name=avatar_participant_name
        )
    
    async def start(self, session: Any, agent_session: Any, room: Any) -> None:
        """Start Tavus avatar session"""
        await session.start(agent_session, room=room)
        _logger.info(f"Tavus avatar session started")
    
    async def close(self, session: Any) -> None:
        """Close Tavus avatar session"""
        if session:
            await session.aclose()
            _logger.info("Tavus avatar session closed")


class BeyProvider(AvatarProvider):
    """Bey (Beyond Presence) avatar provider implementation"""
    
    def __init__(self):
        self._bey = None
        self._available = False
        try:
            from livekit.plugins import bey
            self._bey = bey
            self._available = True
            _logger.debug("Bey provider initialized successfully")
        except ImportError:
            _logger.warning("Bey plugin not available. Install with: pip install 'livekit-agents[bey]'")
    
    @property
    def is_available(self) -> bool:
        return self._available
    
    async def create_session(self, config: Dict[str, Any]) -> Any:
        """Create Bey avatar session"""
        if not self.is_available:
            raise RuntimeError("Bey provider is not available")
        
        avatar_id = config.get("avatar_id", "b9be11b8-89fb-4227-8f86-4a881393cbdb")  # Default avatar ID
        avatar_participant_identity = config.get("avatar_participant_identity", "bey-avatar-agent")
        avatar_participant_name = config.get("avatar_participant_name", "bey-avatar-agent")
        
        return self._bey.AvatarSession(
            avatar_id=avatar_id,
            avatar_participant_identity=avatar_participant_identity,
            avatar_participant_name=avatar_participant_name
        )
    
    async def start(self, session: Any, agent_session: Any, room: Any) -> None:
        """Start Bey avatar session"""
        await session.start(agent_session, room=room)
        _logger.info(f"Bey avatar session started")
    
    async def close(self, session: Any) -> None:
        """Close Bey avatar session"""
        if session:
            await session.aclose()
            _logger.info("Bey avatar session closed")


class AvatarFactory:
    """Factory for creating avatar providers and sessions"""
    
    _providers = {
        "anam": AnamProvider,
        "tavus": TavusProvider,
        "bey": BeyProvider
    }
    
    @classmethod
    def get_available_providers(cls) -> Dict[str, bool]:
        """Get list of available avatar providers"""
        available = {}
        for name, provider_class in cls._providers.items():
            try:
                provider = provider_class()
                available[name] = provider.is_available
            except Exception as e:
                _logger.error(f"Error checking availability for {name}: {e}")
                available[name] = False
        return available
    
    @classmethod
    def create_provider(cls, provider_name: str) -> Optional[AvatarProvider]:
        """Create an avatar provider instance"""
        provider_class = cls._providers.get(provider_name.lower())
        if not provider_class:
            raise ValueError(f"Unknown avatar provider: {provider_name}")
        
        provider = provider_class()
        if not provider.is_available:
            _logger.warning(f"Avatar provider '{provider_name}' is not available")
            return None
        
        return provider
    
    @classmethod
    async def create_avatar_session(cls, provider_name: str, config: Dict[str, Any]):
        """Create an avatar session for the specified provider"""
        provider = cls.create_provider(provider_name)
        if not provider:
            return None
        
        try:
            session = await provider.create_session(config)
            return session, provider
        except Exception as e:
            _logger.error(f"Failed to create {provider_name} avatar session: {e}")
            return None, None


def get_avatar_provider_from_config(avatar_config: Dict[str, Any]) -> str:
    """Determine avatar provider from configuration"""
    provider = avatar_config.get("provider", "anam").lower()
    
    # Validate provider is supported
    if provider not in AvatarFactory._providers:
        _logger.warning(f"Unknown avatar provider '{provider}', falling back to 'anam'")
        provider = "anam"
    
    return provider


def create_avatar_session(avatar_config: Dict[str, Any]):
    """Convenience function to create an avatar session from config"""
    if not avatar_config:
        return None, None
    
    provider_name = get_avatar_provider_from_config(avatar_config)
    return AvatarFactory.create_avatar_session(provider_name, avatar_config)