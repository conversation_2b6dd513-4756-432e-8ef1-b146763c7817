from dataclasses import dataclass
from typing import Dict, Any


@dataclass
class VADProfile:
    """
    Voice Activity Detection (VAD) profile configuration.

    Attributes:
        min_speech_duration (float): Minimum duration of speech to trigger detection (in seconds).
        min_silence_duration (float): Minimum duration of silence to end an utterance (in seconds).
        prefix_padding_duration (float): Audio padding added before and after detected speech (in seconds).
        max_buffered_speech (float): Maximum duration of continuous speech to buffer (in seconds).
        activation_threshold (float): Threshold for speech detection (0.0 to 1.0).
        sample_rate (int): Audio sample rate in Hz.
        force_cpu (bool): Whether to force CPU usage for inference.
    """
    min_speech_duration: float
    min_silence_duration: float
    prefix_padding_duration: float
    max_buffered_speech: float
    activation_threshold: float
    sample_rate: int = 16000
    force_cpu: bool = True

    def to_dict(self) -> Dict[str, Any]:
        return self.__dict__


# Default profile for general use
DEFAULT = VADProfile(
    min_speech_duration=0.3,
    min_silence_duration=0.5,
    prefix_padding_duration=0.2,
    max_buffered_speech=120.0,
    activation_threshold=0.4,
)
"""
DEFAULT: Balanced profile for general-purpose use.
- Suitable for most scenarios with average speakers in typical environments.
- Provides a good balance between responsiveness and accuracy.
"""

# Profile for more sensitive speech detection
SENSITIVE = VADProfile(
    min_speech_duration=0.2,
    min_silence_duration=0.4,
    prefix_padding_duration=0.3,
    max_buffered_speech=180.0,
    activation_threshold=0.3,
)
"""
SENSITIVE: More responsive profile for quieter environments or soft-spoken users.
- Quicker to detect speech and allows for shorter pauses.
- Useful in quiet rooms or for users who speak softly.
- May be more prone to false positives in noisy environments.
"""

# New profile: LESS_SENSITIVE
LESS_SENSITIVE = VADProfile(
    min_speech_duration=0.5,
    min_silence_duration=0.7,
    prefix_padding_duration=0.15,
    max_buffered_speech=90.0,
    activation_threshold=0.55,
)
"""
LESS_SENSITIVE: Reduced sensitivity for environments with intermittent noise or for more deliberate interactions.
- Requires longer speech duration to trigger, reducing false activations from brief noises.
- Longer silence duration helps prevent premature utterance termination during short pauses.
- Higher activation threshold ensures only clearer, more pronounced speech is detected.
- Useful in:
  1. Semi-noisy environments where brief loud noises might occur.
  2. Scenarios where you want to encourage more deliberate, clearer speech from users.
  3. Applications where occasional background conversations should be ignored.
- May miss some softer speech or quick utterances, but provides more reliable detection overall.
"""

# Profile for stricter speech detection
STRICT = VADProfile(
    min_speech_duration=0.4,
    min_silence_duration=0.6,
    prefix_padding_duration=0.1,
    max_buffered_speech=90.0,
    activation_threshold=0.5,
)
"""
STRICT: Less sensitive profile for noisy environments or to reduce false positives.
- Requires longer, clearer speech to trigger detection.
- Useful in noisy environments or when only clear, intentional speech should be captured.
- May miss some softer speech or short utterances.
"""

# Profile optimized for phone calls with latency concerns
PHONE_CALL = VADProfile(
    min_speech_duration=0.2,
    min_silence_duration=0.3,
    prefix_padding_duration=0.1,
    max_buffered_speech=40.0,
    activation_threshold=0.35,
    force_cpu=False,
    # sample_rate=16000,  # Typical for phone calls
)
"""
PHONE_CALL: Optimized for low-latency phone call scenarios.
- Quick to detect speech onset and offset for responsive turn-taking.
- Works well with lower quality audio typical in phone calls.
- Balances between quick response and accurate speech detection.
- Uses 8 kHz sample rate, standard for phone audio.
"""

# Profile for noisy environments
NOISY_ENVIRONMENT = VADProfile(
    min_speech_duration=0.5,
    min_silence_duration=0.7,
    prefix_padding_duration=0.1,
    max_buffered_speech=90.0,
    activation_threshold=0.6,
)
"""
NOISY_ENVIRONMENT: Designed for use in very noisy surroundings.
- Requires longer speech duration and higher activation threshold to filter out noise.
- Useful in crowded places, outdoors, or industrial settings.
- May miss some speech but significantly reduces false positives from background noise.
"""

# Profile for quick, back-and-forth interactions
QUICK_RESPONSE = VADProfile(
    min_speech_duration=0.1,
    min_silence_duration=0.2,
    prefix_padding_duration=0.05,
    max_buffered_speech=30.0,
    activation_threshold=0.3,
)
"""
QUICK_RESPONSE: For rapid, short-utterance interactions.
- Very quick to detect speech and end utterances.
- Ideal for command-based systems or quick question-answering.
- May lead to more false positives and utterance fragmentation in normal conversations.
"""

# Profile for long-form speech or dictation
LONG_FORM = VADProfile(
    min_speech_duration=0.3,
    min_silence_duration=1.0,
    prefix_padding_duration=0.5,
    max_buffered_speech=300.0,
    activation_threshold=0.4,
)
"""
LONG_FORM: Suited for extended monologues or dictation.
- Allows for longer pauses within speech without ending the utterance.
- Provides more padding to catch soft speech at beginnings and ends.
- Supports very long continuous speech (up to 5 minutes).
- Useful for dictation, lectures, or detailed explanations.
"""

# Dictionary of all available profiles
PROFILES = {
    "default": DEFAULT,
    "sensitive": SENSITIVE,
    "strict": STRICT,
    "phone_call": PHONE_CALL,
    "noisy_environment": NOISY_ENVIRONMENT,
    "quick_response": QUICK_RESPONSE,
    "long_form": LONG_FORM,
}


# Function to get a profile by name
def get_vad_profile(name: str = "DEFAULT") -> VADProfile:
    profile = PROFILES.get(name.lower())
    if profile is None:
        raise ValueError(f"Unknown VAD profile: {name}")
    return profile


def list_profiles() -> list[str]:
    return list(PROFILES.keys())


if __name__ == "__main__":
    print("Available VAD profiles:")
    for name in list_profiles():
        print(f"- {name}")

    print("\nExample of retrieving a profile:")
    phone_profile = get_vad_profile("phone_call")
