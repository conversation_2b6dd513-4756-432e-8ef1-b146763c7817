from typing import List, <PERSON><PERSON>


class LanguageSettings:
    language_codes_to_names = {
        'eng': 'English',
        'ara': 'Arabic',
        'tha': 'Thai',
        'rus': 'Russian',
        'ukr': 'Ukrainian'
    }

    language_codes_to_locale = {
        'eng': 'en-US',
        'ara': 'ar-EG',
        'tha': 'th',
        'rus': 'ru',
        'ukr': 'uk'
    }

    def __init__(self, languages: str = None):
        self.languages = self._parse_language(languages)
        self.default_language = self.languages[0] if self.languages else 'en-US'

    def map_language_code(self, lang_code: str) -> Tuple[str, str]:
        lang_code = lang_code.lower()
        language_name = self.language_codes_to_names.get(lang_code, 'English')
        locale = self.language_codes_to_locale.get(lang_code, 'en-US')
        return language_name, locale

    def _parse_language(self, language_str: str) -> List[str]:
        if not language_str:
            return ['en-US']

        import re
        languages = [lang.strip() for lang in re.split(r'[;,]', language_str) if lang.strip()]
        return languages
        # [self.language_codes_to_locale.get(lang.lower(), 'en-US') for lang in languages]

    def get_language_instructions(self) -> str:
        if len(self.languages) == 1:
            lang_name, _ = self.map_language_code(self.languages[0])
            instructions = f"\n**Language Instructions:**\n- You should respond **only** in {lang_name}."
            if lang_name == "Arabic":
                instructions += " Use UAE accent."
            return instructions

        supported_langs = [self.map_language_code(lang)[0] for lang in self.languages]
        default_lang, _ = self.map_language_code(self.languages[0])
        return f"\n**Language Instructions:**\n- You should respond in the same language as the user, supporting {', '.join(supported_langs)}.\n- Default language is {default_lang}."

    def get_locale(self, lang_code: str = None) -> str:
        if not lang_code:
            return self.default_language
        _, locale = self.map_language_code(lang_code)
        return locale
