import random
from datetime import datetime
from zoneinfo import ZoneInfo

from agents.agent_conf import AgentInfo
from agents.language_settings import LanguageSettings


class AgentSettings:
    languages = {}
    response_style: str = """
Keep replies short (max 2 sentences). Write in a human, conversational way with light emotion. Use “--” between sentences.  
Use filler words rarely (about once every 2–3 sentences). Examples: “well”, “you know”, “actually”, “hmm”.  
Punctuation rules: "." end, "," pause, "..." longer pause, "-" quick shift, "--" strong pause, "---" break, "!" excitement, "?" question, "()" aside.  
Style: contractions, simple words, some CAPS for emphasis, mix sentence lengths, add pauses for natural speech.  
"""
    tools_prompt = """
PHONE CALL ONLY. Do not mention chat. Replies must be ≤3 sentences with “--”.  
Do not repeat greetings or tool feedback.  
If user ends call or says “do not call me again” → call function and close politely.  
If user asks for a callback → suggest a clear date/time or schedule it.  
"""

    silence_phrases = {
        'en-US': [
            "Are you still there?",
            "Just let me know when you're ready.",
            "Take your time, I'm here.",
            "No rush, I'll be here when you're ready.",
            "Feel free to ask whenever you're ready.",
            "I’m here whenever you’re ready to continue.",
            "Don't worry, I’m still here.",
            "Whenever you're ready, I'm listening.",
            "I'm here if you need me.",
            "Take your time, there's no hurry.",
            "I'm ready when you are.",
            "Feel free to continue when you're ready."
        ],
        'ar-EG': [
            "هل ما زلت هناك؟",
            "أخبرني فقط عندما تكون جاهزًا.",
            "خذ وقتك، أنا هنا.",
            "لا عجلة، سأكون هنا عندما تكون جاهزًا.",
            "لا تتردد في السؤال عندما تكون مستعدًا.",
            "أنا هنا عندما تكون مستعدًا للاستمرار.",
            "لا تقلق، ما زلت هنا.",
            "عندما تكون جاهزًا، أنا أستمع.",
            "أنا هنا إذا احتجت إلي.",
            "خذ وقتك، لا عجلة.",
            "أنا جاهز عندما تكون أنت.",
            "لا تتردد في المتابعة عندما تكون مستعدًا."
        ]
    }

    welcome_phrases = {
        'en-US': [
            "Hey!",
            "Hello!",
            "Good day!",
            "How's it going?",
            "Hi there!",
            "Greetings!",
            "What's up?"
        ],
        'ar-EG': [
            "مرحبًا!",
            "أهلاً!",
            "يوم سعيد!",
            "كيف الحال؟",
            "مرحبًا بك!",
            "تحياتي!",
            "ما الأخبار؟"
        ]
    }

    ending_phrases = {
        'en-US': [
            "Ok... goodbye :-(",
            "Ok... Have a nice day!",
            "Ok... Bye-bye",
            "Take care!",
            "See you soon!",
            "Until next time."
        ],
        'ar-EG': [
            "حسنًا... وداعًا :-(",
            "حسنًا... أتمنى لك يومًا سعيدًا!",
            "حسنًا... إلى اللقاء",
            "اعتنِ بنفسك!",
            "أراك قريبًا!",
            "إلى المرة القادمة."
        ]
    }

    def __init__(self, agent_info: AgentInfo, profile_prompt=None, languages=None):
        self.config = agent_info
        self.voiceSettings = self.config.voice
        self.profile_prompt = profile_prompt
        self.lang_settings = LanguageSettings(languages)
        self.tools_prompt = self._build_tools_prompt()
        self.default_prompt = """You are helpfull assistant"""

    def _build_tools_prompt(self) -> str:
        # Start from the class-level tools_prompt (concise, llama-friendly)
        base_prompt = self.tools_prompt.strip()

        function_prompts = {
            "send_follow_up_message": """
- Confirm done briefly; don't restate phone number. If the user is busy, offer a follow-up message or a scheduled time.
""",
            "schedule_callback": """
- Suggest a clear date/time and schedule the callback.
""",
            "donot_call": """
- If the user says "do not call me again" or wants to end the call → call the function and finish politely.
"""
        }

        prompt_parts = [base_prompt]
        for function in self.config.functions:
            if function in function_prompts:
                prompt_parts.append(function_prompts[function].strip())

        return "\n".join(prompt_parts)

    def get_time_update(self, timezone):
        now = datetime.now(ZoneInfo(timezone))
        weekday = now.weekday()
        return f"Current time: {now}  Weekday: {weekday} Time zone: {timezone}"

    def get_welcome_message(self, language=None):
        locale = self.lang_settings.get_locale(language)
        phrases = self.welcome_phrases.get(locale, self.welcome_phrases['en-US'])
        return random.choice(phrases)

    def get_ending_message(self, language=None):
        locale = self.lang_settings.get_locale(language)
        phrases = self.ending_phrases.get(locale, self.ending_phrases['en-US'])
        return random.choice(phrases)

    def get_silence_message(self, language=None) -> str:
        locale = self.lang_settings.get_locale(language)
        phrases = self.silence_phrases.get(locale, self.silence_phrases['en-US'])
        return random.choice(phrases)

    def get_system_prompt(self):
        language_instructions = self.lang_settings.get_language_instructions()
        return f'{self.profile_prompt} {self.config.mission.get_prompt(self.default_prompt)} {self.tools_prompt} {self.response_style} {language_instructions}'
