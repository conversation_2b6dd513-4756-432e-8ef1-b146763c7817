import asyncio
import logging
from multiprocessing import Event as <PERSON><PERSON><PERSON>
from threading import Event as Thread<PERSON><PERSON>
from typing import Callable, Awaitable

from livekit.agents import JobContext
from livekit.protocol.models import ParticipantInfo
from livekit.rtc import RemoteAudioTrack

from conv.features.speech_filter_stt import VoiceActivityGate, GateConfig
from log.sentry_decorators import sentry_span
from log.advagency_logger import get_logger

_logger = get_logger(__name__)

def create_event():
    try:
        return ProcessEvent()
    except ImportError:
        return ThreadEvent()


class ParticipantAwaiter:
    def __init__(self,
                 ctx: JobContext,
                 end_call_callback: Callable[[], Awaitable[None]]):
        self.ctx = ctx
        self.end_call_callback = end_call_callback
        self.participant_is_joined = create_event()

    @property
    def participant_is_joined_event(self):
        """Provide access to the participant_is_joined event for backward compatibility."""
        return self.participant_is_joined

    def _has_subscribed_audio_tracks(self, participant) -> bool:
        """
        Check if participant has any subscribed audio tracks.

        Args:
            participant: The participant object to check

        Returns:
            bool: True if participant has subscribed audio tracks, False otherwise
        """
        if not participant or not getattr(participant, "track_publications", None):
            return False
        # `track_publications` is a dict; iterate over its values, not the keys
        return any(
            getattr(pub, "isSubscribed", False)
            for pub in participant.track_publications.values()
            if getattr(pub, "kind", None) in ("audio", 1)
        )

    def _is_target_participant(self, participant) -> bool:
        """Check if participant matches the target criteria.

        Returns True if:
        - Participant is of kind STANDARD and identity is 'mrcp' or 'customer', OR
        - Participant is of kind SIP
        """
        if not participant:
            return False

        try:
            # Get the participant kind as a string (e.g., 'PARTICIPANT_KIND_STANDARD')
            kind_str = participant.kind
            identity = participant.identity.lower()

            # Check if participant is STANDARD kind with specific identity, or SIP kind
            is_standard = True
            is_sip = kind_str == ParticipantInfo.Kind.SIP

            return is_standard or is_sip

        except Exception as e:
            _logger.warning(f"Error checking participant: {e}")
            return False

    async def _wait_speech_on_track(self, track: RemoteAudioTrack, timeout: float = 45.0) -> bool:
        """
        Attach to the remote audio track, feed frames into a VoiceActivityGate,
        and wait until first human speech (tone/beep ignored).
        If we cannot attach a frame stream, we don't block.
        """
        gate = VoiceActivityGate(GateConfig(sample_rate=16000, min_voice_ms=300, anti_tone=True))

        # obtain a frame stream from the track (SDKs differ a bit)
        try:
            audio_stream = track.attach()  # newer agents SDKs
        except Exception:
            _logger.warning("Unable to attach audio stream to track; skipping speech wait.")
            return True  # don't block if frames aren't available

        async def _pump():
            async for frame in audio_stream:
                gate.push_frame(frame)
                if gate.triggered:
                    break

        pump_task = asyncio.create_task(_pump())
        try:
            ok = await gate.wait_first_speech(timeout=timeout)
            return ok
        finally:
            pump_task.cancel()
            try:
                await pump_task
            except Exception:
                pass

    @sentry_span(op="room", description="wait callee")
    async def wait_for_participant(self):
        """
        Wait for a participant to join the room and be fully available.
        This method handles waiting for participant tracks and monitoring call status changes.

        Returns:
            The participant object when fully available

        Raises:
            Exception: If participant is not available after timeout or call is hung up
        """
        # Create a future that will be resolved when participant is fully available
        future = asyncio.get_event_loop().create_future()

        # Wait for participant that matches our criteria
        while True:
            participant_obj = await self.ctx.wait_for_participant()
            if self._is_target_participant(participant_obj):
                break
            _logger.warning(
                f"Ignoring non-target participant: {participant_obj.identity} (kind: {getattr(participant_obj, 'metadata', 'unknown')})")
            # Wait a bit before checking again to avoid tight loop
            await asyncio.sleep(0.1)

        # track_publications is a dict {sid: RemoteTrackPublication}
        audio_tracks = [
            pub
            for pub in participant_obj.track_publications.values()
            if getattr(pub, "kind", None) in ("audio", 1)
        ]
        subscribed_audio_tracks = [
            pub for pub in audio_tracks if getattr(pub, "isSubscribed", False)
        ]
        _logger.info(f"Participant {participant_obj.identity} has {len(audio_tracks)} audio tracks, "
                     f"{len(subscribed_audio_tracks)} subscribed")

        # Check if participant already has subscribed audio tracks
        if self._has_subscribed_audio_tracks(participant_obj):
            # Already have an audio subscription → wait for real human speech first
            first_audio_pub = next(
                (p for p in audio_tracks if getattr(p, "isSubscribed", False)),
                None
            )
            if first_audio_pub and getattr(first_audio_pub, "track", None):
                _logger.info("Audio already subscribed; waiting for human speech…")
                await self._wait_speech_on_track(first_audio_pub.track)

            self.participant_is_joined.set()
            if not future.done():
                future.set_result(participant_obj)

        else:
            # Keep track_published only for logging:
            @self.ctx.room.on("track_published")
            @sentry_span(op="room.track_published", description="callee added track")
            def track_published(publication, participant):
                if not self._is_target_participant(participant):
                    return
                _logger.info(f"{participant.identity} published track: {getattr(publication, 'name', '<noname>')}")

            # Resolve only when we actually SUBSCRIBE to an audio track
            @self.ctx.room.on("track_subscribed")
            @sentry_span(op="room.track_subscribed", description="callee track subscribed")
            def track_subscribed(publication, track, participant):
                if not self._is_target_participant(participant):
                    return
                if getattr(publication, "kind", None) in ("audio", 1):
                    async def _wait_and_resolve():
                        _logger.info("Audio track subscribed; waiting for human speech…")
                        try:
                            await self._wait_speech_on_track(track)
                        except Exception as e:
                            _logger.warning(f"Speech wait failed ({e}); proceeding anyway.")
                        self.participant_is_joined.set()
                        if not future.done():
                            future.set_result(participant_obj)

                    asyncio.create_task(_wait_and_resolve())

        @sentry_span(op="participant.attributes_changed", description="callee changed state")
        @self.ctx.room.on("participant_attributes_changed")
        def participant_attributes_changed(changed_attributes, participant):
            if not self._is_target_participant(participant):
                return

            _logger.info(f"{participant.identity} attributes changed {changed_attributes}")
            call_status_changed_to = changed_attributes.get('sip.callStatus', '')

            if call_status_changed_to == 'active':
                self.participant_is_joined.set()
                _logger.info("callStatus: active")
                # Check if we can resolve the future - ensure we have subscribed audio tracks
                if participant_obj and self._has_subscribed_audio_tracks(participant_obj) and not future.done():
                    future.set_result(participant_obj)
            elif call_status_changed_to == 'hangup':
                asyncio.create_task(self.end_call_callback())
                _logger.info("callStatus: hangup. ending conversation")
                if not future.done():
                    future.set_exception(Exception("Call hungup before participant was fully available"))
            else:
                _logger.info(f"callStatus: {call_status_changed_to} (no action taken)")

        try:
            # Wait for the future to be resolved with a timeout
            await asyncio.wait_for(future, timeout=10.0)  # 10 second timeout
            participant = future.result()
            _logger.info(
                f"Participant fully available: {participant}")
            return participant
        except asyncio.TimeoutError:
            _logger.warning("Timed out waiting for participant to be fully available")
            # Return the participant anyway, even if not fully ready
            if participant_obj:
                _logger.info(f"Returning participant despite timeout: {participant_obj}")
                return participant_obj
            raise Exception("No participant available after timeout")
