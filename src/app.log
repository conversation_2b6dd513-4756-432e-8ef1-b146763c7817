2025-08-02 16:13:56,104 - langfuse - WARNING - Langfuse client is disabled. No observability data will be sent.
2025-08-02 16:14:20,030 - langfuse - WARNING - Langfuse client is disabled. No observability data will be sent.
2025-08-02 16:14:20,109 - asyncio - DEBUG - Using selector: KqueueSelector
2025-08-02 16:14:20,113 - livekit.agents - DEV - Watching /Users/<USER>/Documents/AdvAgency_main/src
2025-08-02 16:14:24,499 - langfuse - WARNING - Langfuse client is disabled. No observability data will be sent.
2025-08-02 16:14:24,554 - asyncio - DEBUG - Using selector: KqueueSelector
2025-08-02 16:14:24,563 - livekit.agents - INFO - starting worker
2025-08-02 16:14:24,563 - livekit.agents - INFO - starting inference executor
2025-08-02 16:14:24,568 - livekit.agents - INFO - initializing process
2025-08-02 16:14:28,913 - langfuse - WARNING - Lang<PERSON> client is disabled. No observability data will be sent.
2025-08-02 16:14:28,965 - livekit.agents - DEBUG - initializing inference runner
2025-08-02 16:14:28,965 - livekit.agents - DEBUG - initializing inference runner
2025-08-02 16:14:30,359 - livekit.agents - DEBUG - inference runner initialized
2025-08-02 16:14:30,359 - livekit.agents - DEBUG - inference runner initialized
2025-08-02 16:14:30,360 - asyncio - DEBUG - Using selector: KqueueSelector
2025-08-02 16:14:30,360 - livekit.agents - INFO - process initialized
2025-08-02 16:14:30,360 - asyncio - DEBUG - Using selector: KqueueSelector
2025-08-02 16:14:30,363 - livekit.agents - INFO - [1msee tracing information at http://localhost:60095/debug[0m
2025-08-02 16:14:30,529 - livekit.agents - INFO - registered worker
2025-08-02 16:15:11,411 - livekit.agents - INFO - shutting down worker
2025-08-02 16:15:11,412 - livekit.agents - INFO - process exiting
2025-08-02 16:15:11,413 - __main__ - INFO - Shutting down the application.
2025-08-02 16:15:46,442 - langfuse - WARNING - Langfuse client is disabled. No observability data will be sent.
2025-08-02 16:16:06,859 - __main__ - ERROR - Error in main application: int() argument must be a string, a bytes-like object or a real number, not 'dict'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/AdvAgency_main/src/main.py", line 274, in main
    cli.run_app(worker_options)
  File "/Users/<USER>/Documents/AdvAgency_main/.venv/lib/python3.12/site-packages/livekit/agents/cli/cli.py", line 248, in run_app
    cli()
  File "/Users/<USER>/Documents/AdvAgency_main/.venv/lib/python3.12/site-packages/click/core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/AdvAgency_main/.venv/lib/python3.12/site-packages/click/core.py", line 1078, in main
    rv = self.invoke(ctx)
         ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/AdvAgency_main/.venv/lib/python3.12/site-packages/click/core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/AdvAgency_main/.venv/lib/python3.12/site-packages/click/core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/AdvAgency_main/.venv/lib/python3.12/site-packages/click/core.py", line 783, in invoke
    return __callback(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/AdvAgency_main/.venv/lib/python3.12/site-packages/livekit/agents/cli/cli.py", line 129, in dev
    _run.run_dev(args)
  File "/Users/<USER>/Documents/AdvAgency_main/.venv/lib/python3.12/site-packages/livekit/agents/cli/_run.py", line 19, in run_dev
    from .watcher import WatchServer
  File "/Users/<USER>/Documents/AdvAgency_main/.venv/lib/python3.12/site-packages/livekit/agents/cli/watcher.py", line 13, in <module>
    import watchfiles
  File "/Users/<USER>/Documents/AdvAgency_main/.venv/lib/python3.12/site-packages/watchfiles/__init__.py", line 2, in <module>
    from .main import Change, awatch, watch
  File "/Users/<USER>/Documents/AdvAgency_main/.venv/lib/python3.12/site-packages/watchfiles/main.py", line 18, in <module>
    class Change(IntEnum):
  File "/Users/<USER>/.pyenv/versions/3.12.0/lib/python3.12/enum.py", line 583, in __new__
    enum_class = super().__new__(metacls, cls, bases, classdict, **kwds)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.0/lib/python3.12/enum.py", line 266, in __set_name__
    enum_member = enum_class._new_member_(enum_class, *args)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: int() argument must be a string, a bytes-like object or a real number, not 'dict'
Error calling __set_name__ on '_proto_member' instance '__pydevd_ret_val_dict' in 'Change'
2025-08-02 16:17:02,275 - langfuse - WARNING - Langfuse client is disabled. No observability data will be sent.
