from typing import Optional

from livekit import api
from livekit.api.egress_service import (
    RoomCompositeEgressRequest,
    StopEgressRequest,
    EgressInfo,
)

from app.config import get_config


class EgressManager:
    """
    Wraps LiveKit's EgressService API to start/stop room-composite recordings
    directly into an S3 (or MinIO) bucket.
    """

    def __init__(self):
        cfg = get_config()

        # # Control-plane URL (must be http, not ws)
        # base_url = cfg.livekit.url.replace("ws://", "http://").replace("wss://", "https://")

        # LiveKit API client
        self.lkapi = api.LiveKitAPI(cfg.livekit.url, cfg.livekit.api_key, cfg.livekit.api_secret)

        # S3 configuration
        self.s3_bucket = cfg.s3.bucket
        self.s3_region = cfg.s3.region
        self.s3_endpoint = cfg.s3.endpoint
        self.s3_access_key = cfg.s3.key_id
        self.s3_secret_key = cfg.s3.key_secret
        self.force_path_style = True  # Can be made configurable if needed

        self.egress_id: Optional[str] = None

    async def start_room_composite(
            self,
            room_name: str,
            file_prefix: str = "recordings",
            audio_only: bool = True,
    ) -> EgressInfo:
        """
        Start a RoomComposite egress (audio-only by default) and save to S3/file.
        Returns the EgressInfo containing egress_id, etc.
        """

        # Define the S3 sink
        s3 = api.S3Upload(
            bucket=self.s3_bucket,
            region=self.s3_region,
            endpoint=self.s3_endpoint or "",
            access_key=self.s3_access_key or "",
            secret=self.s3_secret_key or "",
            force_path_style=self.force_path_style
        )

        # Build the request
        req = RoomCompositeEgressRequest(
            room_name=room_name,
            audio_only=audio_only,
            file_outputs=[api.EncodedFileOutput(
                filepath=room_name,
                s3=s3
            )],
        )

        # Call the API
        info = await self.lkapi.egress.start_room_composite_egress(req)
        self.egress_id = info.egress_id
        return info

    async def stop(self, egress_id: Optional[str] = None) -> Optional[EgressInfo]:
        """
        Stop a running egress by ID (or the last one started).
        """
        if egress_id is None:
            if self.egress_id is None:
                return None
            egress_id = self.egress_id

        req = StopEgressRequest(egress_id=egress_id)
        info = await self.lkapi.egress.stop_egress(req)

        # Reset self.egress_id only if stopping the active one
        if egress_id == self.egress_id:
            self.egress_id = None

        return info
