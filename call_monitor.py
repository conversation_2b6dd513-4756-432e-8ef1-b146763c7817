import asyncio
import os
import time

# Try to import LiveKit SDK for monitoring
try:
    from livekit import api
    LIVEKIT_SDK_AVAILABLE = True
except ImportError:
    LIVEKIT_SDK_AVAILABLE = False

# Interval between status checks (seconds)
CHECK_INTERVAL = 3


class CallMonitor:
    def __init__(self, room_name, participant_id, livekit_url=None, api_key=None, api_secret=None):
        self.room_name = room_name
        self.participant_id = participant_id
        self.livekit_url = livekit_url or os.getenv('LIVEKIT_URL', 'ws://localhost:7880')
        self.api_key = api_key or os.getenv('LIVEKIT_API_KEY')
        self.api_secret = api_secret or os.getenv('LIVEKIT_API_SECRET')

        self.start_time = time.time()
        self.participants_timeline = []
        self.call_events = []
        self.metrics = {
            'monitoring_started': self.start_time,
            'first_participant_join': None,
            'last_participant_leave': None,
            'total_participants_seen': set(),
            'max_concurrent_participants': 0,
            'call_phases': [],
            'sip_events': []
        }

        self.lkapi = None
        self.is_monitoring = False

    async def start_monitoring(self, timeout_seconds=1800):
        if not LIVEKIT_SDK_AVAILABLE:
            return self._create_error_result('LiveKit SDK not available')

        if not self.api_key or not self.api_secret:
            return self._create_error_result('LiveKit API credentials not found')

        try:
            self.lkapi = api.LiveKitAPI(self.livekit_url, self.api_key, self.api_secret)
            self.is_monitoring = True

            print(f"🔍 Starting enhanced call monitoring for room: {self.room_name}")
            self._log_event('monitoring_started', {'timeout': timeout_seconds})

            result = await self._monitor_loop(timeout_seconds)
            return result

        except Exception as e:
            return self._create_error_result(f'Monitoring initialization error: {str(e)}')
        finally:
            if self.lkapi:
                await self.lkapi.aclose()
            self.is_monitoring = False

    async def _monitor_loop(self, timeout_seconds):
        elapsed = 0
        previous_participant_count = 0
        target_participant_found = False
        target_participant_connected = False
        target_connection_time = None
        agent_alone_start = None

        while elapsed < timeout_seconds and self.is_monitoring:
            try:
                current_time = time.time()

                participants_response = await self.lkapi.room.list_participants(
                    api.ListParticipantsRequest(room=self.room_name)
                )

                participant_count = len(participants_response.participants)
                elapsed_formatted = f"{elapsed}s"

                print(f"   🔍 [{elapsed_formatted}] Room {self.room_name}: {participant_count} participants")

                if participant_count == 0:
                    self._log_event('room_empty', {'elapsed': elapsed})
                    if target_participant_connected:
                        self.metrics['last_participant_leave'] = current_time
                        call_duration = current_time - target_connection_time if target_connection_time else 0
                        return self._create_disconnect_result('room_empty', 'All participants left', call_duration, target_participant_connected)
                    else:
                        return self._create_disconnect_result('no_connection', 'No participants joined', 0, False)

                if participant_count != previous_participant_count:
                    self._log_event('participant_count_changed', {
                        'from': previous_participant_count,
                        'to': participant_count,
                        'elapsed': elapsed
                    })
                    previous_participant_count = participant_count

                self.metrics['max_concurrent_participants'] = max(
                    self.metrics['max_concurrent_participants'],
                    participant_count
                )

                target_participant = self._find_target_participant(participants_response.participants)

                if target_participant and not target_participant_found:
                    target_participant_found = True
                    target_connection_time = current_time
                    self.metrics['first_participant_join'] = current_time
                    self._log_event('target_participant_joined', {
                        'participant_id': target_participant.sid,
                        'identity': target_participant.identity,
                        'elapsed': elapsed
                    })

                if target_participant:
                    agent_alone_start = None
                    self.metrics['total_participants_seen'].add(target_participant.sid)

                    sip_status = self._get_sip_status(target_participant)
                    if sip_status:
                        self._log_event('sip_status_update', {
                            'status': sip_status,
                            'elapsed': elapsed
                        })

                    if target_participant.state == api.ParticipantInfo.State.ACTIVE:
                        if not target_participant_connected:
                            target_participant_connected = True
                            self._log_event('target_participant_connected', {
                                'elapsed': elapsed,
                                'connection_delay': elapsed
                            })

                        if sip_status == 'hangup':
                            call_duration = current_time - target_connection_time if target_connection_time else 0
                            self._log_event('call_ended_hangup', {'call_duration': call_duration})
                            return self._create_disconnect_result('user_hangup', 'User hung up', call_duration, True, sip_status)

                    elif target_participant.state == api.ParticipantInfo.State.DISCONNECTED:
                        call_duration = current_time - target_connection_time if target_connection_time else 0
                        disconnect_reason = self._determine_disconnect_reason(sip_status, target_participant_connected, call_duration)
                        self._log_event('target_participant_disconnected', {
                            'reason': disconnect_reason,
                            'call_duration': call_duration,
                            'was_connected': target_participant_connected
                        })
                        return self._create_disconnect_result(disconnect_reason, f'Call ended: {disconnect_reason}', call_duration, target_participant_connected, sip_status)

                else:
                    if target_participant_found and participant_count > 0:
                        if agent_alone_start is None:
                            agent_alone_start = current_time
                            self._log_event('target_participant_left', {
                                'elapsed': elapsed,
                                'remaining_participants': participant_count
                            })

                        agent_alone_duration = current_time - agent_alone_start
                        if agent_alone_duration > 10:
                            call_duration = current_time - target_connection_time if target_connection_time else 0
                            self._log_event('agent_alone_timeout', {
                                'agent_alone_duration': agent_alone_duration,
                                'call_duration': call_duration
                            })
                            return self._create_disconnect_result('agent_alone', 'Target participant left, agent alone >10s', call_duration, target_participant_connected)

                    elif target_participant_found:
                        call_duration = current_time - target_connection_time if target_connection_time else 0
                        self._log_event('target_participant_left_immediate', {
                            'elapsed': elapsed,
                            'call_duration': call_duration
                        })
                        return self._create_disconnect_result('participant_left', 'Target participant left the room', call_duration, target_participant_connected)

                await asyncio.sleep(CHECK_INTERVAL)
                elapsed += CHECK_INTERVAL

            except Exception as e:
                self._log_event('monitoring_error', {'error': str(e), 'elapsed': elapsed})
                await asyncio.sleep(CHECK_INTERVAL)
                elapsed += CHECK_INTERVAL
                continue

        return self._create_timeout_result(elapsed)

    def _find_target_participant(self, participants):
        for participant in participants:
            if participant.sid == self.participant_id:
                return participant
        return None

    def _get_sip_status(self, participant):
        if hasattr(participant, 'attributes') and participant.attributes:
            return participant.attributes.get('sip.callStatus')
        return None

    def _determine_disconnect_reason(self, sip_status, was_connected, call_duration):
        if sip_status == 'hangup':
            return 'user_hangup'
        elif not was_connected:
            return 'no_answer'
        elif call_duration < 2:
            return 'call_rejected'
        elif call_duration < 10:
            return 'quick_hangup'
        else:
            return 'normal_completion'

    def _determine_business_call_status(self, disconnect_reason, was_connected, call_duration, sip_status=None):
        """
        Determine clear business call status based on SIP events in call timeline
        
        Returns:
            str: Business call status (ANSWERED, NOT_ANSWERED, HANGUP, BUSY, etc.)
        """
        # Analyze SIP status events to determine call outcome
        sip_statuses = []
        has_hangup = False
        
        for event in self.call_events:
            if event['event'] == 'sip_status_update':
                status = event['data'].get('status', '')
                sip_statuses.append(status)
                if status == 'hangup':
                    has_hangup = True
            elif event['event'] == 'call_ended_hangup':
                has_hangup = True
        
        # Check for explicit hangup (from SIP status or call_ended_hangup event)
        if has_hangup or disconnect_reason == 'user_hangup':
            return 'HANGUP'
        
        # Check if call was ever active (answered)
        if 'active' in sip_statuses:
            return 'ANSWERED'
        
        # If only ringing status seen, call was not answered
        if sip_statuses and all(status == 'ringing' for status in sip_statuses):
            return 'NOT_ANSWERED'
        
        # Call was never connected
        if not was_connected:
            if disconnect_reason == 'no_answer' or disconnect_reason == 'no_connection':
                return 'NOT_ANSWERED'
            elif disconnect_reason == 'call_rejected':
                return 'BUSY'
            else:
                return 'FAILED'
        
        # Default fallback for connected calls
        if was_connected and call_duration > 0:
            return 'ANSWERED'
        
        return 'FAILED'

    def _log_event(self, event_type, data):
        event = {
            'timestamp': time.time(),
            'elapsed': time.time() - self.start_time,
            'event': event_type,
            'data': data
        }
        self.call_events.append(event)

    def _create_disconnect_result(self, disconnect_reason, reason_text, call_duration, was_connected, sip_status=None):
        business_status = self._determine_business_call_status(disconnect_reason, was_connected, call_duration, sip_status)
        return {
            'status': 'disconnected',
            'reason': reason_text,
            'connected': was_connected,
            'monitoring_duration': time.time() - self.start_time,
            'call_duration': call_duration,
            'disconnect_reason': disconnect_reason,
            'business_call_status': business_status,
            'sip_status': sip_status,
            'metrics': self._compile_metrics(),
            'events': self.call_events,
            'participant_count': 0
        }

    def _create_timeout_result(self, elapsed):
        return {
            'status': 'timeout',
            'reason': f'Monitoring timeout after {elapsed} seconds',
            'connected': False,
            'monitoring_duration': elapsed,
            'call_duration': 0,
            'metrics': self._compile_metrics(),
            'events': self.call_events
        }

    def _create_error_result(self, error_message):
        return {
            'status': 'error',
            'reason': error_message,
            'connected': False,
            'monitoring_duration': 0,
            'call_duration': 0,
            'metrics': self._compile_metrics(),
            'events': self.call_events
        }

    def _compile_metrics(self):
        current_time = time.time()
        metrics = self.metrics.copy()

        metrics['total_participants_seen'] = len(metrics['total_participants_seen'])
        metrics['monitoring_duration'] = current_time - self.start_time

        if metrics['first_participant_join']:
            metrics['time_to_first_join'] = metrics['first_participant_join'] - self.start_time

        if metrics['last_participant_leave'] and metrics['first_participant_join']:
            metrics['actual_call_duration'] = metrics['last_participant_leave'] - metrics['first_participant_join']

        return metrics

    def get_detailed_report(self):
        return {
            'room_name': self.room_name,
            'participant_id': self.participant_id,
            'metrics': self._compile_metrics(),
            'events': self.call_events,
            'timeline': self._generate_timeline()
        }

    def _generate_timeline(self):
        timeline = []
        for event in self.call_events:
            timeline.append({
                'time': f"{event['elapsed']:.1f}s",
                'event': event['event'],
                'description': self._format_event_description(event)
            })
        return timeline

    def _format_event_description(self, event):
        event_type = event['event']
        data = event['data']

        descriptions = {
            'monitoring_started': f"Started monitoring with {data.get('timeout', 'unknown')}s timeout",
            'participant_count_changed': f"Participants: {data.get('from', 0)} → {data.get('to', 0)}",
            'target_participant_joined': f"Target participant joined: {data.get('identity', 'unknown')}",
            'target_participant_connected': f"Participant connected (delay: {data.get('connection_delay', 0)}s)",
            'sip_status_update': f"SIP status: {data.get('status', 'unknown')}",
            'call_ended_hangup': f"Call ended by hangup (duration: {data.get('call_duration', 0):.1f}s)",
            'target_participant_left': f"Target participant left, {data.get('remaining_participants', 0)} remain",
            'target_participant_left_immediate': f"Target participant left (call duration: {data.get('call_duration', 0):.1f}s)",
            'agent_alone_timeout': f"Agent alone for {data.get('agent_alone_duration', 0):.1f}s, ending call",
            'room_empty': "Room became empty",
            'monitoring_error': f"Error: {data.get('error', 'unknown')}"
        }

        return descriptions.get(event_type, f"{event_type}: {data}")
