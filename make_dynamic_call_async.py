#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to make dynamic outbound calls via LiveKit to any phone number
Supports single calls and CSV batches with bounded parallelism (max 4).
Now with per-call colored logging & banners to separate output visually.
"""

import asyncio
import csv
import json
import os
import shutil
import subprocess
import sys
import tempfile
from datetime import datetime, timezone
from pathlib import Path
from typing import Optional

from call_monitor import CallMonitor

# Try to import LiveKit SDK for monitoring
try:
    from livekit import api

    LIVEKIT_SDK_AVAILABLE = True
except ImportError:
    LIVEKIT_SDK_AVAILABLE = False

# Try to load environment variables from .env file
try:
    from dotenv import load_dotenv

    load_dotenv()
    print("🔧 Environment variables loaded from .env file")
except ImportError:
    print("⚠️  python-dotenv not available, using system environment variables")

# ---- Config -----------------------------------------------------------------

CHECK_INTERVAL = 3
RESULTS_DIR = "call_results"

# Recording configuration
REMOTE_HOST = "<EMAIL>"
REMOTE_RECORDINGS_DIR = "/opt/freeswitch-recordings"
LOCAL_RECORDINGS_DIR = "./recordings"

# Concurrency (hard cap 4 unless overridden by env)
MAX_PARALLEL_CALLS = int(os.getenv("MAX_PARALLEL_CALLS", "4"))
# MAX_PARALLEL_CALLS = 1

# ANSI color control
NO_COLOR = os.getenv("NO_COLOR", "").strip() != ""

# ------------------------- Pretty per-call logger ----------------------------

RESET = "\033[0m" if not NO_COLOR else ""
BOLD = "\033[1m" if not NO_COLOR else ""
DIM = "\033[2m" if not NO_COLOR else ""

PALETTE = [
    "\033[38;5;39m",  # blue
    "\033[38;5;208m",  # orange
    "\033[38;5;35m",  # green
    "\033[38;5;199m",  # magenta
    "\033[38;5;45m",  # cyan
    "\033[38;5;220m",  # yellow
    "\033[38;5;141m",  # violet
    "\033[38;5;166m",  # dark orange
] if not NO_COLOR else [""] * 8


def _pick_color(tag: str) -> str:
    return PALETTE[hash(tag) % len(PALETTE)]


class CallLogger:
    """
    Simple per-call logger that prints colored, prefixed lines + start/end banners.
    """

    def __init__(self, tag: str):
        self.tag = tag.strip() or "call"
        self.color = _pick_color(self.tag)

    def set_tag(self, new_tag: str):
        self.tag = new_tag.strip() or self.tag
        self.color = _pick_color(self.tag)

    def _ts(self) -> str:
        return datetime.now().strftime("%H:%M:%S")

    def _prefix(self) -> str:
        return f"{self.color}[{self.tag}]{RESET}"

    def _line(self, level: str, msg: str) -> str:
        return f"{DIM}{self._ts()}{RESET} {self._prefix()} {BOLD}{level:<5}{RESET} {msg}"

    def info(self, msg: str):
        print(self._line("INFO", msg))

    def warn(self, msg: str):
        print(self._line("WARN", msg))

    def error(self, msg: str):
        print(self._line("ERR", msg))

    def debug(self, msg: str):
        print(self._line("DBG", msg))

    def banner_start(self, title="CALL START"):
        sep = "═" * 40
        print(f"{self.color}{sep} {BOLD}{title}{RESET} {self.color}{sep}{RESET}")
        print(self._line("INFO", "Starting pipeline"))

    def banner_end(self, title="CALL END"):
        print(self._line("INFO", "Pipeline finished"))
        sep = "═" * 40
        print(f"{self.color}{sep} {BOLD}{title}{RESET} {self.color}{sep}{RESET}")


def _g(msg: str):
    """Global (batch-level) log."""
    ts = datetime.now().strftime("%H:%M:%S")
    prefix = f"{DIM}{ts}{RESET} {BOLD}[batch]{RESET}"
    print(f"{prefix} {msg}")


# -----------------------------------------------------------------------------

async def create_livekit_room(room_name, logger: Optional[CallLogger] = None):
    """
    Create a new LiveKit room.
    """
    livekit_api = api.LiveKitAPI(
        os.getenv("LIVEKIT_URL"),
        os.getenv("LIVEKIT_API_KEY"),
        os.getenv("LIVEKIT_API_SECRET"),
    )
    try:
        room = await livekit_api.room.create_room(api.CreateRoomRequest(name=room_name))
        (logger.info if logger else print)(f"✅ Room '{room_name}' created successfully.")
        return room
    finally:
        await livekit_api.aclose()


async def download_call_recording(phone_number, room_name, target_dir=None, timeout_seconds=30,
                                  logger: Optional[CallLogger] = None):
    """
    Download call recording for a specific phone number and move it to target directory.
    """
    L = logger.info if logger else print
    W = logger.warn if logger else print
    E = logger.error if logger else print

    try:
        expected_filename = f"{phone_number}.wav"
        L(f"🎵 Looking for recording: {expected_filename}")

        recordings_path = Path(LOCAL_RECORDINGS_DIR)
        recordings_path.mkdir(exist_ok=True)

        results_path = target_dir if target_dir else Path(RESULTS_DIR)
        results_path.mkdir(parents=True, exist_ok=True)

        max_attempts = 3
        for attempt in range(max_attempts):
            try:
                L(f"📥 Downloading recordings from remote host (attempt {attempt + 1}/{max_attempts})...")
                result = subprocess.run(
                    ["rsync", "-avz", "--progress",
                     f"{REMOTE_HOST}:{REMOTE_RECORDINGS_DIR}/",
                     f"{LOCAL_RECORDINGS_DIR}/"],
                    capture_output=True, text=True, timeout=30
                )

                if result.returncode != 0:
                    W(f"rsync failed: {result.stderr}")
                    if attempt < max_attempts - 1:
                        await asyncio.sleep(5)
                    continue

                recording_file = recordings_path / expected_filename
                if recording_file.exists():
                    final_recording_path = results_path / f"recording_{room_name}.wav"
                    shutil.copy2(recording_file, final_recording_path)
                    L(f"✅ Recording downloaded and saved: {final_recording_path}")
                    return str(final_recording_path)

                if attempt < max_attempts - 1:
                    L("⏳ Recording not ready yet, waiting 5 seconds...")
                    await asyncio.sleep(5)

            except subprocess.TimeoutExpired:
                W("rsync timeout, retrying...")
                if attempt < max_attempts - 1:
                    await asyncio.sleep(2)
                continue
            except Exception as e:
                W(f"Error during recording download: {e}")
                if attempt < max_attempts - 1:
                    await asyncio.sleep(5)
                continue

        W(f"Recording not found after {max_attempts} attempts")
        return None

    except Exception as e:
        E(f"Failed to download recording: {e}")
        return None


async def make_outbound_call(phone_number, room_name=None, participant_name=None, agent_identity="agent", metadata=None,
                             logger: Optional[CallLogger] = None):
    """
    Create room, set metadata, dispatch agent, then add SIP participant via `lk` CLI.
    """
    L = logger.info if logger else print
    W = logger.warn if logger else print

    if not phone_number.startswith('+'):
        phone_number = "+" + phone_number

    if not room_name:
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H")
        room_name = f"call-{phone_number}-{timestamp}"

    if not participant_name:
        participant_name = f"Caller to {phone_number}"

    # 1) Create a room
    await create_livekit_room(room_name, logger=logger)

    # 2) Prepare and set room metadata
    call_metadata = {
        "context": {
            "participant": {
                "phone": phone_number,
                "name": participant_name,
                "email": "<EMAIL>",
                "country": "New Zealand",
                "timestamp": datetime.now().isoformat(),
            }
        }
    }

    L(f"metadata: {metadata}")

    livekit_api = api.LiveKitAPI(
        os.getenv("LIVEKIT_URL"),
        os.getenv("LIVEKIT_API_KEY"),
        os.getenv("LIVEKIT_API_SECRET"),
    )
    try:
        await livekit_api.room.update_room_metadata(
            api.UpdateRoomMetadataRequest(room=room_name, metadata=json.dumps(call_metadata, ensure_ascii=False))
        )
        L(f"✅ Room metadata set for '{room_name}'")
    except Exception as e:
        W(f"Warning: Failed to set room metadata: {e}")
    finally:
        await livekit_api.aclose()

    # 3) Dispatch an agent
    L(f"🚀 Dispatching agent '{agent_identity}' to room '{room_name}'...")
    livekit_api = api.LiveKitAPI(
        os.getenv("LIVEKIT_URL"),
        os.getenv("LIVEKIT_API_KEY"),
        os.getenv("LIVEKIT_API_SECRET"),
    )
    try:
        dispatch = await livekit_api.agent_dispatch.create_dispatch(
            api.CreateAgentDispatchRequest(
                agent_name=agent_identity,
                room=room_name,
                metadata=json.dumps(call_metadata, ensure_ascii=False),
            )
        )
        L(f"✅ Agent dispatch created. Dispatch ID: {dispatch.id}")
        await asyncio.sleep(10)  # allow agent to join
    finally:
        await livekit_api.aclose()

    # 4) Add remote participant through `lk` CLI
    L(f"📞 Adding remote participant {phone_number} to the room...")
    participant_config = {
        "sip_trunk_id": "ST_EzgYeBNxFiPd",
        "sip_call_to": phone_number,
        "room_name": room_name,
        "participant_identity": f"caller-{phone_number.replace('+', '').replace(' ', '')}",
        "participant_name": participant_name,
    }

    with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False) as temp_file:
        json.dump(participant_config, temp_file, indent=2)
        temp_file_path = temp_file.name

    try:
        result = subprocess.run(
            ["lk", "sip", "participant", "create", temp_file_path],
            capture_output=True, text=True, check=True
        )

        output_lines = result.stdout.strip().split("\n")
        call_details = {}
        for line in output_lines:
            if line.startswith("SIPCallID:"):
                call_details["call_id"] = line.split(": ")[1]
            elif line.startswith("ParticipantID:"):
                call_details["participant_id"] = line.split(": ")[1]

        call_details.update({
            "room_name": room_name,
            "phone_number": phone_number,
            "participant_name": participant_name,
            "timestamp": datetime.now().isoformat(),
            "config_used": participant_config,
        })

        L(f"✅ SIP participant added: call_id={call_details.get('call_id', '')} participant_id={call_details.get('participant_id', '')}")
        return call_details

    except subprocess.CalledProcessError as e:
        raise RuntimeError(f"Failed to create SIP participant: {e.stderr}")
    finally:
        os.unlink(temp_file_path)


async def monitor_call_status(room_name, participant_id, timeout_seconds=1800, logger: Optional[CallLogger] = None):
    """
    Monitor call status using enhanced CallMonitor class.
    """
    L = logger.info if logger else print
    monitor = CallMonitor(room_name, participant_id)
    result = await monitor.start_monitoring(timeout_seconds)

    # Optional: console timeline
    if result.get("events"):
        L(f"📊 Call Timeline for {room_name}:")
        timeline = monitor._generate_timeline()
        for entry in timeline:
            L(f"   {entry['time']}: {entry['description']}")

    metrics = result.get("metrics", {})
    if metrics:
        L("📈 Call Metrics:")
        if metrics.get("time_to_first_join"):
            L(f"   ⏱️  Time to first join: {metrics['time_to_first_join']:.1f}s")
        if metrics.get("actual_call_duration"):
            L(f"   📞 Actual call duration: {metrics['actual_call_duration']:.1f}s")
        if metrics.get("max_concurrent_participants"):
            L(f"   👥 Max participants: {metrics['max_concurrent_participants']}")
        L(f"   🔍 Total monitoring time: {metrics.get('monitoring_duration', 0):.1f}s")

    return result


def export_results_to_csv(results, csv_filename):
    """
    Export call results to CSV (legacy single results dir).
    """
    try:
        results_path = Path(RESULTS_DIR)
        results_path.mkdir(exist_ok=True)
        csv_output_path = results_path / csv_filename

        with open(csv_output_path, "w", newline="", encoding="utf-8") as csvfile:
            fieldnames = [
                "row", "name", "phone", "call_status", "call_id",
                "participant_id", "room_name", "connected", "duration",
                "call_duration", "disconnect_reason", "sip_status", "error_reason", "timestamp",
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for call in results.get("successful_calls", []):
                call_details = call["call_details"]
                status_info = call_details.get("status_info", {})
                business_status = status_info.get("business_call_status", "UNKNOWN")

                writer.writerow({
                    "row": call["row"],
                    "name": call["name"],
                    "phone": call_details["phone_number"],
                    "call_status": business_status,
                    "call_id": call_details.get("call_id", ""),
                    "participant_id": call_details.get("participant_id", ""),
                    "room_name": call_details.get("room_name", ""),
                    "connected": status_info.get("connected", False),
                    "duration": status_info.get("duration", 0),
                    "call_duration": status_info.get("call_duration", 0),
                    "disconnect_reason": status_info.get("disconnect_reason", ""),
                    "sip_status": status_info.get("sip_status", ""),
                    "error_reason": "",
                    "timestamp": call_details.get("timestamp", ""),
                })

            for call in results.get("failed_calls", []):
                writer.writerow({
                    "row": call["row"],
                    "name": call["name"],
                    "phone": call["phone"],
                    "call_status": "failed",
                    "call_id": "",
                    "participant_id": "",
                    "room_name": "",
                    "connected": False,
                    "duration": 0,
                    "call_duration": 0,
                    "disconnect_reason": "initiation_failed",
                    "sip_status": "",
                    "error_reason": call["error"],
                    "timestamp": datetime.now().isoformat(),
                })

        _g(f"📊 Results exported to CSV: {csv_output_path}")

    except Exception as e:
        _g(f"⚠️  Failed to export CSV: {e}")


def map_call_status(status_info):
    """
    Map technical monitoring data to clear business call statuses.
    """
    if not status_info:
        return "FAILED"

    connected = status_info.get("connected", False)
    disconnect_reason = status_info.get("disconnect_reason", "")
    call_duration = status_info.get("call_duration", 0)
    sip_status = status_info.get("sip_status", "")

    if not connected:
        if disconnect_reason == "no_answer":
            return "NOT_ANSWERED"
        elif disconnect_reason == "call_rejected":
            return "BUSY"
        elif disconnect_reason == "no_connection":
            return "NOT_ANSWERED"
        else:
            return "FAILED"

    if connected:
        if call_duration < 3:
            return "IMMEDIATE_HANGUP"
        if sip_status == "hangup" or disconnect_reason == "user_hangup":
            return "HANGUP"
        if disconnect_reason in ["room_empty", "participant_left", "agent_alone"]:
            return "ANSWERED"
        if disconnect_reason == "normal_completion":
            return "ANSWERED"
        if disconnect_reason == "quick_hangup":
            return "HANGUP"
        return "ANSWERED"

    return "UNKNOWN"


def export_results_to_csv_in_batch(results, batch_folder, csv_filename):
    """
    Export call results to CSV in the batch folder.
    """
    try:
        csv_output_path = batch_folder / csv_filename

        with open(csv_output_path, "w", newline="", encoding="utf-8") as csvfile:
            fieldnames = [
                "row", "name", "phone", "call_status", "room_name",
                "connected", "monitoring_duration", "call_duration",
                "disconnect_reason", "sip_status", "error_reason", "timestamp",
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for call in results.get("successful_calls", []):
                call_details = call["call_details"]
                status_info = call_details.get("status_info", {})
                business_status = status_info.get("business_call_status", "UNKNOWN")

                writer.writerow({
                    "row": call["row"],
                    "name": call["name"],
                    "phone": call_details["phone_number"],
                    "call_status": business_status,
                    "room_name": call_details.get("room_name", ""),
                    "connected": status_info.get("connected", False),
                    "monitoring_duration": status_info.get("monitoring_duration", 0),
                    "call_duration": status_info.get("call_duration", 0),
                    "disconnect_reason": status_info.get("disconnect_reason", ""),
                    "sip_status": status_info.get("sip_status", ""),
                    "error_reason": "",
                    "timestamp": call_details.get("timestamp", ""),
                })

            for call in results.get("failed_calls", []):
                writer.writerow({
                    "row": call["row"],
                    "name": call["name"],
                    "phone": call["phone"],
                    "call_status": "failed",
                    "room_name": "",
                    "connected": False,
                    "monitoring_duration": 0,
                    "call_duration": 0,
                    "disconnect_reason": "initiation_failed",
                    "sip_status": "",
                    "error_reason": call["error"],
                    "timestamp": datetime.now().isoformat(),
                })

        _g(f"📊 Results exported to CSV: {csv_output_path}")

    except Exception as e:
        _g(f"⚠️  Failed to export CSV: {e}")


# ---------------------- CONCURRENT CSV RUNNER (max 4) ------------------------

async def _process_row_concurrent(
        row_num,
        row,
        batch_info,
        monitor_calls: bool,
        semaphore: asyncio.Semaphore,
):
    """
    One complete call pipeline for a CSV row, guarded by a semaphore.
    """
    # build a preliminary tag from row info
    base_tag = f"#{row_num}"
    if len(row) >= 2 and row[1].strip():
        base_tag += f" {row[1].strip()}"
    logger = CallLogger(base_tag)

    async with semaphore:
        name = None
        phone = None
        try:
            logger.banner_start("CALL START")

            if len(row) < 2:
                logger.error("Invalid row (not enough columns)")
                logger.banner_end("CALL END")
                return {"status": "failed",
                        "payload": {"row": row_num, "name": "", "phone": "", "error": "invalid_row"}}

            name = row[0].strip()
            phone = row[1].strip()
            logger.info(f"Parsed row → name='{name}', phone='{phone}'")

            if not phone:
                logger.error("Empty phone number")
                logger.banner_end("CALL END")
                return {"status": "failed",
                        "payload": {"row": row_num, "name": name, "phone": "", "error": "empty_phone"}}

            if not phone.startswith('+'):
                phone = '+' + phone
                logger.debug(f"Normalized phone → {phone}")

            metadata = {
                "name": name,
                "phone": phone,
                "csv_row": row_num,
                "call_timestamp": datetime.now().isoformat(),
            }
            if len(row) > 2 and row[2].strip():
                metadata["email"] = row[2].strip()
            if len(row) > 3 and row[3].strip():
                metadata["country"] = row[3].strip()

            logger.info(f"Dispatching call…")
            call_details = await make_outbound_call(
                phone_number=phone,
                participant_name=name,
                metadata=metadata,
                logger=logger,
            )

            # enrich tag with room / call ids once known
            room_name = call_details.get("room_name", "")
            call_id = call_details.get("call_id", "")
            logger.set_tag(f"#{row_num} {phone} | room={room_name} | call={call_id}")

            logger.info("Saving initial call details to batch folder")
            call_file = batch_info["batch_folder"] / f"{room_name}.json"
            with open(call_file, "w", encoding="utf-8") as f:
                json.dump(call_details, f, indent=2)

            if monitor_calls and call_details.get("participant_id") and call_details.get("room_name"):
                logger.info("Monitoring call status…")
                status_info = await monitor_call_status(
                    call_details["room_name"],
                    call_details["participant_id"],
                    timeout_seconds=1800,
                    logger=logger,
                )
                call_details["status_info"] = status_info

                if status_info.get("connected"):
                    mdur = status_info.get("monitoring_duration", 0)
                    cdur = status_info.get("call_duration", 0)
                    logger.info(f"Connected. Call={cdur:.1f}s, Monitor={mdur:.1f}s")

                    recording_path = await download_call_recording(
                        phone, call_details["room_name"], batch_info["batch_folder"], timeout_seconds=120, logger=logger
                    )
                    if recording_path:
                        call_details["recording_path"] = recording_path
                        logger.info("Recording saved")
                else:
                    logger.warn(f"Not connected: {status_info.get('status')} - {status_info.get('reason')}")

                with open(call_file, "w", encoding="utf-8") as f:
                    json.dump(call_details, f, indent=2)

            logger.banner_end("CALL END")
            return {"status": "ok", "payload": {"row": row_num, "name": name, "call_details": call_details}}

        except Exception as e:
            logger.error(f"Pipeline error: {e}")
            logger.banner_end("CALL END")
            return {"status": "failed",
                    "payload": {"row": row_num, "name": name or "", "phone": phone or "", "error": str(e)}}


async def process_csv_calls_concurrent(
        csv_filename: str,
        max_rows: int | None = None,
        max_parallel: int = MAX_PARALLEL_CALLS,
        monitor_calls: bool = True,
):
    """
    Run CSV batch with bounded concurrency. At most `max_parallel` calls run at once.
    """
    successful_calls = []
    failed_calls = []

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    base_filename = Path(csv_filename).stem
    batch_folder_name = f"batch_{timestamp}_{base_filename}"
    batch_results_path = Path(RESULTS_DIR) / batch_folder_name
    batch_results_path.mkdir(parents=True, exist_ok=True)

    _g(f"📁 Created batch folder: {batch_results_path}")

    batch_info = {
        "batch_folder": batch_results_path,
        "batch_name": batch_folder_name,
        "timestamp": timestamp,
    }

    # Read rows first
    rows = []
    with open(csv_filename, "r", encoding="utf-8") as f:
        reader = csv.reader(f, delimiter=";")
        for idx, row in enumerate(reader, start=1):
            if max_rows and idx > max_rows:
                break
            rows.append((idx, row))

    _g(f"Queued {len(rows)} rows. Running up to {max_parallel} calls in parallel.")

    sem = asyncio.Semaphore(max_parallel)

    tasks = [
        asyncio.create_task(
            _process_row_concurrent(
                row_num=idx,
                row=row,
                batch_info=batch_info,
                monitor_calls=monitor_calls,
                semaphore=sem,
            )
        )
        for idx, row in rows
    ]

    for coro in asyncio.as_completed(tasks):
        res = await coro
        if res["status"] == "ok":
            successful_calls.append(res["payload"])
        else:
            failed_calls.append(res["payload"])

    return {
        "csv_file": csv_filename,
        "processed_rows": len(rows),
        "successful_calls": successful_calls,
        "failed_calls": failed_calls,
        "timestamp": datetime.now().isoformat(),
        "batch_info": batch_info,
    }


# ---------------------- (Legacy) Sequential CSV runner -----------------------

async def process_csv_calls_async(csv_filename, max_rows=None, delay_seconds=5, monitor_calls=True):
    """
    Process calls from CSV file one by one with monitoring (sequential).
    """
    successful_calls = []
    failed_calls = []

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    base_filename = Path(csv_filename).stem
    batch_folder_name = f"batch_{timestamp}_{base_filename}"
    batch_results_path = Path(RESULTS_DIR) / batch_folder_name
    batch_results_path.mkdir(parents=True, exist_ok=True)

    _g(f"📁 Created batch folder: {batch_results_path}")

    batch_info = {
        "batch_folder": batch_results_path,
        "batch_name": batch_folder_name,
        "timestamp": timestamp,
    }

    try:
        with open(csv_filename, "r", encoding="utf-8") as csvfile:
            reader = csv.reader(csvfile, delimiter=";")

            for row_num, row in enumerate(reader, 1):
                if max_rows and row_num > max_rows:
                    break

                logger = CallLogger(f"#{row_num} {row[1].strip() if len(row) > 1 else ''}")
                logger.banner_start("CALL START")

                if len(row) < 2:
                    logger.error("Invalid format (not enough columns)")
                    logger.banner_end("CALL END")
                    continue

                name = row[0].strip()
                phone = row[1].strip()

                if not phone:
                    logger.error(f"Empty phone number for {name}")
                    logger.banner_end("CALL END")
                    continue

                if not phone.startswith('+'):
                    phone = '+' + phone

                try:
                    logger.info(f"Making call to {name} at {phone} …")

                    metadata = {
                        "name": name,
                        "phone": phone,
                        "csv_row": row_num,
                        "call_timestamp": datetime.now().isoformat(),
                    }
                    if len(row) > 2 and row[2].strip():
                        metadata["email"] = row[2].strip()
                    if len(row) > 3 and row[3].strip():
                        metadata["country"] = row[3].strip()

                    call_details = await make_outbound_call(
                        phone_number=phone,
                        participant_name=name,
                        metadata=metadata,
                        logger=logger,
                    )

                    room_name = call_details["room_name"]
                    call_id = call_details.get("call_id", "")
                    logger.set_tag(f"#{row_num} {phone} | room={room_name} | call={call_id}")

                    call_file = batch_info["batch_folder"] / f"{room_name}.json"
                    with open(call_file, "w") as f:
                        json.dump(call_details, f, indent=2)
                    logger.info(f"Call details saved to: {call_file}")

                    if monitor_calls and call_details.get("participant_id") and call_details.get("room_name"):
                        logger.info("Monitoring call status…")
                        status_info = await monitor_call_status(
                            call_details["room_name"],
                            call_details["participant_id"],
                            timeout_seconds=1800,
                            logger=logger,
                        )
                        call_details["status_info"] = status_info

                        if status_info["connected"]:
                            monitoring_duration = status_info.get("monitoring_duration", 0)
                            call_duration = status_info.get("call_duration", 0)
                            logger.info(
                                f"Connected! Call duration: {call_duration:.1f}s, Monitoring: {monitoring_duration:.1f}s")

                            recording_path = await download_call_recording(
                                phone, call_details["room_name"], batch_info["batch_folder"], timeout_seconds=120,
                                logger=logger
                            )
                            if recording_path:
                                call_details["recording_path"] = recording_path
                                logger.info("Recording saved")
                        else:
                            monitoring_duration = status_info.get("monitoring_duration", 0)
                            logger.warn(
                                f"Call status: {status_info['status']} - {status_info['reason']} (monitored: {monitoring_duration:.1f}s)")

                        with open(call_file, "w") as f:
                            json.dump(call_details, f, indent=2)

                    successful_calls.append({"row": row_num, "name": name, "call_details": call_details})

                except Exception as e:
                    failed_calls.append({"row": row_num, "name": name, "phone": phone, "error": str(e)})
                    logger.error(f"Failed to call {name}: {e}")

                logger.banner_end("CALL END")

                # Legacy pacing
                if row_num < max_rows if max_rows else True:
                    _g(f"⏳ Waiting {delay_seconds} seconds before next call…")
                    await asyncio.sleep(delay_seconds)

    except FileNotFoundError:
        raise FileNotFoundError(f"CSV file not found: {csv_filename}")
    except Exception as e:
        raise RuntimeError(f"Error processing CSV file: {e}")

    return {
        "csv_file": csv_filename,
        "processed_rows": row_num if "row_num" in locals() else 0,
        "successful_calls": successful_calls,
        "failed_calls": failed_calls,
        "timestamp": datetime.now().isoformat(),
        "batch_info": batch_info,
    }


# -----------------------------------------------------------------------------


async def main():
    """Main function for command line usage"""
    if len(sys.argv) < 2:
        print("Usage:")
        print("  Single call: python make_dynamic_call.py <phone_number> [room_name] [participant_name]")
        print("  CSV batch:   python make_dynamic_call.py --csv <csv_file> [max_rows]")
        print()
        print("Examples:")
        print("  python make_dynamic_call.py +380961717124")
        print("  python make_dynamic_call.py +1234567890 'my-call-room' 'Agent Smith'")
        print("  python make_dynamic_call.py --csv 'NZ 300.csv' 5")
        print("  python make_dynamic_call.py --csv 'NZ 300.csv'")
        sys.exit(1)

    if sys.argv[1] == "--csv":
        if len(sys.argv) < 3:
            print("❌ Error: CSV filename required")
            print("Usage: python make_dynamic_call.py --csv <csv_file> [max_rows]")
            sys.exit(1)

        csv_filename = sys.argv[2]
        max_rows = int(sys.argv[3]) if len(sys.argv) > 3 and sys.argv[3].isdigit() else None

        try:
            _g(f"🚀 Starting batch calls from {csv_filename}")
            if max_rows:
                _g(f"📊 Processing first {max_rows} rows")
            _g(f"🤹 Running up to {MAX_PARALLEL_CALLS} calls in parallel\n")

            results = await process_csv_calls_concurrent(
                csv_filename,
                max_rows=max_rows,
                max_parallel=MAX_PARALLEL_CALLS,
                monitor_calls=True,
            )

            _g(f"\n🎉 Batch processing completed!")
            _g(f"✅ Successfully initiated {len(results.get('successful_calls', []))} calls")

            batch_info = results.get("batch_info", {})
            output_csv_filename = "results_summary.csv"

            if batch_info.get("batch_folder"):
                export_results_to_csv_in_batch(results, batch_info["batch_folder"], output_csv_filename)
                _g(f"📄 Detailed results saved to {batch_info['batch_folder']}/{output_csv_filename}")
            else:
                export_results_to_csv(results, output_csv_filename)
                _g(f"📄 Detailed results saved to {RESULTS_DIR}/{output_csv_filename}")

        except Exception as e:
            _g(f"❌ Batch processing failed: {e}")
            sys.exit(1)

    else:
        # Single call mode
        phone_number = sys.argv[1]
        room_name = sys.argv[2] if len(sys.argv) > 2 else None
        participant_name = sys.argv[3] if len(sys.argv) > 3 else None

        try:
            logger = CallLogger(f"single {phone_number}")
            logger.banner_start("CALL START")

            logger.info(f"Making outbound call to {phone_number}…")
            call_details = await make_outbound_call(phone_number, room_name, participant_name, logger=logger)

            logger.info("Call initiated successfully!")
            logger.info(f"Phone Number: {call_details['phone_number']}")
            logger.info(f"Room Name: {call_details['room_name']}")
            logger.info(f"Participant: {call_details['participant_name']}")
            logger.info(f"Call ID: {call_details['call_id']}")
            logger.info(f"Participant ID: {call_details['participant_id']}")

            results_path = Path(RESULTS_DIR)
            results_path.mkdir(exist_ok=True)

            output_file = results_path / f"call_details_{call_details['call_id']}.json"
            with open(output_file, "w") as f:
                json.dump(call_details, f, indent=2)

            logger.info(f"Call details saved to: {output_file}")
            logger.info("To connect an agent to this call, run:")
            logger.info(f"python join_agent_to_room.py {call_details['room_name']} emma.json")
            logger.info("…or join via the LiveKit dashboard")

            logger.banner_end("CALL END")

        except Exception as e:
            _g(f"❌ Error: {e}")
            sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
